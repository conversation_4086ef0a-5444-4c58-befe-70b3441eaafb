name: PHPLint

on: [push]

jobs:
  phplint:

    runs-on: ubuntu-latest
    container:
      image: publishpress/dev-workspace-terminal:checklists-pro-2
      options: --privileged

    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Validate composer.json and composer.lock
      run: composer validate

    - name: Install dependencies
      run: composer install --prefer-dist --no-progress --no-suggest
      env:
        COMPOSER_TOKEN: ${{ secrets.COMPOSER_TOKEN }}

    - name: Check syntax errors
      run: ./vendor/bin/phplint
