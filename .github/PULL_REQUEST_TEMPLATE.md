<!--
  Hey, that's awesome! Thanks for your interest and for taking the time to contribute.
  The following is a set of guidelines for contributing to PublishPress Checklists Pro plugin. Use your best judgment, and feel free to propose changes to this document in a pull request. 
  
  Filling out this template is required when contributing.
  Any pull request that does not include enough information to be reviewed in a timely manner may be closed at the maintainers' discretion.
  
  Please, review the guidelines for contributing to this repository:
  
  https://github.com/publishpress/PublishPress-Checklists-Pro/blob/development/CONTRIBUTING.md
 -->

## Description
<!-- We must be able to understand the design of your change from this description. -->

## Benefits
<!-- What benefits will be realized the code changes? -->

## Possible drawbacks
<!-- What are the possible side-effects or negative impacts of the code changes? -->

## Applicable issues
<!-- Link any applicable Issues here -->

## Checklist

<!-- Put an x in the boxes that apply. You can also fill these out after creating the PR. If you're unsure about any of them, don't hesitate to ask. We're here to help! This is simply a reminder of what we are going to look for before merging your code. -->

- [ ] I have created a specific branch for this pull request before committing, starting off the current HEAD of `development` branch. 
- [ ] I'm submitting to the `development`, feature/hotfix/release branch. (Do not submit to the master branch!)
- [ ] This pull request relates to a specific problem (bug or improvement).
- [ ] I have mentioned the issue number in the pull request description text.
- [ ] All the issues mentioned in this pull request relate to the problem I'm trying to solve.
- [ ] The code I'm sending follows the [PSR-12](https://www.php-fig.org/psr/psr-12/) coding style.
