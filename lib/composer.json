{"minimum-stability": "stable", "prefer-stable": true, "config": {"vendor-dir": "vendor", "preferred-install": {"*": "dist"}, "platform-check": false, "autoloader-suffix": "PPChecklistsPro"}, "repositories": [{"type": "github", "url": "https://github.com/publishpress/PublishPress-Checklists", "only": ["publishpress/publishpress-checklists"]}], "require": {"php": ">=7.2.5", "publishpress/instance-protection": "^1.0", "publishpress/pimple-pimple": "^3.5", "publishpress/psr-container": "^2.0", "publishpress/publishpress-checklists": "dev-development", "publishpress/wordpress-banners": "^1.3", "publishpress/wordpress-edd-license": "^3.0", "publishpress/wordpress-reviews": "^1.1", "publishpress/wordpress-version-notices": "^2.1"}, "scripts": {"post-update-cmd": "rm -rf vendor/publishpress/publishpress-checklists/lib/vendor", "post-install-cmd": "@post-update-cmd"}}