=== PublishPress Checklists: Pre-Publishing Approval Checklist - Validate Post Requirements ===

Contributors: publishpress, kevin<PERSON>, ste<PERSON><PERSON><PERSON><PERSON>, ander<PERSON><PERSON><PERSON>, ojo<PERSON>ul, olatechpro
Author: PublishPress
Author URI: https://publishpress.com
Tags: approval, checklist, maximum, minimum, requirement
Requires at least: 5.5
Requires PHP: 7.2.5
Tested up to: 6.8
Stable tag: 2.20.0
License: GPLv2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html

Define checklist tasks to complete before publishing posts. Make sure your content meets your requirements.

== Description ==

[PublishPress Checklists](https://publishpress.com/checklists/) is the best plugin to make sure your content is ready to go live. Using PublishPress Checklists, you define tasks that must be completed before content is published.

Let's show you a few possible options for your posts:

* Ensure your posts have a minimum or maximum number of words.
* Require your posts have a featured image.
* Check for any broken links.
* Force authors to use a specific number of Tags or Categories.
* Require posts to be approved by a user in a specific role.
* Use OpenAI to scan your content and make sure it uses good grammar or the correct tone.

Next to every post and page, writers see a checklist box, showing the tasks they need to complete. Tasks can either be recommended or required. As authors complete each task, the red X automatically turns to a green checkmark.  

PublishPress Checklists integrates with WooCommerce, Yoast SEO, and Advanced Custom Fields. You can set requirements based on those plugins.

PublishPress Checklists also integrates with OpenAI. You can use OpenAI technology to scan your posts and check for the right tone, good spelling, or correct grammar.

## PublishPress Checklists Pro ##

> <strong>Checklists Pro</strong><br />
> This plugin is the free version of the Checklists Pro plugin that comes with all the features you need to set standards for your WordPress content. <a href="https://publishpress.com/checklists"  title="Checklists Pro">Click here to purchase the best premium WordPress checklists plugin now!</a>

## You Can Set These Checklist Requirements

Each task on your pre-publish checklist can be configured to meet your site’s needs. You can also set maximum and minimum values.

[Read the Getting Started guide for PublishPress Checklists](https://publishpress.com/knowledge-base/checklists-started/).

Here are the default tasks you can use on your checklists:

* **Title**: Maximum or minimum number of characters
* **Body text**: Maximum or minimum number of words
* **Excerpt**: Maximum or minimum number of characters
* **User approval**: Require that posts are approved by users in a specific role

* **Featured image**: Require a featured image
* **Featured image size (Pro version)**: Require a maximum and minimum height and width for featured images
* **ALT text**: Require ALT text for all images
* **Number of images in content (Pro version)**: Require a maximum and minimum number of images in the post

* **Taxonomy terms**: Maximum or minimum number of categories or tags
* **Required taxonomy terms**: Force users to add specific terms
* **Prohibited taxonomy terms**: Force users to not add specific terms

* **Internal links**: Maximum or minimum number of internal links
* **External links**: Maximum or minimum number of external links
* **Broken links**: Check that all links use a valid format
* **Permalink**: Control the characters in the URL


You can configure each requirement, depending on whether you want to require writers to complete the tasks. Here are the three options:

* Disabled
* Recommended
* Required

If you choose the “Required” option, it will be impossible to publish without completing the task.

## OpenAI Checklist Requirements

PublishPress Checklists integrates with OpenAI. You can use prompts to analyze your content. If OpenAI decides that your content doesn't meet the requirements, it will show the task as incomplete.

You can create checklist requirements like these:</p>

* "Is the content clear and easy to read?"
* "Is this content tone professional?"
* "Does this article use correct grammar?"

OpenAI will scan your content and given a detailed verdict. This is an excellent way to catch errors and improve your content before publishing.

[Click here to see how to create OpenAI requirements](https://publishpress.com/knowledge-base/custom-requirements-checklist/).

## You Can Create New Checklist Requirements

You can create new requirements for your checklists by clicking the “Add custom task” link. For example, you can require authors to get a green Yoast sign, or force them to run a spell-check before publishing.

[Click here to see how to create custom requirements](https://publishpress.com/knowledge-base/custom-requirements-checklist/).

It is also possible to create more powerful requirements using a custom plugin. We have created a sample plugin to show how to do this. The sample plugin will automatically check that your site’s authors have included a specific word in their main content. If this new requirement is enabled, it will automatically search the text of your content to make sure it contains the word you choose.

## Pro Version: Checklists for WooCommerce Products

The Pro version of PublishPress Checklists has support for WooCommerce. There are all the requirements you can choose:

* Number of characters in Excerpt
* Number of Product tags
* Number of Product categories
* Number of words
* Featured image
* Check the “Virtual” box
* Check the “Downloadable” box
* Enter a “Regular price”
* Enter a “Sale price”
* Schedule the “Sale price”
* Discount for the “Sale price”
* Enter a “SKU”
* Check the “Manage stock?” box
* Check the “Sold individually” box
* Check the “Allow backorders?” box
* Select some products for “Upsells”
* Select some products for “Cross-sells”
* Product image

[Click here to read more about WooCommerce checklists](https://publishpress.com/knowledge-base/use-woocommerce-checklist-add-publishpress/).

## Pro Version: Checklists for Advanced Custom Fields

The Pro version of PublishPress Checklists has support for the Advanced Custom Fields plugin. For example, you can require that text fields have a certain number of characters or that image fields are filled in.

[Click here to read more about ACF checklists](https://publishpress.com/knowledge-base/advance-custom-fields-tasks/).

## Pro Version: Checklists for Yoast SEO

With the PublishPress Checklists plugin, you can require that site's content meets minimum standards with the Yoast SEO plugin. If the content doesn't meet those standards, you can choose to show a warning, or prevent the post from being published. The Checklists plugin integrates with the SEO and Readability features in Yoast SEO.

[Click here to read more about Yoast SEO checklists](https://publishpress.com/knowledge-base/yoast-seo-tasks/.

## Join PublishPress and get the Pro plugins ##

The Pro versions of the PublishPress plugins are well worth your investment. The Pro versions have extra features and faster support. [Click here to join PublishPress](https://publishpress.com/pricing/).

Join PublishPress and you'll get access to these Pro plugins:

* [PublishPress Authors Pro](https://publishpress.com/authors) allows you to add multiple authors and guest authors to WordPress posts.
* [PublishPress Blocks Pro](https://publishpress.com/blocks) has everything you need to build professional websites with the WordPress block editor.
* [PublishPress Capabilities Pro](https://publishpress.com/capabilities) is the plugin to manage your WordPress user roles, permissions, and capabilities.
* [PublishPress Checklists Pro](https://publishpress.com/checklists) enables you to define tasks that must be completed before content is published.
* [PublishPress Future Pro](https://publishpress.com/future) is the plugin for scheduling changes to your posts.
* [PublishPress Permissions Pro](https://publishpress.com/permissions)  is the plugin for advanced WordPress permissions.
* [PublishPress Planner Pro](https://publishpress.com/publishpress) is the plugin for managing and scheduling WordPress content.
* [PublishPress Revisions Pro](https://publishpress.com/revisions) allows you to update your published pages with teamwork and precision.
* [PublishPress Series Pro](https://publishpress.com/series) enables you to group content together into a series

Together, these plugins are a suite of powerful publishing tools for WordPress. If you need to create a professional workflow in WordPress, with moderation, revisions, permissions and more... then you should try PublishPress.

## Bug Reports ##

Bug reports for PublishPress Checklists are welcomed in our [repository on GitHub](https://github.com/publishpress/publishpress-checklists). Please note that GitHub is not a support forum, and that issues that aren’t properly qualified as bugs will be closed.

## Follow the PublishPress team ##

Follow PublishPress on [Facebook](https://www.facebook.com/publishpress), [Twitter](https://www.twitter.com/publishpresscom) and [YouTube](https://www.youtube.com/publishpress)

== Screenshots ==

1. Create your own checklists: Next to every post and page, writers see a checklist box, showing the tasks they need to complete.
2. Custom checklist rules: In addition to the default rules, PublishPress Checklists allows you to create your own rules.
3. Configure your requirements: Each item on the checklist can be configured to meet your site’s needs. You can decide whether items are recommended, required or ignored.
4. Feedback before publishing: If the writers don’t complete all the requirements, PublishPress Checklists will show them a message explaining what they need to do.
5. WooCommerce products checklist: This feature in Checklists Pro allows you to create requirements for WooCommerce products.

== Frequently Asked Questions ==

= Can I choose the number of characters in WordPress post titles? =

With the PublishPress Checklists plugin, you can require a maximum and minimum number of characters for the Title in your WordPress content. If the Title isn't the correct length, you can choose to show a warning, or prevent the post from being published. Many sites require the title to be around 55 to 60 characters long. This is often because that is the optimal length to show in Google's search results.

* Go to Checklists > Settings.
* Find the Title option.
* You can choose from “Disabled, Recommended, or Required” options.
* You can choose “Who can ignore this task?“
* You can enter choices for “Min” and “Max”.
* If you enable this title option, it will be visible when you edit content. If your title is not the correct length, this task will be marked in red in the sidebar. If your title is the correct length, this task will be marked in green in the sidebar/

[Read about choosing the number of characters in titles](https://publishpress.com/knowledge-base/number-of-characters-in-title/)

= Can I choose the number of words in WordPress post content? =

With the PublishPress Checklists plugin, you can require a maximum and minimum number of words for the content of your WordPress posts. If the content isn't the correct length, you can choose to show a warning, or prevent the post from being published. Many WordPress sites want to specify a certain number of words for all their posts. For example, the Yoast SEO plugin recommends that you aim for around 1,000-1,500 words.

* Go to Checklists > Settings.
* You can choose from “Disabled, Recommended, or Required” options.
* You can choose “Who can ignore this task?“
* You can enter choices for “Min” and “Max”.
* If you enable the content option, it will be visible when you edit content. If your content text is not the correct length, this task will be marked in red in the sidebar. If your content text is the correct length, this task will be marked in green in the sidebar.

[Read about choosing the number of words in posts](https://publishpress.com/knowledge-base/number-of-words-in-content/)

= Can I set the maximum and minimum number of categories? =

With the PublishPress Checklists plugin, you can require a maximum and minimum number of categories for your WordPress posts. If the correct number of categories aren't added, you can choose to show a warning, or prevent the post from being published. Having the correct number of categories is important for your site's SEO.  By default, Categories are only available on WordPress Posts. However, with the "TaxoPress" plugin you can add Categories to other post types and so you will be able to use the tutorial for those post types too.

* Go to Checklists > Settings.
* You can choose from “Disabled, Recommended, or Required” options.
* You can choose “Who can ignore this task?“
* You can enter choices for “Min” and “Max”.
* If you enable the categories option, it will be visible when you edit content. If you add an incorrect number of categories, this task will be marked in red in the sidebar. If you add a correct number of categories, this task will be marked in green in the sidebar.

[Read about choosing the number of categories in posts](https://publishpress.com/knowledge-base/number-of-categories/)

= Can I set the maximum and minimum number of tags? =

With the PublishPress Checklists plugin, you can require a maximum and minimum number of tags for your WordPress posts. This feature also supports taxonomy terms from other plugins or custom post types. If the correct number of tags aren't added, you can choose to show a warning, or prevent the post from being published. Having the correct number of tags is important for your site's SEO.

* Go to Checklists > Settings. If you are using a custom post type or plugin, any available taxonomies will show as options.
* You can choose from “Disabled, Recommended, or Required” options.
* You can choose “Who can ignore this task?“
* You can enter choices for “Min” and “Max”.
* If you enable this tags option, it will be visible when you edit content. If you add an incorrect number of tags, this task will be marked in red in the sidebar. If you add a correct number of tags, this task will be marked in green in the sidebar.

[Read about choosing the number of tags in posts](https://publishpress.com/knowledge-base/number-of-tags-or-taxonomy-terms/)

= Can I decide the number of characters in WordPress excerpts? =

With the PublishPress Checklists plugin, you can require a maximum and minimum number of characters for the Excerpts in your WordPress content. If the Excerpt isn't the correct length, you can choose to show a warning, or prevent the post from being published. The Excerpt field is a very useful feature in WordPress. The Excerpt is an optional summary or description of the main content.

* Go to Checklists > Settings.
* You can choose from “Disabled, Recommended, or Required” options.
* You can choose “Who can ignore this task?“
* You can enter choices for “Min” and “Max”.
* If you enable the Excerpt option, it will be visible when you edit content. If your Excerpt text is not the correct length, this task will be marked in red in the sidebar. If your Excerpt text is the correct length, this task will be marked in green in the sidebar.

[Read about choosing the number of characters in WordPress excerpts](https://publishpress.com/knowledge-base/number-of-characters-in-excerpt/)

= Can I limit the number of internal links in WordPress posts? =

With the PublishPress Checklists plugin, you can require a maximum and minimum number of internal links in your WordPress content. If the content does not have the correct number of internal links, you can choose to show a warning, or prevent the post from being published. We define “internal links” as any "a href" link to another page on your site. Many sites want a large number of internal links because it can improve their site's search engine optimization.

* Go to Checklists > Settings.
* You can choose from “Disabled, Recommended, or Required” options.
* You can choose “Who can ignore this task?“
* You can enter choices for “Min” and “Max”.
* If you enable this internal links option, it will be visible when you edit content. If you do not have the correct number of internal links, this task will be marked in red in the sidebar. If you do have the correct number of internal links, this task will be marked in green in the sidebar.

[Read about choosing the number of internal links in WordPress excerpts](https://publishpress.com/knowledge-base/number-of-internal-links-in-content/)

= Can I limit the number of external links in WordPress posts? =

With the PublishPress Checklists plugin, you can require a maximum and minimum number of external links in your WordPress content. If the content does not have the correct number of external links, you can choose to show a warning, or prevent the post from being published. We define “external links” as any "a href" link to URL on another website. Many sites want to prevent a large number of external links because it can harm their site's search engine optimization.

* Go to Checklists > Settings.
* You can choose from “Disabled, Recommended, or Required” options.
* You can choose “Who can ignore this task?“
* You can enter choices for “Min” and “Max”.
* If you enable this external links option, it will be visible when you edit content. If you do not have the correct number of external links, this task will be marked in red in the sidebar. If you do have the correct number of external links, this task will be marked in green in the sidebar.

[Read about choosing the number of external links in WordPress excerpts](https://publishpress.com/knowledge-base/number-of-external-links-in-content/)


= Can I check that all my links are valid? =

With the PublishPress Checklists plugin, you can ensure that all the links in your content (both external and internal) use a valid link format. This feature won't check the destination URL, but it will check the link format to make sure you haven't used URLs such as htpps// or .nt.

* Go to Checklists > Settings.
* You can choose from “Disabled, Recommended, or Required” options.
* You can choose “Who can ignore this task?“
* If you enable this link validation option, it will be visible when you edit content. If you have broken links in your content, this task will be marked in red in the sidebar. If you do not have broken links, this task will be marked in green in the sidebar.

The Checklists plugin uses these regular expressions to check for broken links. You will find this code in this file: /core/Utils/HyperlinkValidator.php.

[Read about validating links](https://publishpress.com/knowledge-base/all-links-use-a-valid-format/)

= Can I check that all my images have ALT text? =

With the PublishPress Checklists plugin, you can require that all the images have ALT text in your WordPress content. If an image is missing the ALT text, you can choose to show a warning, or prevent the post from being published. ALT text (short for “alternative text”) is useful because it describes images to visitors who are unable to see them. Visually impaired users often use screen readers that can read ALT text to better understand an on-page image.

* Go to Checklists > Settings.
* You can choose from “Disabled, Recommended, or Required” options.
* You can choose “Who can ignore this task?“
* You can enter choices for “Min” and “Max”.
* If you enable the ALT text option, it will be visible when you edit content. If your images are missing ALT text, this task will be marked in red in the sidebar. If all your images have ALT text, this task will be marked in green in the sidebar.

[Read about checking images for ALT text](https://publishpress.com/knowledge-base/alt-text-for-all-images/)

= Can I require a featured image on all posts? =

With the PublishPress Checklists plugin, you can require that all your posts have a Featured image. If the image is missing, you can choose to show a warning, or prevent the post from being published. Many sites require a featured image for blog posts because an image is important for sharing on social media.

* Go to Checklists > Settings.
* You can choose from “Disabled, Recommended, or Required” options.
* You can choose “Who can ignore this task?"
* If you enable this Featured image option, it will be visible when you edit content. If your Featured image box is empty, this task will be marked in red in the sidebar. If your Featured image box has an image, this task will be marked in green in the sidebar.

[Read about requiring a featured image](https://publishpress.com/knowledge-base/featured-image/)

= Can I require all posts to be approved by an administrator? =

With this requirement, you can stop content from being published unless it has been approved by a user in a particular role. This is an excellent way to allow some users to “sign off” on content before it is published.

* Go to Checklists > Settings.
* You can choose from “Disabled, Recommended, or Required” options.
* You can choose “Who can ignore this task?“
* In the final box, you can choose which roles can approve posts. If a user is in one of the selected roles, they will see a checkbox. If the user approves the post, they can check the box and the content can be published.

[Read about requiring approval by an admin](https://publishpress.com/knowledge-base/approved-by-a-user-in-this-role/)

= Can I force all posts to be approved by Yoast SEO? =

With the PublishPress Checklists plugin, you can require that site's content meets minimum standards with the Yoast SEO plugin. If the content doesn't meet those standards, you can choose to show a warning, or prevent the post from being published. Many sites require that their content meets Yoast SEO standards. This helps ensure that their content is optimized for search engines. The Checklists plugin integrates with the SEO and Readability features in Yoast SEO.

* Go to Checklists > Settings.
* You can choose from “Disabled, Recommended, or Required” options.
* You can choose “Who can ignore this task?“
* You can enter choices for both options. Choose either “OK” (yellow/orange) or “Good” (green).
* If you enable the Yoast SEO option, they will be visible when you edit content. If you don't meet the minimum requirements, the checklist items will be shown in red.

[Read about requiring approval by Yoast SEO](https://publishpress.com/knowledge-base/yoast-seo-tasks/)

== Screenshots ==

1. Create your own checklists. Next to every post and page, writers see a checklist box, showing the tasks they need to complete. As writers complete each item, the red text automatically turns to green when it is complete.
2. Configure your requirements. Each item on the checklist can be configured to meet your site’s needs. You can decide whether items are recommended, required or ignored. You can also set maximum and minimum values.
3. Feedback before publishing. If the writers don’t complete all the requirements, PublishPress Checklists will show them a message explaining what they need to do
4. OpenAI support. PublishPress Checklists supports AI to suggest new tasks and analyze your content.
5. WooCommerce Products Checklist. This feature in Checklists Pro allows you to create requirements for WooCommerce products. You can set over 20 requirements that must be met before a product is published.
6. Custom checklist rules. In addition to the default rules, PublishPress Checklists allows you to create your own rules. Click “Add custom item” to create as many rules as you want.



== Changelog ==

The format is based on [Keep a Changelog](http://keepachangelog.com/)
and this project adheres to [Semantic Versioning](http://semver.org/).

= [2.20.0] - 5 June 2025 =
* Updated: Remove branding feature #888
* Updated: Promo for Checklists Pro in Checklists screen #881
* Added: Setting links on Plugins screen. #902
* Added: Status Filter Promo in Setting. #906
* Fixed: Allow 0 on max in link checklists. #892

= [2.19.0] - 20 May 2025 =

* Updated: Checklists panel and metabox will be hidden if there are no checklists #854
* Updated: Promo Banner for PublishPress Checklists Pro #863
* Updated: Remove "Configure" link inside the metabox #860
* Added: Promo for Pro Checklists #856
* Added: New checklists item "Featured Image has Caption" #870

= [2.18.0] - 26 March 2025 =

* Added: Support for Yoast Focus Keyword, meta description #555
* Added: Allow custom SVG icons in Tabs #849
* Added: API for adding values programmatically #635
* Fixed: Rankmath Analyzer stopped working when Checklists initializes #846

= [2.17.0] - 10 December 2024 =

* Fixed: Remove space at top of the Checklists screen #834
* Added: New tab for Yoast #815
* Added: Disable Checklists on Elementor Page Builder #504

= [2.16.0] - 20 November 2024 =

* Fixed: Links with ! are marked as invalid #261
* Fixed: Internal link does not update real time #824
* Fixed: Wrong result openAI checklists #823
* Added: "List" view to highlight blocks with issues? #783
* Added: New tab for Featured Image #814
* Added: New tab for Permalinks #812
* Added: New tab for Approval #813
* Updated: Update the readme #820


= [2.15.0] - 21 October 2024 =

* Fixed: Conflict with Rank Math SEO plugin #791
* Fixed: Promo banner update #779
* Fixed: Anchor link not passing the valid link requirement #781
* Fixed: Required and prohibited categories showing deleted terms #786
* Fixed: OpenAI checklist button not working on the second tab #782
* Fixed: Alt text for featured image not working on the first try #743
* Fixed: Plugin description update #780
* Fixed: Custom arrow position issue #778
* Fixed: Reorganized the images checklists #801
* Fixed: Updated Yoast SEO rule #809
* Fixed: Updated required and prohibited categories #808
* Fixed: Missing translation updates #780
* Updated: Translations for checklist items
* Updated: Translations for image-related checklist items
* Updated: Translations "Featured image" updated to "Featured image is added"
* Updated: Translations "Alt text for featured images" updated to "Featured image has Alt text"
* Updated: Translations "Alt text for all images" updated to "All images have Alt text"
* Updated: Translations "Readability" updated to "Yoast Readability"
* Updated: Translations "SEO" updated to "Yoast SEO"
* Updated: Changelog and translation files
* Updated: Composer dependencies

= [2.14.0] - 25 September 2024 =

* Added: Taxonomies tab #747
* Added: Minimum number of characters for alt text #616
* Added: ACF (Advanced Custom Fields) integration support #639
* Added: Option to specify the number of images in a post #729
* Fixed: Renamed field group to be compatible with ACF #774
* Fixed: Hide checklist from ACF fields #770
* Fixed: Tiny text change for new alt text #766
* Fixed: Undefined disable_publish_button issue
* Fixed: Failed save rule
* Fixed: PHP 8.2 deprecated message #752
* Fixed: Character count issue #740
* Updated: Composer dependencies
* Updated: Bumped Webpack from 5.91.0 to 5.94.0

= [2.13.0] - 28 August 2024 =

* Added: Class FieldsTabs to support modifying tabs from the Pro version #739
* Added: Option to disable the Publish button #728
* Fixed: Missing variable gutenbergLockName for locking post saving #738
* Fixed: URL requirement broken by ellipsis, colon, and other characters #301
* Fixed: Checklists design fails on smaller screens #730
* Fixed: Code scanning alerts and minor UI issues #737
* Updated: Composer dependencies
* Updated: PHPLint workflow to use the dev-workspace
* Chore: Added absolute path to dev-workspace

= [2.12.0] - 31 July 2024 =

* Added: Tabs for different types of requirements #672
* Added: Required category & tag for new rule #492
* Added: Prohibited category & tag for new rule #491
* Fixed: External link missing from checklists #710
* Fixed: String not translated in Italian #638
* Fixed: Checklists could be bypassed if scheduled #666
* Fixed: Redirect to checklists screen on new activation #669
* Updated: Composer dependencies
* Updated: Position for feature image alt
* Updated: Move banner to lib/vendor
* Updated: Full-width CSS for better layout
* Implemented: Cache mechanism for improved performance

= [2.11.1] - 18 July 2024 =

* Fixed: Fixed compatibility with wordPress 6.6 #697
* Fixed: Quick edit settings are now disabled by default #689

= [2.11.0] - 15 July 2024 =

* Added: Added a sidebar feature #562
* Added: Added an option to disable quick edit #665
* Added: Added post type validation #403
* Fixed: Issue with menu being empty on first install #552
* Fixed: Default text color for OpenAI prompts #580
* Update: Updated ES, FR, and IT translations #652

= [2.10.4] - 04 April 2024 =

* Fixed: WordPress 6.5 causes Checklists button to shift, #642
* Fixed: Incompatability with SEOPress, #636
* Fixed: Conflict with Yoast SEO, editor stops working, #631
* Update: Turkish Translation, #641


= [2.10.3] - 24 Jan 2024 =

* Fixed: Uncaught RangeError: Maximum call stack size exceeded coming from gutenberg panel, #613
* Fixed: Image alt requirement HTTP request loop, #623
* Update: New Translation ES-FR-IT Updates, #615


= [2.10.2] - 15 Jan 2024 =

* Fixed: Fix issues with saving while editing post, #598
* Update: Re-Enable "Show Warning Icon" settings, #605
* Update: Update OpenAI tasks "Check now" button styles, #604
* Update: Small text update for OpenAI response, #603
* Update: Update Checklists sidebar items spacing, #600
* Fixed: The "publishpress-checklists-panel" plugin encountered error and cannot be rendered, #594
* Update: CheckLists Translation Updates 11 January 2024, #597


= [2.10.1] - 11 Jan 2024 =

* Fixed: 2.10.0 automatically updates posts while editing, #584
* Fixed: Featured image HTTP request loop, #585
* Update: Remove Show warning icon settings, #586


= [2.10.0] - 10 Jan 2024 =

* Feature: Add AI features to Checklists requirements, #541
* Update: Add Checklists gutenberg panel, #567
* Update: Remove "Define tasks that must be complete before content is published.", #561
* Update: Checklists FREE v.2.9.1 Translation Updates ES-FR-IT, #554

= [2.9.1] - 30 Nov 2023 =

* Fixed: Checklist menu often missing on new installation, #524
* Fixed: HyperlinkValidator fails with URLs containing text fragments, #485
* Update: Block updates for posts with incomplete checklists, #303
* Fixed: Conflict with ACF custom field when creating new post, #506
* Fixed: Yoast SEO metabox error when creating new woocommerce product, #505
* Fixed: Warning: Undefined array key "HTTP_REFERER" in checklists, #411
* Update: Only disable Status in quick edit for checklists enabled post types, #536
* Fixed: Featured Image Height and Width checks fail for Authors / Contributors, #486
* Fixed: "Featured Image Size" requirement fails if you do not have the "edit_other_posts" capability, #523

= [2.9.0] - 09 Aug 2023 =

* Changed: Updated internal libraries to latest versions;
* Changed: Move dependencies to lib/vendor;
* Changed: Internal dependencies moved from `vendor` to `lib/vendor`;
* Changed: Updated internal libraries to the latest versions;
* Changed: Removed the `vendor-locator-checklists` library. Internal vendor is now on a fixed path, `lib/vendor`;
* Changed: Deprecated constant `PUBLISHPRESS_CHECKLISTS_VENDOR_PATH` in favor of `PPCH_LIB_VENDOR_PATH`;
* Fixed: Fix compatibility with Composer-based installations, using prefixed libraries;

= [2.8.0] - 18 May 2023 =

* Changed: Replaced Pimple library with a prefixed version of the library to avoid conflicts with other plugins;
* Changed: Replaced Psr/Container library with a prefixed version of the library to avoid conflicts with other plugins;
* Changed: Change min PHP version to 7.2.5. If not compatible, the plugin will not execute;
* Changed: Change min WP version to 5.5. If not compatible, the plugin will not execute;
* Changed: Updated internal libraries to latest versions;

= [2.7.4] - 06 Mar 2023 =

* Fixed: Image alt tag function not working in Classic Editor, #471
* Fixed: Gutenberg Editor error when using Taxonomy Categories, #407
* Fixed: Internal link checker not working in Classic Editor, #278
* Fixed: Rank Math meta field missing when checklists is enabled, #470
* Fixed: Support for Rank Math and Classic Editor, #293
* Update: Use wp_kses_post filter instead esc_html to enable allowed tags in metabox label, #478
* Fixed: Submit Lock Affecting Content Update with Yoast, #423
* Update: German translation, #262
* Update: PRO_Checklists_ES-FR-IT_TranslationUpdate_October2022, #415
* Update: TRANSLATION UPDATES French-Spansh-Italian, #406

= [2.7.3] - 05 Jul 2022 =

* Fixed: Missing checklists settings menu in PHP 8.0 and Multisite, #387
* Fixed: Settings footer breaks for language other than English, #388
* Fixed: Missing translation for Min and Max, #389
* Added: Include new Free / Pro library, #377
* Fixed: Issue with Yoast SEO and post_content, #391
* Fixed: Clicking the Preview button will trigger the publishing pop-up, #378
* Fixed: Extra calls slowing down website, #385
* Fixed: Warning: Undefined array key "page", caused by "helper_settings_validate_and_save" function, #369
* Update: Most important buttons should be yellow only, #382
* Fixed: Issue with PHP 5.6, #386

= [2.7.2] - 27 Apr 2022 =

* Fixed: Fix Yoast SEO word count breaks in Pending Review, #345;
* Fixed: Run the WordPress VIP scans on Checklists, #354;

= [2.7.1] - 20 Apr 2022 =

* Fixed: Fix incorrect text in settings, #344;
* Fixed: Allow links with # as valid URL check, #352;
* Fixed: Hide task not supported for post type, #199;
* Fixed: Fix bad footer image URL on Windows for the Pro plugin, #342;
* Fixed: Only load the checklists and resources in relevant pages, #129;
* Fixed: Fix method call is provided 2 parameters, but the method signature uses 1 parameters error, #179;
* Updated: Spanish and Italian translations, #348;

= [2.7.0] - 16 Feb 2022 =

* Added: Added capability "manage_checklists" to access Checklists screen, #173;
* Removed: Remove the icon from the admin heading;
* Fixed: Fix tabs layout in the settings page, #317;
* Fixed: Updated the Reviews library, fixing compatibility with our other plugins;
* Fixed: Add capability check before saving global checklists options (we already had a nonce check in place), #325;
* Fixed: Improved output escaping in the admin interface, #326;
* Fixed: Improved input sanitization, #324;
* Fixed: Fixed duplicated admin menu on PHP 8, #316;

= [2.6.0] = SKIPPED

Skipped version, for syncing the version number with the Pro plugin.

= [2.5.3] - 2021-11-15 =

* Fixed: Can't update published posts if requirements changed;
* Added: WordPress Reviews version 1.1.12;

= [2.5.2] - 2021-11-11 =

* Fixed: Missing logo image for ask-for-review banner;
* Added: Ask-for-review banner in other admin pages;

= [2.5.1] - 2021-11-11 =

* Fixed: Skip the comply of requirements when "Include pre-publish checklist" is disabled;
* Fixed: Preferences Panel box is broken;
* Fixed: Changed ID of span where full slug is picked up from with Classic Editor;
* Fixed: Border width for buttons;
* Added: Ask for plugin review support;

= [2.5.0] - 2021-04-22 =

* Added: Added drag-and-drop support for sorting the checklists requirements, #172;
* Fixed: Fixed default position of items in the checklist;
* Changed: Added support for displaying unit text in the checklist requirements settings page;

= [2.4.4] - 2021-03-31 =

* Fixed: Fixed link validation for "tel:" and "mailto:" links, #246
* Fixed: Fixed WPBakery compatibility, #237;

= [2.4.3] - 2021-03-30 =

* Fixed: Fixed support to PHP 5.6, #240;
* Fixed: Fixed some class names to match the filename, #241;
* Fixed: Fixed some strings that were not being translated;
* Fixed: Fixed detection of the Block Editor when the Classic Editor plugin is installed and the user can select which editor to use, #239;
* Fixed: Fixed a CSS conflict with the class "warning" and some themes, #243;
* Fixed: Fixed pre-publishing panel and warning when required items are unchecked, #252;
* Added: Added Italian translation. Huge thanks to Simone Bianchelli and Angelo Giammarresi for sharing the translation files;

= [2.4.2] - 2020-10-22 =

* Fixed: Remove unexistent dependencies for met-box.js, #231;

= [2.4.1] - 2020-10-08 =

* Fixed: Fix JS error Uncaught TypeError: Cannot read property 'doAction' of undefined, #224;
* Fixed: Fix broken menu item if the user doesn't have permissions to see the menu, #226;

= [2.4.0] - 2020-09-22 =

* Added: Added a new task for validating links in the content, #200;
* Added: Added a new task for checking the number of external links, #201;
* Added: Added form validation for required fields in the checklists page, #175;
* Added: Added a new task for requiring approval for specific roles, #104;
* Added: Added new field for custom tasks to select which role can check/uncheck the box, #104;
* Changed: Changed the order of tasks in the settings page, #223;
* Removed: The option "Recommended: show only in the sidebar" were removed and current settings fallback to "Recommended: show in the sidebar and before publishing", which was renamed to just: "Recommended", #195.

= [2.3.2] - 2020-08-20 =

* Fixed: Fixed warnings related to missed dependencies for scripts when the post type is not selected to use checklists, #208;

= [2.3.1] - 2020-08-14 =

* Fixed: Fixed compatibility with WP 5.5;
* Fixed: Fixed Gutenberg and Classic Editor detection, #203, #202;
* Fixed: Fixed invalid selector in jQuery, #197;
* Fixed: Fixed the publishing button that was stuck sometimes making impossible to publish a post, #191;

= [2.3.0] - 2020-08-06 =

* Added: Added new task for checking if all the images in the post has an "alt" attribute, #164;
* Fixed: Fixed the verification for custom taxonomies in the post editor page, #114;
* Fixed: Fixed style for unchecked custom tasks, #184;
* Fixed: Updated language files;
* Changed: Hide Yoast SEO tasks if Yoast's plugin is not activated, #164;
* Changed: Updated translation strings;
* Changed: Changed the algorithm of the Yoast SEO readability and SEO analysis verification, considering the selected score as the minimum score, #169;
* Changed: Change the label of the "Add custom item" button to "Add custom task", #181;

= [2.2.0] - 2020-07-21 =

* Added: Add support to Yoast SEO readability and SEO analysis pass task in the checklists - #86;
* Added: Add new task for checking the limit of chars in the excerpt test - #150;
* Added: Add new task for checking the number of internal links in the text - #52;
* Fixed: Remove not used transient for checking data migration;
* Fixed: JS error message related to missed PP_Checklists object;
* Fixed: Enqueue scripts only when required - #106;
* Fixed: Fixed translation support adding French and British English translations;
* Changed: Updated the PHP min requirement from 5.4 to 5.6;
* Changed: Updated the WordPress tested up to version to 5.4;
* Changed: Updated the label and text for some tasks;

= [2.1.0] - 2020-05-07 =

* Added: Add permalink validation rule for the checklists - #115;
* Added: Add option to select user roles to skip specific requirements - #131;
* Added: Add a menu link to upgrade to the Pro plan;
* Changed: Improve UI for custom items in the checklist, removing the "X" icon - #126;
* Removed: Remove the option to hide the Publish Button due to conflicts with Gutenberg;
* Fixed: Fixed the tabs for post types in the Checklists admin page. If you have too many post types the second line of tabs was overlaying the first line - #132;
* Fixed: Fixed the checklist warning popup when you are updating a published post and has unchecked required tasks in the checklist, for the classic editor - #124;
* Fixed: Fixed the list of available post types for the checklists to display any post type that has the show_ui = true. Non public post types are now recognized - #127;
* Fixed: Fixed the list of post types in the Checklists page hiding the tabs of post types that are not selected in the settings - #136;
* Fixed: Fixed the error displayed on Windows servers when the constant DIRECTORY_SEPARATOR is not defined;
* Fixed: Fixed empty checklists on fresh installs due to no post type being selected. Posts is selected by default now - #140;
* Fixed: Fix warning icon on Gutenberg moving it from the side to over the publish button - #138;

= [2.0.2] - 2020-03-16 =

* Fixed: Fix Checklist for custom hierarquical taxonomies when using Gutenberg;
* Fixed: Small improvements to the UI;
* Fixed: Fix compatibility with Rank Math fixing error in Gutenberg;
* Added: Added hooks to extend the interface for the Pro version;

= [2.0.1] - 2020-02-07 =

* Fixed: Fixed the prefix of post types in the post_type_support variable;
* Fixed: Adjusted the plugin URL for assets when working as vendor dependency;
* Fixed: Fix the suffix of the settings section from _post_types, to _general;
* Fixed: Fixed an undefined index error when the index "type" is not defined;
* Fixed: Fixed a JS error when you type in the editor and the word count requirement is set;
* Fixed: Fixed the verification for custom taxonomies on Gutenberg;
* Added: Added filters to allow using the plugin as base for the Pro plugin;

= [2.0.0] - 2019-12-03 =
* Fixed: Fixed the word counter for the Text tab in the Classic Editor;
* Changed: Renamed from "PublishPress Content Checklist" to "PublishPress Checklists";
* Changed: Refactored to be a standalone plugin, not requiring PublishPress anymore;
* Changed: Plugin name and text domain changed from "publishpress-content-checklist" to "publishpress-checklists";
* Changed: Namespace changed from "PublishPress\Addon\Content_checklist" to "PublishPress\Checklists\". The requirements' namespace changed from "PublishPress\Addon\Content_checklist\Requirement" to "PublishPress\Checklists\Core\Requirement";
* Changed: JavaScript object changed from "PP_Content_Checklist" to "PP_Checklists";
* Changed: New admin menu added with the checklists options and settings page;
* Changed: The checklists options section was removed from the settings page to an specific menu item;

= [1.4.7] - 2019-07-21 =
* Fixed: A JS error was preventing to block the post save action when displaying a popup with missed requirements on Classic Editor;

= [1.4.6] - 2019-06-20 =
* Fixed: Avoid JS white screen on Gutenberg "New Post" access by Author with Multiple Authors plugin active and "Remove author from new posts" setting enabled;
* Changed: Change minimum required version of PublishPress to 1.20.0;

= [1.4.5] - 2019-02-22 =
* Fixed: Fixed the pre-publishing check to avoid blocking save when not publishing;

= [1.4.4] - 2019-02-12 =
* Fixed: Fixed JS error that was preventing the Preview button to work properly in the classic editor;

= [1.4.3] - 2019-02-11 =
* Fixed: Fixed translation to PT-BR (thanks to Dionizio Bach);
* Fixed: Fixed bug when word-count script was not loaded;
* Fixed: Fixed JS error if an editor is not found;
* Changed: Changed the label for checklist options in the settings panel;

= [1.4.2] - 2019-01-30 =
* Fixed: Fixed the checklist for the block editor;
* Changed: Removed license key field from the settings tab;

= [1.4.1] - 2019-01-24 =
* Changed: Disable post types by default, if Gutenberg is installed;

= [1.4.0] - 2019-01-14 =
* Fixed: Fixed the TinyMCE plugin to count words to not load in the front-end when TinyMCE is initialized;
* Fixed: Fixed the assets loading to load tinymce-pp-checklists-requirements.js only in the admin;
* Fixed: Fixed conflict between custom taxonomies and tags in the checklist while counting items;
* Added: Added better support for custom post types and custom taxonomies which use WordPress default UI;
* Changed: Update POT file and fixed translations loading the text domain;
* Changed: Updated PT-BT language files;

= [1.3.8] - 2018-04-18 =
* Fixed: Fixed wrong reference to a legacy EDD library's include file;
* Fixed: Fixed PHP warning about undefined property and constant;

= [1.3.7] - 2018-02-21 =
* Fixed: Fixed support for custom post types;

= [1.3.6] - 2018-02-07 =
* Fixed: Fixed error about class EDD_SL_Plugin_Updater being loaded twice;

= [1.3.5] - 2018-02-06 =
* Fixed: Fixed saving action for custom items on the checklist;
* Fixed: Fixed license validation and automatic update;

= [1.3.4] - 2018-01-26 =
* Changed: Changed plugin headers, fixing author and text domain;

= [1.3.3] - 2018-01-26 =
* Fixed: Fixed JS error when the checklist is empty (no requirements are selected);
* Fixed: Fixed compatibility with PHP 5.4 (we will soon require min 5.6);
* Fixed: Fixed custom requirements;
* Fixed: Fixed the requirement of tags;
* Fixed: Fixed PHP Fatal error on some PHP on the featured image requirement;
* Fixed: Fixed category count in the checklist;
* Added: Added action to load plugins' script files;
* Changed: Rebranded to PublishPress;

= [1.3.2] - 2017-08-31 =
* Fixed: Fixed EDD integration and updates;
* Changed: Removed Freemius integration;

= [1.3.1] - 2017-07-13 =
* Fixed: Fixed support for custom post types allowing to use custom items as requirements;

= [1.3.0] - 2017-07-12 =
* Fixed: Fixed the delete button for custom items in the settings. It was remocing wrong items, in an odd pattern;
* Fixed: Fixed PHP warning in the settings page about undefined index in array;
* Fixed: Fixed the menu slug in the Freemius integration;
* Added: Added support for setting specific requirements for each post type, instead of global only;
* Changed: Changed the required minimum version of PublishPress to 1.6.0;
* Changed: Improved extensibility for add-ons;

= [1.2.1] - 2017-06-21 =
* Fixed: Fixed PHP warnings after install and activate
* Fixed: Fixed PHP warnings about wrong index type
* Fixed: Fixed the license and update checker
* Added: Added pt-BR translations
* Changed: Removed English language files
* Changed: Updated Tested Up to 4.8

= [1.2.0] - 2017-06-06 =
* Fixed: Fixes the mask for numeric input fields in the settings tab on Firefox
* Fixed: Fixes the license key validation
* Fixed: Fixes the update system
* Added: Added the option to hide the Publish button if the checklist is not completed
* Added: Added the option to add custom items for the checklist
* Added: Added POT file and English PO files
* Changed: The warning icon in the publish box now appears even for published content

= [1.1.2] - 2017-05-23 =
* Fixed: Fixes the word count feature
* Changed: Displays empty value in the max fields when max is less than min
* Changed: Improves the min and max fields for value equal 0. Displays empty fields.

= [1.1.1] - 2017-05-18 =
* Fixed: Removed .DS_Store file from the package
* Fixed: Fixed the "Hello Dolly" message in the Freemius opt-in dialog
* Fixed: Increased the minimum WordPress version to 4.6
* Changed: Improved settings merging the checkbox and the action list for each requirement
* Changed: Changed order for Categories and Tags to stay together in the list
* Changed: Changed code to use correct language domain

= [1.1.0] - 2017-05-11 =
* Added: Added "Excerpt has text" as requirement
* Added: Added option to set "max" value for the number of categories, tags and words - now you can have min, max or an interval for each requirement.
* Changed: Improved the JavaScript code for better readbility

= [1.0.1] - 2017-05-03 =
* Fixed: Fixed the name of plugin's main file
* Fixed: Fixed WordPress-EDD-License-Integration library in the vendor dir

= [1.0.0] - 2017-04-27 =
* Added: Added requirement for minimum number of words
* Added: Added requirement for featured image
* Added: Added requirement for minimum number of tags
* Added: Added requirement for minimum number of categories
* Added: Added Freemius integration for feedback and contact form
* Added: Added option to display a warning icon in the publish box
* Added: Added checklist to the post form
* Added: Added option to select specific post types
