# Translation of Plugins - PublishPress Checklists: Pre-Publishing Approval Task Checklist for WordPress Content - Stable (latest release) in Spanish (Spain)
# This file is distributed under the same license as the Plugins - PublishPress Checklists: Pre-Publishing Approval Task Checklist for WordPress Content - Stable (latest release) package.
msgid ""
msgstr ""
"Project-Id-Version: Plugins - PublishPress Checklists: Pre-Publishing "
"Approval Task Checklist for WordPress Content - Stable (latest release)\n"
"POT-Creation-Date: 2024-10-15 21:56+0700\n"
"PO-Revision-Date: 2024-10-17 20:53+0000\n"
"Last-Translator: \n"
"Language-Team: Spanish (Spain)\n"
"Language: es_ES\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Loco https://localise.biz/\n"
"Report-Msgid-Bugs-To: \n"
"X-Loco-Version: 2.6.11; wp-6.6.2"

#: core/Requirement/Taxonomies_count.php:48
#: core/Requirement/Taxonomies_count.php:49
msgid "%d "
msgstr "%d "

#: core/Requirement/Filled_excerpt.php:41
msgid "%d character in excerpt"
msgstr "%d caracter en el extracto"

#: core/Requirement/Title_count.php:39
msgid "%d character in title"
msgstr "%d caracteres en el título"

#: core/Requirement/Filled_excerpt.php:42
msgid "%d characters in excerpt"
msgstr "%d caracteres en el extracto"

#: core/Requirement/Title_count.php:40
msgid "%d characters in title"
msgstr "%d caracteres en el título"

#: core/Requirement/External_links.php:41
msgid "%d external link in content"
msgstr "%d enlace externo en el contenido"

#: core/Requirement/External_links.php:42
msgid "%d external links in content"
msgstr "%d enlaces externos en el contenido"

#: core/Requirement/Internal_links.php:41
msgid "%d internal link in content"
msgstr "%d enlace interno en el contenido"

#: core/Requirement/Internal_links.php:42
msgid "%d internal links in content"
msgstr "%d enlaces internos en el contenido"

#: core/Requirement/Tags_count.php:45
msgid "%d tag"
msgstr "%d etiqueta"

#: core/Requirement/Tags_count.php:46
msgid "%d tags"
msgstr "%d etiquetas"

#: core/Requirement/Words_count.php:41
msgid "%d word in content"
msgstr "%d palabra en el contenido"

#: core/Requirement/Words_count.php:42
msgid "%d words in content"
msgstr "%d palabras en el contenido"

#: core/Requirement/Categories_count.php:46
#, php-format
msgid "%s categories"
msgstr "%s categorías"

#: core/Requirement/Categories_count.php:45
#, php-format
msgid "%s category"
msgstr "%s categoría"

#: modules/checklists/templates/footer.php:23
msgid "About"
msgstr "Acerca de"

#: modules/checklists/checklists.php:953
msgid "Add custom task"
msgstr "Añadir tarea personalizada"

#: modules/checklists/templates/global-checklists.php:94
msgid "Add OpenAI Prompt task"
msgstr "Añadir tarea de indicación de OpenAI"

#: modules/settings/settings.php:684
msgid "Add to these post types:"
msgstr "Añadir a estos tipos de contenido:"

#: core/Requirement/Image_alt.php:36 core/Requirement/Image_alt.php:37
msgid "All images have Alt text"
msgstr "Todas las imágenes tienen texto alternativo"

#: core/Requirement/Validate_links.php:58
#: core/Requirement/Validate_links.php:59
msgid "All links use a valid format"
msgstr "Todos los enlaces usan un formato válido"

#: core/Requirement/Openai_item.php:266
msgid "An error occured."
msgstr "Se ha producido un error."

#: core/Requirement/Openai_item.php:340
#, php-format
msgid "API Error: %1s."
msgstr "Error de API: %1s."

#: core/Requirement/Approved_by.php:41
#, php-format
msgid "Approved by %s"
msgstr "Aprobado por %s"

#: core/Requirement/Approved_by.php:42
msgid "Approved by a user in this role"
msgstr "Aprobado por un usuario con este perfil"

#: modules/checklists/checklists.php:736
msgid "Are you sure you want to publish anyway?"
msgstr "¿Estás seguro de querer publicar de todos modos?"

#: modules/checklists/checklists.php:740
msgid "Are you sure you want to update the published post anyway?"
msgstr ""
"¿Estás seguro de querer actualizar la entrada publicada de todos modos?"

#: core/Requirement/Taxonomies_count.php:50
msgid "Between %d and %d "
msgstr "Entre %d y %d "

#: core/Requirement/Filled_excerpt.php:43
msgid "Between %d and %d characters in excerpt"
msgstr "Entre %d y %d caracteres en el extracto"

#: core/Requirement/Title_count.php:41
msgid "Between %d and %d characters in title"
msgstr "Entre %d y %d caracteres en el título"

#: core/Requirement/External_links.php:43
msgid "Between %d and %d external links in content"
msgstr "Entre %d y %d enlaces externos en el contenido"

#: core/Requirement/Internal_links.php:43
msgid "Between %d and %d internal links in content"
msgstr "Entre %d y %d enlaces internos en el contenido"

#: core/Requirement/Tags_count.php:47
msgid "Between %d and %d tags"
msgstr "Entre  %d and %d etiquetas"

#: core/Requirement/Words_count.php:43
msgid "Between %d and %d words in content"
msgstr "Entre %d y %d palabras en el contenido"

#: core/Requirement/Categories_count.php:47
#, php-format
msgid "Between %s and %s categories"
msgstr "Entre  %s y %s categorías"

#: core/Legacy/LegacyPlugin.php:213 modules/settings/settings.php:444
msgid "Cheatin&#8217; uh?"
msgstr "Haciendo trampas, ¿eh?"

#: modules/checklists/checklists.php:788
#: modules/checklists/assets/js/gutenberg-panel.jsx:242
msgid "Check Now"
msgstr "Comprobar ahora"

#: modules/checklists/checklists.php:669 modules/checklists/checklists.php:734
msgid "Checklist"
msgstr "Lista de verificación"

#: core/Legacy/LegacyPlugin.php:349 modules/checklists/checklists.php:101
#: modules/checklists/checklists.php:887 modules/checklists/checklists.php:902
#: modules/checklists/assets/js/gutenberg-panel.jsx:197
#: modules/checklists/assets/js/gutenberg-panel.jsx:201
msgid "Checklists"
msgstr "Listas de verificación"

#: modules/settings/settings.php:61 modules/settings/settings.php:112
msgid "Checklists Settings"
msgstr "Ajustes de las listas de verificación"

#: core/Legacy/LegacyPlugin.php:206 modules/checklists/checklists.php:735
msgid "Configure"
msgstr "Configurar"

#: modules/checklists/templates/footer.php:37
msgid "Contact"
msgstr "Contacto"

#: core/Requirement/Custom_item.php:52 core/Requirement/Custom_item.php:53
msgid "Custom"
msgstr "Personalizada"

#: modules/checklists/templates/global-checklists.php:87
msgid ""
"Custom tasks do not complete automatically. Users must check the box to show "
"they have completed the task."
msgstr ""
"Las tareas personalizadas no se completan automáticamente. Los usuarios "
"deben marcar la caja para indicar que han completado la tarea."

#: modules/permalinks/permalinks.php:66
msgid "Define tasks related to permalinks"
msgstr "Definir las tareas relacionadas con los enlaces permanentes"

#: modules/yoastseo/yoastseo.php:67
msgid "Define tasks related to Yoast SEO"
msgstr "Definir tareas de Yoast SEO"

#: modules/checklists/templates/global-checklists.php:75
msgid "Description"
msgstr "Descripción"

#: modules/settings/settings.php:700
msgid "Disable the \"Status\" option when using \"Quick Edit\":"
msgstr "Desactiva la opción «Estado» cuando utilices la «Edición rápida»:"

#: modules/checklists/checklists.php:975
msgid "Disabled"
msgstr "Desactivado"

#: modules/settings/settings.php:408
#, php-format
msgid ""
"Disabled because add_post_type_support('%1$s', '%2$s') is included in a "
"loaded file."
msgstr ""
"Desactivado porque `add_post_type_support('%1$s', '%2$s')` está incluido en "
"un archivo cargado."

#: modules/checklists/checklists.php:951
msgid "Disabled, Recommended or Required"
msgstr "Desactivado, Recomendado u Obligatorio"

#: modules/checklists/templates/footer.php:30
msgid "Documentation"
msgstr "Documentación"

#: modules/settings/settings.php:261
msgid "Enabled features"
msgstr "Características activadas"

#: modules/settings/settings.php:781
msgid "Enter your API Key to use OpenAI prompts in checklist tasks."
msgstr ""
"Introduce tu clave API para utilizar las indicaciones de OpenAI en las "
"tareas de las listas de verificación."

#: core/Requirement/Featured_image_alt.php:36
#: core/Requirement/Featured_image_alt.php:37
msgid "Featured image has Alt text"
msgstr "La imagen destacada tiene texto alternativo"

#: core/Requirement/Featured_image.php:35
#: core/Requirement/Featured_image.php:36
msgid "Featured image is added"
msgstr "Se agregó la imagen destacada"

#: modules/settings/settings.php:252
msgid "Features"
msgstr "Características"

#: modules/settings/settings.php:253
msgid "Feel free to select only the features you need."
msgstr "Siéntete libre de seleccionar sólo las características que necesitas."

#: modules/settings/settings.php:675
msgid "General:"
msgstr "General:"

#: modules/yoastseo/lib/Requirement/Seo_Analysis.php:129
#: modules/yoastseo/lib/Requirement/Seo_Analysis.php:160
#: modules/yoastseo/lib/Requirement/Readability_Analysis.php:130
#: modules/yoastseo/lib/Requirement/Readability_Analysis.php:161
msgid "Good"
msgstr "Bueno"

#. Author URI of the plugin
msgid "https://publishpress.com"
msgstr "https://publishpress.com"

#. URI of the plugin
msgid "https://publishpress.com/"
msgstr "https://publishpress.com/"

#: modules/settings/settings.php:762
msgid ""
"If the \"Status\" option is enabled, it can be used to avoid using the "
"Checklists requirements."
msgstr ""
"Si la opción «Estado» está activada, puede utilizarse para evitar el uso de "
"los requisitos de las listas de control."

#: core/Legacy/Module.php:288
#, php-format
msgid "If you like %s please leave us a %s rating. Thank you!"
msgstr "Si te gusta %s, por favor déjanos una valoración de %s. ¡Gracias!"

#: core/Requirement/Openai_item.php:386
msgid "Invalid response"
msgstr "Respuesta no válida"

#: modules/permalinks/lib/Requirement/ValidChars.php:37
msgid "Latin characters in permalink"
msgstr "Caracteres latinos en el enlace permanente"

#: core/Requirement/Base_counter.php:293
msgid "Max"
msgstr "Máximo"

#: core/Requirement/Taxonomies_count.php:46
#: core/Requirement/Taxonomies_count.php:47
msgid "Maximum of %d "
msgstr "Máximo de %d "

#: core/Requirement/Filled_excerpt.php:39
msgid "Maximum of %d character in excerpt"
msgstr "Máximo de %d caracteres en el extracto"

#: core/Requirement/Title_count.php:37
msgid "Maximum of %d character in title"
msgstr "Máximo de %d caracteres en el título"

#: core/Requirement/Filled_excerpt.php:40
msgid "Maximum of %d characters in excerpt"
msgstr "Máximo de %d caracteres en el extracto"

#: core/Requirement/Title_count.php:38
msgid "Maximum of %d characters in title"
msgstr "Máximo de %d caracteres en el título"

#: core/Requirement/External_links.php:39
msgid "Maximum of %d external link in content"
msgstr "Máximo de %d enlace externo en el contenido"

#: core/Requirement/External_links.php:40
msgid "Maximum of %d external links in content"
msgstr "Máximo de %d enlaces externos en el contenido"

#: core/Requirement/Internal_links.php:39
msgid "Maximum of %d internal link in content"
msgstr "Máximo de %d enlace interno en el contenido"

#: core/Requirement/Internal_links.php:40
msgid "Maximum of %d internal links in content"
msgstr "Máximo de %d enlaces internos en el contenido"

#: core/Requirement/Tags_count.php:43
msgid "Maximum of %d tag"
msgstr "Máximo de %d etiquetas"

#: core/Requirement/Tags_count.php:44
msgid "Maximum of %d tags"
msgstr "Máximo de %d etiquetas"

#: core/Requirement/Words_count.php:39
msgid "Maximum of %d word in content"
msgstr "Máximo de %d palabras en el contenido"

#: core/Requirement/Words_count.php:40
msgid "Maximum of %d words in content"
msgstr "Máximo de %d palabras en el contenido"

#: core/Requirement/Categories_count.php:44
#, php-format
msgid "Maximum of %s categories"
msgstr "Máximo de %s categorías"

#: core/Requirement/Categories_count.php:43
#, php-format
msgid "Maximum of %s category"
msgstr "Máximo de %s categorías"

#: core/Requirement/Base_counter.php:285
msgid "Min"
msgstr "Mínimo"

#: core/Requirement/Taxonomies_count.php:44
#: core/Requirement/Taxonomies_count.php:45
msgid "Minimum of %d "
msgstr "Mínimo de %d "

#: core/Requirement/Filled_excerpt.php:37
msgid "Minimum of %d character in excerpt"
msgstr "Mínimo de %d caracteres en el extracto"

#: core/Requirement/Title_count.php:35
msgid "Minimum of %d character in title"
msgstr "Mínimo de %d caracteres en el título"

#: core/Requirement/Filled_excerpt.php:38
msgid "Minimum of %d characters in excerpt"
msgstr "Mínimo de %d caracteres en el extracto"

#: core/Requirement/Title_count.php:36
msgid "Minimum of %d characters in title"
msgstr "Mínimo de %d caracteres en el título"

#: core/Requirement/External_links.php:37
msgid "Minimum of %d external link in content"
msgstr "Mínimo de %d enlaces externos en el contenido"

#: core/Requirement/External_links.php:38
msgid "Minimum of %d external links in content"
msgstr "Mínimo de %d enlaces externos en el contenido"

#: core/Requirement/Internal_links.php:37
msgid "Minimum of %d internal link in content"
msgstr "Mínimo de %d enlace interno en el contenido"

#: core/Requirement/Internal_links.php:38
msgid "Minimum of %d internal links in content"
msgstr "Mínimo de %d enlaces internos en el contenido"

#: core/Requirement/Tags_count.php:41
msgid "Minimum of %d tag"
msgstr "Mínimo de %d etiquetas"

#: core/Requirement/Tags_count.php:42
msgid "Minimum of %d tags"
msgstr "Mínimo de %d etiquetas"

#: core/Requirement/Words_count.php:37
msgid "Minimum of %d word in content"
msgstr "Mínimo de %d palabras en el contenido"

#: core/Requirement/Words_count.php:38
msgid "Minimum of %d words in content"
msgstr "Mínimo de %d palabras en el contenido"

#: core/Requirement/Categories_count.php:42
#, php-format
msgid "Minimum of %s categories"
msgstr "Mínimo de %s categorías"

#: core/Requirement/Categories_count.php:41
#, php-format
msgid "Minimum of %s category"
msgstr "Mínimo de %s categorías"

#: modules/yoastseo/lib/Requirement/Seo_Analysis.php:46
msgid "Minimum Yoast SEO analysis score"
msgstr "Puntuación mínima del análisis Yoast SEO"

#: modules/yoastseo/lib/Requirement/Readability_Analysis.php:47
msgid "Minimum Yoast SEO readability score"
msgstr "Mínima puntuación de legibilidad en Yoast SEO"

#: modules/checklists/templates/global-checklists.php:74
msgid "New Item"
msgstr "Nuevo elemento"

#: modules/checklists/checklists.php:790
msgid "No"
msgstr "No"

#: modules/checklists/templates/global-checklists.php:101
msgid "No %s requirements for this post type."
msgstr "No hay %s requisitos para este tipo de publicación."

#: modules/checklists/checklists.php:752 modules/checklists/checklists.php:756
msgid "Not required, but important: "
msgstr "No es obligatorio, pero es importante: "

#: core/Requirement/Taxonomies_count.php:43
msgid "Number of "
msgstr "Número de "

#: core/Requirement/Categories_count.php:40
msgid "Number of categories"
msgstr "Número de categorías"

#: core/Requirement/Image_alt_count.php:42
#: core/Requirement/Image_alt_count.php:43
msgid "Number of characters in Alt text"
msgstr "Número de caracteres en el texto Alt"

#: core/Requirement/Title_count.php:33 core/Requirement/Title_count.php:34
msgid "Number of characters in title"
msgstr "Número de caracteres en el título"

#: core/Requirement/External_links.php:36
msgid "Number of external links in content"
msgstr "Número de links externos en el contenido"

#: core/Requirement/Internal_links.php:36
msgid "Number of internal links in content"
msgstr "Número de enlaces internos en el contenido"

#: core/Requirement/Tags_count.php:40
msgid "Number of tags"
msgstr "Número de etiquetas"

#: core/Requirement/Words_count.php:36
msgid "Number of words in content"
msgstr "Número de palabras en el contenido"

#: core/Requirement/Filled_excerpt.php:35
#: core/Requirement/Filled_excerpt.php:36
msgid "Numbers of characters in excerpt"
msgstr "Número de caracteres en el extracto"

#: modules/yoastseo/lib/Requirement/Seo_Analysis.php:124
#: modules/yoastseo/lib/Requirement/Seo_Analysis.php:159
#: modules/yoastseo/lib/Requirement/Readability_Analysis.php:125
#: modules/yoastseo/lib/Requirement/Readability_Analysis.php:160
msgid "OK"
msgstr "Correcto"

#: modules/checklists/checklists.php:789
msgid "Ok"
msgstr "Aceptar"

#: core/Requirement/Openai_item.php:61 core/Requirement/Openai_item.php:62
msgid "Openai"
msgstr "Openai"

#: modules/settings/settings.php:708
msgid "OpenAI API Key:"
msgstr "Clave API de OpenAI:"

#: core/Requirement/Openai_item.php:287
msgid ""
"OpenAI tasks require an API Key. Please add your API Key in the Settings "
"area."
msgstr ""
"Las tareas de OpenAI requieren una clave API. Por favor, añade tu clave API "
"en los ajustes."

#: modules/checklists/checklists.php:952
msgid "Options"
msgstr "Opciones"

#: modules/permalinks/permalinks.php:65
msgid "Permalinks"
msgstr "Enlaces permanentes"

#: modules/permissions/permissions.php:68
msgid "Permissions"
msgstr "Permisos"

#: modules/checklists/checklists.php:744
msgid "Please complete the following tasks before publishing:"
msgstr "Por favor, completa las siguientes tareas antes de publicar:"

#: modules/checklists/checklists.php:748
msgid "Please complete the following tasks before updating the published post:"
msgstr ""
"Por favor, completa estas tareas antes de actualizar la estrada publicada:"

#: modules/checklists/assets/js/gutenberg-panel.jsx:167
msgid "Please complete the required(*) checklists task."
msgstr ""
"Por favor, completa la tarea obligatoria(*) de las listas de verificación."

#: core/Legacy/LegacyPlugin.php:209
msgid "Please correct your form errors below and try again."
msgstr ""
"Por favor, corrige tus errores en el formulario de abajo y prueba otra vez."

#: modules/checklists/checklists.php:594
msgid "Please make sure to add a name for all the custom tasks."
msgstr ""
"Por favor asegúrate de añadirle un nombre a todas las tareas personalizadas."

#: modules/checklists/checklists.php:590
msgid "Please make sure to complete the settings for"
msgstr "Por favor, asegúrate de completar los ajustes de"

#: core/Requirement/Openai_item.php:278
msgid "Post content is empty."
msgstr "El contenido de la entrada es vacío."

#: core/Legacy/LegacyPlugin.php:218
msgid "Post does not exist"
msgstr "La entrada no existe"

#: core/Requirement/Prohibited_categories.php:43
msgid "Prohibited categories"
msgstr "Categorías prohibidas"

#: core/Requirement/Prohibited_categories.php:42
msgid "Prohibited categories: %s"
msgstr "Categorías prohibidas: %s"

#: core/Requirement/Prohibited_tags.php:43
msgid "Prohibited tags"
msgstr "Etiquetas prohibidas"

#: core/Requirement/Prohibited_tags.php:42
msgid "Prohibited tags: %s"
msgstr "Etiquetas prohibidas: %s"

#. Author of the plugin
msgid "PublishPress"
msgstr "PublishPress"

#. Name of the plugin
#: core/Legacy/Module.php:285
msgid "PublishPress Checklists"
msgstr "Publishpress Checklists"

#: modules/checklists/checklists.php:976
msgid "Recommended"
msgstr "Recomendado"

#: core/Requirement/Custom_item.php:121 core/Requirement/Openai_item.php:139
#: modules/checklists/checklists.php:602
msgid "Remove"
msgstr "Eliminar"

#: modules/checklists/checklists.php:787 modules/checklists/checklists.php:977
msgid "Required"
msgstr "Obligatorio"

#: modules/checklists/assets/js/gutenberg-panel.jsx:255
msgid "required"
msgstr "obligatorio"

#: core/Requirement/Required_categories.php:43
msgid "Required categories"
msgstr "Categorías requeridas"

#: core/Requirement/Required_categories.php:42
msgid "Required categories: %s"
msgstr "Categorías requeridas: %s"

#: core/Requirement/Required_tags.php:43
msgid "Required tags"
msgstr "Etiquetas requeridas"

#: core/Requirement/Required_tags.php:42
msgid "Required tags: %s"
msgstr "Etiquetas requeridas: %s"

#: modules/reviews/reviews.php:61
msgid "Reviews"
msgstr "Valoraciones"

#: modules/checklists/templates/global-checklists.php:108
msgid "Save Changes"
msgstr "Guardar cambios"

#: core/Requirement/Openai_item.php:365
msgid "See the full response."
msgstr "Ver la respuesta completa."

#: modules/settings/settings.php:114
msgid "Settings"
msgstr "Ajustes"

#: modules/settings/settings.php:692
msgid "Show warning icon:"
msgstr "Mostrar el icono de advertencia:"

#: modules/checklists/checklists.php:950
msgid "Task"
msgstr "Tarea"

#: modules/checklists/templates/global-checklists.php:101
msgid "The prompt should be in form of a question."
msgstr "La indicación debe tener forma de pregunta."

#: modules/settings/settings.php:312
msgid "There are no PublishPress modules registered"
msgstr "No hay módulos de PublishPress registrados"

#: modules/checklists/templates/global-checklists.php:101
msgid "This feature requires an OpenAI API Key."
msgstr ""
"Para utilizar esta característica es obligatorio disponer de una clave API "
"de OpenAI."

#: modules/settings/settings.php:741
msgid "This will display a warning icon in the \"Publish\" box."
msgstr "Esto mostrará un icono de advertencia en la caja «Publicar»"

#: core/Requirement/Openai_item.php:273
msgid "Validation error. Kindly reload this page and try agai.n"
msgstr "Error de validación. Vuelve a cargar esta página e inténtalo de nuevo"

#: modules/checklists/checklists.php:606
msgid "What's the expected OpenAI response to mark the requirement as pass?"
msgstr ""
"¿Cuál es la respuesta esperada de OpenAI para marcar el requisito como "
"aprobado?"

#: core/Requirement/Custom_item.php:54 modules/checklists/checklists.php:598
msgid "Which roles can mark this task as complete?"
msgstr "¿Qué perfiles pueden marcar esta tarea como completada?"

#: modules/permissions/permissions.php:143
msgid "Who can ignore the task?"
msgstr "¿Quién puede ignorar la tarea?"

#. Description of the plugin
msgid ""
"With PublishPress Checklists, you can choose publishing requirements for "
"your content."
msgstr ""
"Con las listas de verificación de PublishPress, puede elegir los requisitos "
"de publicación para su contenido."

#: modules/checklists/checklists.php:791
msgid "Yes"
msgstr "Sí"

#. %s expands to the readability score
#: modules/yoastseo/lib/Requirement/Readability_Analysis.php:124
#: modules/yoastseo/lib/Requirement/Readability_Analysis.php:129
#, php-format
msgid "Yoast Readability: %s"
msgstr "Legibilidad de Yoast: %s"

#: modules/yoastseo/yoastseo.php:66
msgid "Yoast SEO"
msgstr "Yoast SEO"

#. %s expands to the seo score
#: modules/yoastseo/lib/Requirement/Seo_Analysis.php:123
#: modules/yoastseo/lib/Requirement/Seo_Analysis.php:128
#, php-format
msgid "Yoast SEO: %s"
msgstr "SEO de Yoast: %s"

#: core/Legacy/LegacyPlugin.php:214
msgid "You do not have necessary permissions to complete this action."
msgstr "No tienes los permisos necesarios para realizar esta acción."

#: modules/checklists/checklists.php:783
#, php-format
msgid "You don't have to complete any %sChecklist tasks%s."
msgstr ""
"No tienes que completar ninguna de las %stareas de la lista de "
"verificación%s."

#: modules/checklists/checklists.php:730
#: modules/checklists/assets/js/gutenberg-panel.jsx:208
msgid "You don't have to complete any Checklist tasks."
msgstr "No tienes que completar ninguna tarea de la lista de verificación."
