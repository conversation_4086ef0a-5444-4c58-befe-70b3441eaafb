#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PublishPress Checklists\n"
"POT-Creation-Date: 2024-10-15 21:56+0700\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON> <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=INTEGER; plural=EXPRESSION;\n"
"X-Generator: Poedit 3.0.1\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-Flags-xgettext: --add-comments=translators:\n"
"X-Poedit-WPHeader: publishpress-checklists.php\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;esc_attr_e;"
"esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;_nx_noop:3c,1,2;"
"__ngettext_noop:1,2\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.min.js\n"
"X-Poedit-SearchPathExcluded-1: vendor\n"
"X-Poedit-SearchPathExcluded-2: node_modules\n"

#: core/Requirement/Taxonomies_count.php:48 core/Requirement/Taxonomies_count.php:49
msgid "%d "
msgstr ""

#: core/Requirement/Filled_excerpt.php:41
msgid "%d character in excerpt"
msgstr ""

#: core/Requirement/Title_count.php:39
msgid "%d character in title"
msgstr ""

#: core/Requirement/Filled_excerpt.php:42
msgid "%d characters in excerpt"
msgstr ""

#: core/Requirement/Title_count.php:40
msgid "%d characters in title"
msgstr ""

#: core/Requirement/External_links.php:41
msgid "%d external link in content"
msgstr ""

#: core/Requirement/External_links.php:42
msgid "%d external links in content"
msgstr ""

#: core/Requirement/Internal_links.php:41
msgid "%d internal link in content"
msgstr ""

#: core/Requirement/Internal_links.php:42
msgid "%d internal links in content"
msgstr ""

#: core/Requirement/Tags_count.php:45
msgid "%d tag"
msgstr ""

#: core/Requirement/Tags_count.php:46
msgid "%d tags"
msgstr ""

#: core/Requirement/Words_count.php:41
msgid "%d word in content"
msgstr ""

#: core/Requirement/Words_count.php:42
msgid "%d words in content"
msgstr ""

#: core/Requirement/Categories_count.php:46
#, php-format
msgid "%s categories"
msgstr ""

#: core/Requirement/Categories_count.php:45
#, php-format
msgid "%s category"
msgstr ""

#: modules/checklists/templates/footer.php:23
msgid "About"
msgstr ""

#: modules/checklists/checklists.php:953
msgid "Add custom task"
msgstr ""

#: modules/checklists/templates/global-checklists.php:94
msgid "Add OpenAI Prompt task"
msgstr ""

#. Description of the plugin
msgid "With PublishPress Checklists, you can choose publishing requirements for your content."
msgstr ""

#: modules/settings/settings.php:684
msgid "Add to these post types:"
msgstr ""

#: core/Requirement/Validate_links.php:58 core/Requirement/Validate_links.php:59
msgid "All links use a valid format"
msgstr ""

#: core/Requirement/Image_alt.php:36 core/Requirement/Image_alt.php:37
msgid "All images have Alt text"
msgstr ""

#: core/Requirement/Featured_image_alt.php:36 core/Requirement/Featured_image_alt.php:37
msgid "Featured image has Alt text"
msgstr ""

#: core/Requirement/Image_alt_count.php:42 core/Requirement/Image_alt_count.php:43
msgid "Number of characters in Alt text"
msgstr ""

#: core/Requirement/Openai_item.php:266
msgid "An error occured."
msgstr ""

#: core/Requirement/Openai_item.php:340
#, php-format
msgid "API Error: %1s."
msgstr ""

#: core/Requirement/Approved_by.php:41
#, php-format
msgid "Approved by %s"
msgstr ""

#: core/Requirement/Approved_by.php:42
msgid "Approved by a user in this role"
msgstr ""

#: modules/checklists/checklists.php:736
msgid "Are you sure you want to publish anyway?"
msgstr ""

#: modules/checklists/checklists.php:740
msgid "Are you sure you want to update the published post anyway?"
msgstr ""

#: core/Requirement/Taxonomies_count.php:50
msgid "Between %d and %d "
msgstr ""

#: core/Requirement/Filled_excerpt.php:43
msgid "Between %d and %d characters in excerpt"
msgstr ""

#: core/Requirement/Title_count.php:41
msgid "Between %d and %d characters in title"
msgstr ""

#: core/Requirement/External_links.php:43
msgid "Between %d and %d external links in content"
msgstr ""

#: core/Requirement/Internal_links.php:43
msgid "Between %d and %d internal links in content"
msgstr ""

#: core/Requirement/Tags_count.php:47
msgid "Between %d and %d tags"
msgstr ""

#: core/Requirement/Words_count.php:43
msgid "Between %d and %d words in content"
msgstr ""

#: core/Requirement/Categories_count.php:47
#, php-format
msgid "Between %s and %s categories"
msgstr ""

#: core/Legacy/LegacyPlugin.php:213 modules/settings/settings.php:444
msgid "Cheatin&#8217; uh?"
msgstr ""

#: modules/checklists/checklists.php:788 modules/checklists/assets/js/gutenberg-panel.jsx:242
msgid "Check Now"
msgstr ""

#: modules/checklists/checklists.php:669 modules/checklists/checklists.php:734
msgid "Checklist"
msgstr ""

#: core/Legacy/LegacyPlugin.php:349 modules/checklists/checklists.php:101
#: modules/checklists/checklists.php:887 modules/checklists/checklists.php:902
#: modules/checklists/assets/js/gutenberg-panel.jsx:197
#: modules/checklists/assets/js/gutenberg-panel.jsx:201
msgid "Checklists"
msgstr ""

#: modules/settings/settings.php:61 modules/settings/settings.php:112
msgid "Checklists Settings"
msgstr ""

#: core/Legacy/LegacyPlugin.php:206 modules/checklists/checklists.php:735
msgid "Configure"
msgstr ""

#: modules/checklists/templates/footer.php:37
msgid "Contact"
msgstr ""

#: core/Requirement/Custom_item.php:52 core/Requirement/Custom_item.php:53
msgid "Custom"
msgstr ""

#: modules/checklists/templates/global-checklists.php:87
msgid ""
"Custom tasks do not complete automatically. Users must check the box to show they have "
"completed the task."
msgstr ""

#: modules/permalinks/permalinks.php:66
msgid "Define tasks related to permalinks"
msgstr ""

#: modules/yoastseo/yoastseo.php:67
msgid "Define tasks related to Yoast SEO"
msgstr ""

#: modules/checklists/templates/global-checklists.php:75
msgid "Description"
msgstr ""

#: modules/settings/settings.php:700
msgid "Disable the \"Status\" option when using \"Quick Edit\":"
msgstr ""

#: modules/checklists/checklists.php:975
msgid "Disabled"
msgstr ""

#: modules/settings/settings.php:408
#, php-format
msgid "Disabled because add_post_type_support('%1$s', '%2$s') is included in a loaded file."
msgstr ""

#: modules/checklists/checklists.php:951
msgid "Disabled, Recommended or Required"
msgstr ""

#: modules/checklists/templates/footer.php:30
msgid "Documentation"
msgstr ""

#: modules/settings/settings.php:261
msgid "Enabled features"
msgstr ""

#: modules/settings/settings.php:781
msgid "Enter your API Key to use OpenAI prompts in checklist tasks."
msgstr ""

#: core/Requirement/Featured_image.php:35 core/Requirement/Featured_image.php:36
msgid "Featured image is added"
msgstr ""

#: modules/settings/settings.php:252
msgid "Features"
msgstr ""

#: modules/settings/settings.php:253
msgid "Feel free to select only the features you need."
msgstr ""

#: modules/settings/settings.php:675
msgid "General:"
msgstr ""

#: modules/yoastseo/lib/Requirement/Seo_Analysis.php:129
#: modules/yoastseo/lib/Requirement/Seo_Analysis.php:160
#: modules/yoastseo/lib/Requirement/Readability_Analysis.php:130
#: modules/yoastseo/lib/Requirement/Readability_Analysis.php:161
msgid "Good"
msgstr ""

#. Author URI of the plugin
msgid "https://publishpress.com"
msgstr ""

#. URI of the plugin
msgid "https://publishpress.com/"
msgstr ""

#: modules/settings/settings.php:762
msgid ""
"If the \"Status\" option is enabled, it can be used to avoid using the Checklists "
"requirements."
msgstr ""

#: core/Legacy/Module.php:288
#, php-format
msgid "If you like %s please leave us a %s rating. Thank you!"
msgstr ""

#: core/Requirement/Openai_item.php:386
msgid "Invalid response"
msgstr ""

#: modules/permalinks/lib/Requirement/ValidChars.php:37
msgid "Latin characters in permalink"
msgstr ""

#: core/Requirement/Base_counter.php:293
msgid "Max"
msgstr ""

#: core/Requirement/Taxonomies_count.php:46 core/Requirement/Taxonomies_count.php:47
msgid "Maximum of %d "
msgstr ""

#: core/Requirement/Filled_excerpt.php:39
msgid "Maximum of %d character in excerpt"
msgstr ""

#: core/Requirement/Title_count.php:37
msgid "Maximum of %d character in title"
msgstr ""

#: core/Requirement/Filled_excerpt.php:40
msgid "Maximum of %d characters in excerpt"
msgstr ""

#: core/Requirement/Title_count.php:38
msgid "Maximum of %d characters in title"
msgstr ""

#: core/Requirement/External_links.php:39
msgid "Maximum of %d external link in content"
msgstr ""

#: core/Requirement/External_links.php:40
msgid "Maximum of %d external links in content"
msgstr ""

#: core/Requirement/Internal_links.php:39
msgid "Maximum of %d internal link in content"
msgstr ""

#: core/Requirement/Internal_links.php:40
msgid "Maximum of %d internal links in content"
msgstr ""

#: core/Requirement/Tags_count.php:43
msgid "Maximum of %d tag"
msgstr ""

#: core/Requirement/Tags_count.php:44
msgid "Maximum of %d tags"
msgstr ""

#: core/Requirement/Words_count.php:39
msgid "Maximum of %d word in content"
msgstr ""

#: core/Requirement/Words_count.php:40
msgid "Maximum of %d words in content"
msgstr ""

#: core/Requirement/Categories_count.php:44
#, php-format
msgid "Maximum of %s categories"
msgstr ""

#: core/Requirement/Categories_count.php:43
#, php-format
msgid "Maximum of %s category"
msgstr ""

#: core/Requirement/Base_counter.php:285
msgid "Min"
msgstr ""

#: core/Requirement/Taxonomies_count.php:44 core/Requirement/Taxonomies_count.php:45
msgid "Minimum of %d "
msgstr ""

#: core/Requirement/Filled_excerpt.php:37
msgid "Minimum of %d character in excerpt"
msgstr ""

#: core/Requirement/Title_count.php:35
msgid "Minimum of %d character in title"
msgstr ""

#: core/Requirement/Filled_excerpt.php:38
msgid "Minimum of %d characters in excerpt"
msgstr ""

#: core/Requirement/Title_count.php:36
msgid "Minimum of %d characters in title"
msgstr ""

#: core/Requirement/External_links.php:37
msgid "Minimum of %d external link in content"
msgstr ""

#: core/Requirement/External_links.php:38
msgid "Minimum of %d external links in content"
msgstr ""

#: core/Requirement/Internal_links.php:37
msgid "Minimum of %d internal link in content"
msgstr ""

#: core/Requirement/Internal_links.php:38
msgid "Minimum of %d internal links in content"
msgstr ""

#: core/Requirement/Tags_count.php:41
msgid "Minimum of %d tag"
msgstr ""

#: core/Requirement/Tags_count.php:42
msgid "Minimum of %d tags"
msgstr ""

#: core/Requirement/Words_count.php:37
msgid "Minimum of %d word in content"
msgstr ""

#: core/Requirement/Words_count.php:38
msgid "Minimum of %d words in content"
msgstr ""

#: core/Requirement/Categories_count.php:42
#, php-format
msgid "Minimum of %s categories"
msgstr ""

#: core/Requirement/Categories_count.php:41
#, php-format
msgid "Minimum of %s category"
msgstr ""

#: modules/yoastseo/lib/Requirement/Seo_Analysis.php:46
msgid "Minimum Yoast SEO analysis score"
msgstr ""

#: modules/yoastseo/lib/Requirement/Readability_Analysis.php:47
msgid "Minimum Yoast SEO readability score"
msgstr ""

#: modules/checklists/templates/global-checklists.php:74
msgid "New Item"
msgstr ""

#: modules/checklists/checklists.php:790
msgid "No"
msgstr ""

#: modules/checklists/checklists.php:752 modules/checklists/checklists.php:756
msgid "Not required, but important: "
msgstr ""

#: core/Requirement/Taxonomies_count.php:43
msgid "Number of "
msgstr ""

#: core/Requirement/Categories_count.php:40
msgid "Number of categories"
msgstr ""

#: core/Requirement/Title_count.php:33 core/Requirement/Title_count.php:34
msgid "Number of characters in title"
msgstr ""

#: core/Requirement/External_links.php:36
msgid "Number of external links in content"
msgstr ""

#: core/Requirement/Internal_links.php:36
msgid "Number of internal links in content"
msgstr ""

#: core/Requirement/Tags_count.php:40
msgid "Number of tags"
msgstr ""

#: core/Requirement/Words_count.php:36
msgid "Number of words in content"
msgstr ""

#: core/Requirement/Filled_excerpt.php:35 core/Requirement/Filled_excerpt.php:36
msgid "Numbers of characters in excerpt"
msgstr ""

#: modules/yoastseo/lib/Requirement/Seo_Analysis.php:124
#: modules/yoastseo/lib/Requirement/Seo_Analysis.php:159
#: modules/yoastseo/lib/Requirement/Readability_Analysis.php:125
#: modules/yoastseo/lib/Requirement/Readability_Analysis.php:160
msgid "OK"
msgstr ""

#: modules/checklists/checklists.php:789
msgid "Ok"
msgstr ""

#: core/Requirement/Openai_item.php:61 core/Requirement/Openai_item.php:62
msgid "Openai"
msgstr ""

#: modules/settings/settings.php:708
msgid "OpenAI API Key:"
msgstr ""

#: core/Requirement/Openai_item.php:287
msgid "OpenAI tasks require an API Key. Please add your API Key in the Settings area."
msgstr ""

#: modules/checklists/checklists.php:952
msgid "Options"
msgstr ""

#: modules/permalinks/permalinks.php:65
msgid "Permalinks"
msgstr ""

#: modules/permissions/permissions.php:68
msgid "Permissions"
msgstr ""

#: modules/checklists/checklists.php:744
msgid "Please complete the following tasks before publishing:"
msgstr ""

#: modules/checklists/checklists.php:748
msgid "Please complete the following tasks before updating the published post:"
msgstr ""

#: modules/checklists/assets/js/gutenberg-panel.jsx:167
msgid "Please complete the required(*) checklists task."
msgstr ""

#: core/Legacy/LegacyPlugin.php:209
msgid "Please correct your form errors below and try again."
msgstr ""

#: modules/checklists/checklists.php:594
msgid "Please make sure to add a name for all the custom tasks."
msgstr ""

#: modules/checklists/checklists.php:590
msgid "Please make sure to complete the settings for"
msgstr ""

#: core/Requirement/Openai_item.php:278
msgid "Post content is empty."
msgstr ""

#: core/Legacy/LegacyPlugin.php:218
msgid "Post does not exist"
msgstr ""

#: core/Requirement/Prohibited_categories.php:43
msgid "Prohibited categories"
msgstr ""

#: core/Requirement/Prohibited_categories.php:42
msgid "Prohibited categories: %s"
msgstr ""

#: core/Requirement/Prohibited_tags.php:43
msgid "Prohibited tags"
msgstr ""

#: core/Requirement/Prohibited_tags.php:42
msgid "Prohibited tags: %s"
msgstr ""

#. Author of the plugin
msgid "PublishPress"
msgstr ""

#. Name of the plugin
#: core/Legacy/Module.php:285
msgid "PublishPress Checklists"
msgstr ""

#. %s expands to the readability score
#: modules/yoastseo/lib/Requirement/Readability_Analysis.php:124
#: modules/yoastseo/lib/Requirement/Readability_Analysis.php:129
#, php-format
msgid "Yoast Readability: %s"
msgstr ""

#: modules/checklists/checklists.php:976
msgid "Recommended"
msgstr ""

#: core/Requirement/Custom_item.php:121 core/Requirement/Openai_item.php:139
#: modules/checklists/checklists.php:602
msgid "Remove"
msgstr ""

#: modules/checklists/checklists.php:787 modules/checklists/checklists.php:977
msgid "Required"
msgstr ""

#: modules/checklists/assets/js/gutenberg-panel.jsx:255
msgid "required"
msgstr ""

#: core/Requirement/Required_categories.php:43
msgid "Required categories"
msgstr ""

#: core/Requirement/Required_categories.php:42
msgid "Required categories: %s"
msgstr ""

#: core/Requirement/Required_tags.php:43
msgid "Required tags"
msgstr ""

#: core/Requirement/Required_tags.php:42
msgid "Required tags: %s"
msgstr ""

#: modules/reviews/reviews.php:61
msgid "Reviews"
msgstr ""

#: modules/checklists/templates/global-checklists.php:108
msgid "Save Changes"
msgstr ""

#: core/Requirement/Openai_item.php:365
msgid "See the full response."
msgstr ""

#. %s expands to the seo score
#: modules/yoastseo/lib/Requirement/Seo_Analysis.php:123
#: modules/yoastseo/lib/Requirement/Seo_Analysis.php:128
#, php-format
msgid "Yoast SEO: %s"
msgstr ""

#: modules/settings/settings.php:114
msgid "Settings"
msgstr ""

#: modules/settings/settings.php:692
msgid "Show warning icon:"
msgstr ""

#: modules/checklists/checklists.php:950
msgid "Task"
msgstr ""

#: modules/checklists/templates/global-checklists.php:101
msgid "The prompt should be in form of a question."
msgstr ""

#: modules/settings/settings.php:312
msgid "There are no PublishPress modules registered"
msgstr ""

#: modules/checklists/templates/global-checklists.php:101
msgid "This feature requires an OpenAI API Key."
msgstr ""

#: modules/checklists/templates/global-checklists.php:101
msgid "No %s requirements for this post type."
msgstr ""

#: modules/settings/settings.php:741
msgid "This will display a warning icon in the \"Publish\" box."
msgstr ""

#: core/Requirement/Openai_item.php:273
msgid "Validation error. Kindly reload this page and try agai.n"
msgstr ""

#: modules/checklists/checklists.php:606
msgid "What's the expected OpenAI response to mark the requirement as pass?"
msgstr ""

#: core/Requirement/Custom_item.php:54 modules/checklists/checklists.php:598
msgid "Which roles can mark this task as complete?"
msgstr ""

#: modules/permissions/permissions.php:143
msgid "Who can ignore the task?"
msgstr ""

#: modules/checklists/checklists.php:791
msgid "Yes"
msgstr ""

#: modules/yoastseo/yoastseo.php:66
msgid "Yoast SEO"
msgstr ""

#: core/Legacy/LegacyPlugin.php:214
msgid "You do not have necessary permissions to complete this action."
msgstr ""

#: modules/checklists/checklists.php:783
#, php-format
msgid "You don't have to complete any %sChecklist tasks%s."
msgstr ""

#: modules/checklists/checklists.php:730 modules/checklists/assets/js/gutenberg-panel.jsx:208
msgid "You don't have to complete any Checklist tasks."
msgstr ""
