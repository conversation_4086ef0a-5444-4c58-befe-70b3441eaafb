# Translation of Plugins - PublishPress Checklists: Pre-Publishing Approval Task Checklist for WordPress Content - Stable (latest release) in French (France)
# This file is distributed under the same license as the Plugins - PublishPress Checklists: Pre-Publishing Approval Task Checklist for WordPress Content - Stable (latest release) package.
msgid ""
msgstr ""
"Project-Id-Version: Plugins - PublishPress Checklists: Pre-Publishing "
"Approval Task Checklist for WordPress Content - Stable (latest release)\n"
"POT-Creation-Date: 2024-10-15 21:56+0700\n"
"PO-Revision-Date: 2024-10-17 20:54+0000\n"
"Last-Translator: \n"
"Language-Team: French (France)\n"
"Language: fr_FR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: Loco https://localise.biz/\n"
"Report-Msgid-Bugs-To: \n"
"X-Loco-Version: 2.6.11; wp-6.6.2"

#: core/Requirement/Taxonomies_count.php:48
#: core/Requirement/Taxonomies_count.php:49
msgid "%d "
msgstr "%d "

#: core/Requirement/Filled_excerpt.php:41
msgid "%d character in excerpt"
msgstr "%d caractère dans l’extrait"

#: core/Requirement/Title_count.php:39
msgid "%d character in title"
msgstr "%d caractère dans le titre"

#: core/Requirement/Filled_excerpt.php:42
msgid "%d characters in excerpt"
msgstr "%d caractères dans l’extrait"

#: core/Requirement/Title_count.php:40
msgid "%d characters in title"
msgstr "%d caractères dans le titre"

#: core/Requirement/External_links.php:41
msgid "%d external link in content"
msgstr "%d lien externe dans le contenu"

#: core/Requirement/External_links.php:42
msgid "%d external links in content"
msgstr "%d liens externes dans le contenu"

#: core/Requirement/Internal_links.php:41
msgid "%d internal link in content"
msgstr "%d lien interne dans le contenu"

#: core/Requirement/Internal_links.php:42
msgid "%d internal links in content"
msgstr "%d liens internes dans le contenu"

#: core/Requirement/Tags_count.php:45
msgid "%d tag"
msgstr "%d étiquette"

#: core/Requirement/Tags_count.php:46
msgid "%d tags"
msgstr "%s étiquettes"

#: core/Requirement/Words_count.php:41
msgid "%d word in content"
msgstr "%d mot dans le contenu"

#: core/Requirement/Words_count.php:42
msgid "%d words in content"
msgstr "%d mots dans le contenu"

#: core/Requirement/Categories_count.php:46
#, php-format
msgid "%s categories"
msgstr "%s catégories"

#: core/Requirement/Categories_count.php:45
#, php-format
msgid "%s category"
msgstr "%s catégorie"

#: modules/checklists/templates/footer.php:23
msgid "About"
msgstr "À propos"

#: modules/checklists/checklists.php:953
msgid "Add custom task"
msgstr "Ajouter une tâche personnalisée"

#: modules/checklists/templates/global-checklists.php:94
msgid "Add OpenAI Prompt task"
msgstr "Ajoutez la tâche d’invite OpenAI"

#: modules/settings/settings.php:684
msgid "Add to these post types:"
msgstr "Ajouter à ces types de publication :"

#: core/Requirement/Image_alt.php:36 core/Requirement/Image_alt.php:37
msgid "All images have Alt text"
msgstr "Toutes les images contiennent un texte alternatif"

#: core/Requirement/Validate_links.php:58
#: core/Requirement/Validate_links.php:59
msgid "All links use a valid format"
msgstr "Tous les liens utilisent un format valide"

#: core/Requirement/Openai_item.php:266
msgid "An error occured."
msgstr "Une erreur s’est produite."

#: core/Requirement/Openai_item.php:340
#, php-format
msgid "API Error: %1s."
msgstr "Erreur API : %1s."

#: core/Requirement/Approved_by.php:41
#, php-format
msgid "Approved by %s"
msgstr "Approuvé par %s"

#: core/Requirement/Approved_by.php:42
msgid "Approved by a user in this role"
msgstr "Approuvé par un compte avec ce rôle"

#: modules/checklists/checklists.php:736
msgid "Are you sure you want to publish anyway?"
msgstr "Confirmez-vous vouloir publier de toute façon ?"

#: modules/checklists/checklists.php:740
msgid "Are you sure you want to update the published post anyway?"
msgstr ""
"Confirmez-vous vouloir tout de même mettre à jour la publication publiée ?"

#: core/Requirement/Taxonomies_count.php:50
msgid "Between %d and %d "
msgstr "Entre %d et %d "

#: core/Requirement/Filled_excerpt.php:43
msgid "Between %d and %d characters in excerpt"
msgstr "Entre %d et %d caractères dans l’extrait"

#: core/Requirement/Title_count.php:41
msgid "Between %d and %d characters in title"
msgstr "Entre %d et %d caractères dans le titre"

#: core/Requirement/External_links.php:43
msgid "Between %d and %d external links in content"
msgstr "Entre %d et %d liens externes dans le contenu"

#: core/Requirement/Internal_links.php:43
msgid "Between %d and %d internal links in content"
msgstr "Entre %d et %d liens internes dans le contenu"

#: core/Requirement/Tags_count.php:47
msgid "Between %d and %d tags"
msgstr "Entre les étiquettes %d et %d"

#: core/Requirement/Words_count.php:43
msgid "Between %d and %d words in content"
msgstr "Entre %d et %d mots dans le contenu"

#: core/Requirement/Categories_count.php:47
#, php-format
msgid "Between %s and %s categories"
msgstr "Entre %s et %s catégories"

#: core/Legacy/LegacyPlugin.php:213 modules/settings/settings.php:444
msgid "Cheatin&#8217; uh?"
msgstr "Une mauvaise manipulation ?"

#: modules/checklists/checklists.php:788
#: modules/checklists/assets/js/gutenberg-panel.jsx:242
msgid "Check Now"
msgstr "Vérifier maintenant"

#: modules/checklists/checklists.php:669 modules/checklists/checklists.php:734
msgid "Checklist"
msgstr "Liste de contrôle"

#: core/Legacy/LegacyPlugin.php:349 modules/checklists/checklists.php:101
#: modules/checklists/checklists.php:887 modules/checklists/checklists.php:902
#: modules/checklists/assets/js/gutenberg-panel.jsx:197
#: modules/checklists/assets/js/gutenberg-panel.jsx:201
msgid "Checklists"
msgstr "Listes de contrôle"

#: modules/settings/settings.php:61 modules/settings/settings.php:112
msgid "Checklists Settings"
msgstr "Réglages des listes de contrôle"

#: core/Legacy/LegacyPlugin.php:206 modules/checklists/checklists.php:735
msgid "Configure"
msgstr "Configurer"

#: modules/checklists/templates/footer.php:37
msgid "Contact"
msgstr "Contact"

#: core/Requirement/Custom_item.php:52 core/Requirement/Custom_item.php:53
msgid "Custom"
msgstr "Personnaliser"

#: modules/checklists/templates/global-checklists.php:87
msgid ""
"Custom tasks do not complete automatically. Users must check the box to show "
"they have completed the task."
msgstr ""
"Les tâches personnalisées ne se terminent pas automatiquement. Les "
"utilisateurs et les utilisatrices doivent cocher la case pour afficher "
"qu’ils ont terminé la tâche."

#: modules/permalinks/permalinks.php:66
msgid "Define tasks related to permalinks"
msgstr "Définir les tâches liées aux permaliens"

#: modules/yoastseo/yoastseo.php:67
msgid "Define tasks related to Yoast SEO"
msgstr "Definissez les tâches liées à Yoast SEO"

#: modules/checklists/templates/global-checklists.php:75
msgid "Description"
msgstr "Description"

#: modules/settings/settings.php:700
msgid "Disable the \"Status\" option when using \"Quick Edit\":"
msgstr ""
"Désactivez l’option « État » lorsque vous utilisez « Modification rapide » :"

#: modules/checklists/checklists.php:975
msgid "Disabled"
msgstr "Désactivée"

#: modules/settings/settings.php:408
#, php-format
msgid ""
"Disabled because add_post_type_support('%1$s', '%2$s') is included in a "
"loaded file."
msgstr ""
"Désactivé parce que add_post_type_support(’%1$s’, ’%2$s’) est inclus dans un "
"fichier chargé."

#: modules/checklists/checklists.php:951
msgid "Disabled, Recommended or Required"
msgstr "Désactivée, recommandée ou nécessaire"

#: modules/checklists/templates/footer.php:30
msgid "Documentation"
msgstr "Documentation"

#: modules/settings/settings.php:261
msgid "Enabled features"
msgstr "Activer les fonctionnalités"

#: modules/settings/settings.php:781
msgid "Enter your API Key to use OpenAI prompts in checklist tasks."
msgstr ""
"Saisissez votre clé API pour utiliser les invites OpenAI dans les tâches de "
"la liste de contrôle."

#: core/Requirement/Featured_image_alt.php:36
#: core/Requirement/Featured_image_alt.php:37
msgid "Featured image has Alt text"
msgstr "L'image sélectionnée contient un texte alternatif"

#: core/Requirement/Featured_image.php:35
#: core/Requirement/Featured_image.php:36
msgid "Featured image is added"
msgstr "L'image sélectionnée est ajoutée"

#: modules/settings/settings.php:252
msgid "Features"
msgstr "Fonctionnalités"

#: modules/settings/settings.php:253
msgid "Feel free to select only the features you need."
msgstr ""
"N’hésitez pas à sélectionner uniquement les fonctionnalités dont vous avez "
"besoin."

#: modules/settings/settings.php:675
msgid "General:"
msgstr "Général :"

#: modules/yoastseo/lib/Requirement/Seo_Analysis.php:129
#: modules/yoastseo/lib/Requirement/Seo_Analysis.php:160
#: modules/yoastseo/lib/Requirement/Readability_Analysis.php:130
#: modules/yoastseo/lib/Requirement/Readability_Analysis.php:161
msgid "Good"
msgstr "Bon"

#. Author URI of the plugin
msgid "https://publishpress.com"
msgstr "https://publishpress.com"

#. URI of the plugin
msgid "https://publishpress.com/"
msgstr "https://publishpress.com/"

#: modules/settings/settings.php:762
msgid ""
"If the \"Status\" option is enabled, it can be used to avoid using the "
"Checklists requirements."
msgstr ""
"Si l’option « État » est activée, elle peut être utilisée pour éviter "
"d’avoir recours aux prérequis des listes de contrôle."

#: core/Legacy/Module.php:288
#, php-format
msgid "If you like %s please leave us a %s rating. Thank you!"
msgstr "Si vous appréciez %s, veuillez nous laisser une note de %s. Merci !"

#: core/Requirement/Openai_item.php:386
msgid "Invalid response"
msgstr "Réponse non valide"

#: modules/permalinks/lib/Requirement/ValidChars.php:37
msgid "Latin characters in permalink"
msgstr "Caractères latins dans le permalien"

#: core/Requirement/Base_counter.php:293
msgid "Max"
msgstr "Max"

#: core/Requirement/Taxonomies_count.php:46
#: core/Requirement/Taxonomies_count.php:47
msgid "Maximum of %d "
msgstr "Maximum de %d "

#: core/Requirement/Filled_excerpt.php:39
msgid "Maximum of %d character in excerpt"
msgstr "Maximum d‘%d caractères dans l’extrait"

#: core/Requirement/Title_count.php:37
msgid "Maximum of %d character in title"
msgstr "Maximum d‘%d caractère dans le titre"

#: core/Requirement/Filled_excerpt.php:40
msgid "Maximum of %d characters in excerpt"
msgstr "Maximum de %d caractères dans l’extrait"

#: core/Requirement/Title_count.php:38
msgid "Maximum of %d characters in title"
msgstr "Maximum de %d caractères dans le titre"

#: core/Requirement/External_links.php:39
msgid "Maximum of %d external link in content"
msgstr "Maximum d‘%d lien externe dans le contenu"

#: core/Requirement/External_links.php:40
msgid "Maximum of %d external links in content"
msgstr "Maximum de %d liens externes dans le contenu"

#: core/Requirement/Internal_links.php:39
msgid "Maximum of %d internal link in content"
msgstr "Maximum d‘%d lien interne dans le contenu"

#: core/Requirement/Internal_links.php:40
msgid "Maximum of %d internal links in content"
msgstr "Maximum de %d liens internes dans le contenu"

#: core/Requirement/Tags_count.php:43
msgid "Maximum of %d tag"
msgstr "Maximum d‘%d étiquette"

#: core/Requirement/Tags_count.php:44
msgid "Maximum of %d tags"
msgstr "Maximum de %d étiquettes"

#: core/Requirement/Words_count.php:39
msgid "Maximum of %d word in content"
msgstr "Maximum d‘%d mot dans le contenu"

#: core/Requirement/Words_count.php:40
msgid "Maximum of %d words in content"
msgstr "Maximum de %d mots dans le contenu"

#: core/Requirement/Categories_count.php:44
#, php-format
msgid "Maximum of %s categories"
msgstr "Maximum de %s catégories"

#: core/Requirement/Categories_count.php:43
#, php-format
msgid "Maximum of %s category"
msgstr "Maximum de %s catégories"

#: core/Requirement/Base_counter.php:285
msgid "Min"
msgstr "Min"

#: core/Requirement/Taxonomies_count.php:44
#: core/Requirement/Taxonomies_count.php:45
msgid "Minimum of %d "
msgstr "Minimum de %d "

#: core/Requirement/Filled_excerpt.php:37
msgid "Minimum of %d character in excerpt"
msgstr "Minimum d‘%d caractère dans l’extrait"

#: core/Requirement/Title_count.php:35
msgid "Minimum of %d character in title"
msgstr "Minimum d‘%d caractère dans le titre"

#: core/Requirement/Filled_excerpt.php:38
msgid "Minimum of %d characters in excerpt"
msgstr "Minimum de %d caractères dans l’extrait"

#: core/Requirement/Title_count.php:36
msgid "Minimum of %d characters in title"
msgstr "Minimum de %d caractères dans le titre"

#: core/Requirement/External_links.php:37
msgid "Minimum of %d external link in content"
msgstr "Minimum d‘%d lien externe dans le contenu"

#: core/Requirement/External_links.php:38
msgid "Minimum of %d external links in content"
msgstr "Minimum de %d liens externes dans le contenu"

#: core/Requirement/Internal_links.php:37
msgid "Minimum of %d internal link in content"
msgstr "Minimum d‘%d lien interne dans le contenu"

#: core/Requirement/Internal_links.php:38
msgid "Minimum of %d internal links in content"
msgstr "Minimum de %d liens internes dans le contenu"

#: core/Requirement/Tags_count.php:41
msgid "Minimum of %d tag"
msgstr "Minimum d‘%d étiquette"

#: core/Requirement/Tags_count.php:42
msgid "Minimum of %d tags"
msgstr "Minimum de %d étiquettes"

#: core/Requirement/Words_count.php:37
msgid "Minimum of %d word in content"
msgstr "Minimum d‘%d mot dans le contenu"

#: core/Requirement/Words_count.php:38
msgid "Minimum of %d words in content"
msgstr "Minimum de %d mots dans le contenu"

#: core/Requirement/Categories_count.php:42
#, php-format
msgid "Minimum of %s categories"
msgstr "Minimum de %s catégories"

#: core/Requirement/Categories_count.php:41
#, php-format
msgid "Minimum of %s category"
msgstr "Minimum de %s catégorie"

#: modules/yoastseo/lib/Requirement/Seo_Analysis.php:46
msgid "Minimum Yoast SEO analysis score"
msgstr "Score minimum d’analyse Yoast SEO"

#: modules/yoastseo/lib/Requirement/Readability_Analysis.php:47
msgid "Minimum Yoast SEO readability score"
msgstr "Score minimum de lisibilité Yoast SEO"

#: modules/checklists/templates/global-checklists.php:74
msgid "New Item"
msgstr "Nouvel élément"

#: modules/checklists/checklists.php:790
msgid "No"
msgstr "Non"

#: modules/checklists/templates/global-checklists.php:101
msgid "No %s requirements for this post type."
msgstr "Aucune exigence %s pour ce type de poste."

#: modules/checklists/checklists.php:752 modules/checklists/checklists.php:756
msgid "Not required, but important: "
msgstr "Non nécessaire, mais importante : "

#: core/Requirement/Taxonomies_count.php:43
msgid "Number of "
msgstr "Nombre de "

#: core/Requirement/Categories_count.php:40
msgid "Number of categories"
msgstr "Nombre de catégories"

#: core/Requirement/Image_alt_count.php:42
#: core/Requirement/Image_alt_count.php:43
msgid "Number of characters in Alt text"
msgstr "Nombre de caractères dans le texte alternatif"

#: core/Requirement/Title_count.php:33 core/Requirement/Title_count.php:34
msgid "Number of characters in title"
msgstr "Nombre de caractères dans le titre"

#: core/Requirement/External_links.php:36
msgid "Number of external links in content"
msgstr "Nombre de liens externes dans le contenu"

#: core/Requirement/Internal_links.php:36
msgid "Number of internal links in content"
msgstr "Nombre de liens internes dans le contenu"

#: core/Requirement/Tags_count.php:40
msgid "Number of tags"
msgstr "Nombre d’étiquettes"

#: core/Requirement/Words_count.php:36
msgid "Number of words in content"
msgstr "Nombre de mots dans le contenu"

#: core/Requirement/Filled_excerpt.php:35
#: core/Requirement/Filled_excerpt.php:36
msgid "Numbers of characters in excerpt"
msgstr "Nombre de caractères dans l’extrait"

#: modules/yoastseo/lib/Requirement/Seo_Analysis.php:124
#: modules/yoastseo/lib/Requirement/Seo_Analysis.php:159
#: modules/yoastseo/lib/Requirement/Readability_Analysis.php:125
#: modules/yoastseo/lib/Requirement/Readability_Analysis.php:160
msgid "OK"
msgstr "OK"

#: modules/checklists/checklists.php:789
msgid "Ok"
msgstr "Ok"

#: core/Requirement/Openai_item.php:61 core/Requirement/Openai_item.php:62
msgid "Openai"
msgstr "Openai"

#: modules/settings/settings.php:708
msgid "OpenAI API Key:"
msgstr "Clé API de OpenAI :"

#: core/Requirement/Openai_item.php:287
msgid ""
"OpenAI tasks require an API Key. Please add your API Key in the Settings "
"area."
msgstr ""
"Les tâches de OpenAI nécessitent une clé API. Veuillez ajouter votre clé API "
"dans les réglages."

#: modules/checklists/checklists.php:952
msgid "Options"
msgstr "Options"

#: modules/permalinks/permalinks.php:65
msgid "Permalinks"
msgstr "Permaliens"

#: modules/permissions/permissions.php:68
msgid "Permissions"
msgstr "Droits"

#: modules/checklists/checklists.php:744
msgid "Please complete the following tasks before publishing:"
msgstr "Veuillez accomplir les tâches suivantes avant de publier :"

#: modules/checklists/checklists.php:748
msgid "Please complete the following tasks before updating the published post:"
msgstr ""
"Veuillez effectuer les tâches suivantes avant de mettre à jour la "
"publication publiée :"

#: modules/checklists/assets/js/gutenberg-panel.jsx:167
msgid "Please complete the required(*) checklists task."
msgstr "Veuillez compléter les tâches nécessaire (*) de listes de contrôle."

#: core/Legacy/LegacyPlugin.php:209
msgid "Please correct your form errors below and try again."
msgstr ""
"Veuillez corriger les erreurs de votre formulaire ci-dessous et réessayer."

#: modules/checklists/checklists.php:594
msgid "Please make sure to add a name for all the custom tasks."
msgstr ""
"Veuillez vous assurer d’ajouter un nom pour toutes les tâches personnalisées."

#: modules/checklists/checklists.php:590
msgid "Please make sure to complete the settings for"
msgstr "Veuillez vous assurez-vous d’effectuer tous les réglages pour"

#: core/Requirement/Openai_item.php:278
msgid "Post content is empty."
msgstr "Le contenu de la publication est vide."

#: core/Legacy/LegacyPlugin.php:218
msgid "Post does not exist"
msgstr "La publication n’existe pas"

#: core/Requirement/Prohibited_categories.php:43
msgid "Prohibited categories"
msgstr "Catégories interdites"

#: core/Requirement/Prohibited_categories.php:42
msgid "Prohibited categories: %s"
msgstr "Catégories interdites: %s"

#: core/Requirement/Prohibited_tags.php:43
msgid "Prohibited tags"
msgstr "Étiquettes interdites"

#: core/Requirement/Prohibited_tags.php:42
msgid "Prohibited tags: %s"
msgstr "Étiquettes interdites : %s"

#. Author of the plugin
msgid "PublishPress"
msgstr "PublishPress"

#. Name of the plugin
#: core/Legacy/Module.php:285
msgid "PublishPress Checklists"
msgstr "PublishPress Checklists"

#: modules/checklists/checklists.php:976
msgid "Recommended"
msgstr "Recommandée"

#: core/Requirement/Custom_item.php:121 core/Requirement/Openai_item.php:139
#: modules/checklists/checklists.php:602
msgid "Remove"
msgstr "Retirer"

#: modules/checklists/checklists.php:787 modules/checklists/checklists.php:977
msgid "Required"
msgstr "Obligatoire"

#: modules/checklists/assets/js/gutenberg-panel.jsx:255
msgid "required"
msgstr "obligatoire"

#: core/Requirement/Required_categories.php:43
msgid "Required categories"
msgstr "Catégories obligatoires"

#: core/Requirement/Required_categories.php:42
msgid "Required categories: %s"
msgstr "Catégories obligatoires : %s"

#: core/Requirement/Required_tags.php:43
msgid "Required tags"
msgstr "Étiquettes obligatoires"

#: core/Requirement/Required_tags.php:42
msgid "Required tags: %s"
msgstr "Étiquettes obligatoires : %s"

#: modules/reviews/reviews.php:61
msgid "Reviews"
msgstr "Avis"

#: modules/checklists/templates/global-checklists.php:108
msgid "Save Changes"
msgstr "Enregistrer les modifications"

#: core/Requirement/Openai_item.php:365
msgid "See the full response."
msgstr "Voir la réponse complète."

#: modules/settings/settings.php:114
msgid "Settings"
msgstr "Réglages"

#: modules/settings/settings.php:692
msgid "Show warning icon:"
msgstr "Afficher une icône d’avertissement :"

#: modules/checklists/checklists.php:950
msgid "Task"
msgstr "Tâche"

#: modules/checklists/templates/global-checklists.php:101
msgid "The prompt should be in form of a question."
msgstr "L’invite doit avoir la forme d’une question."

#: modules/settings/settings.php:312
msgid "There are no PublishPress modules registered"
msgstr "Il n’y a aucun module PublishPress enregistré"

#: modules/checklists/templates/global-checklists.php:101
msgid "This feature requires an OpenAI API Key."
msgstr "Cette fonctionnalité nécessite une clé API OpenAI."

#: modules/settings/settings.php:741
msgid "This will display a warning icon in the \"Publish\" box."
msgstr "Ceci affichera une icône d’avertissement dans la boîte « Publier »"

#: core/Requirement/Openai_item.php:273
msgid "Validation error. Kindly reload this page and try agai.n"
msgstr "Erreur de validation. Veuillez recharger cette page et réessayer."

#: modules/checklists/checklists.php:606
msgid "What's the expected OpenAI response to mark the requirement as pass?"
msgstr ""
"Quelle est la réponse attendue de l’OpenAI pour marquer le prérequis comme "
"réussie ?"

#: core/Requirement/Custom_item.php:54 modules/checklists/checklists.php:598
msgid "Which roles can mark this task as complete?"
msgstr "Quels rôles peuvent marquer cette tâche comme étant terminée ?"

#: modules/permissions/permissions.php:143
msgid "Who can ignore the task?"
msgstr "Qui peut ignorer la tâche ?"

#. Description of the plugin
msgid ""
"With PublishPress Checklists, you can choose publishing requirements for "
"your content."
msgstr ""
"Avec les listes de contrôle PublishPress, vous pouvez choisir les exigences "
"de publication pour votre contenu."

#: modules/checklists/checklists.php:791
msgid "Yes"
msgstr "Oui"

#. %s expands to the readability score
#: modules/yoastseo/lib/Requirement/Readability_Analysis.php:124
#: modules/yoastseo/lib/Requirement/Readability_Analysis.php:129
#, php-format
msgid "Yoast Readability: %s"
msgstr "Lisibilité Yoast : %s"

#: modules/yoastseo/yoastseo.php:66
msgid "Yoast SEO"
msgstr "Yoast SEO"

#. %s expands to the seo score
#: modules/yoastseo/lib/Requirement/Seo_Analysis.php:123
#: modules/yoastseo/lib/Requirement/Seo_Analysis.php:128
#, php-format
msgid "Yoast SEO: %s"
msgstr "Yoast SEO : %s"

#: core/Legacy/LegacyPlugin.php:214
msgid "You do not have necessary permissions to complete this action."
msgstr "Vous n’avez pas les droits nécessaires pour réaliser cette action."

#: modules/checklists/checklists.php:783
#, php-format
msgid "You don't have to complete any %sChecklist tasks%s."
msgstr "Vous n’avez pas à remplir de %stâche de la liste de contrôle%s."

#: modules/checklists/checklists.php:730
#: modules/checklists/assets/js/gutenberg-panel.jsx:208
msgid "You don't have to complete any Checklist tasks."
msgstr "Vous n’avez pas besoin d’accomplir les tâches de la liste de contrôle."
