# Translation of Plugins - PublishPress Checklists: Pre-Publishing Approval Task Checklist for WordPress Content - Stable (latest release) in Italian
# This file is distributed under the same license as the Plugins - PublishPress Checklists: Pre-Publishing Approval Task Checklist for WordPress Content - Stable (latest release) package.
msgid ""
msgstr ""
"Project-Id-Version: Plugins - PublishPress Checklists: Pre-Publishing "
"Approval Task Checklist for WordPress Content - Stable (latest release)\n"
"POT-Creation-Date: 2024-10-15 21:56+0700\n"
"PO-Revision-Date: 2024-10-17 20:52+0000\n"
"Last-Translator: \n"
"Language-Team: Italian\n"
"Language: it_IT\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Loco https://localise.biz/\n"
"Report-Msgid-Bugs-To: \n"
"X-Loco-Version: 2.6.11; wp-6.6.2"

#: core/Requirement/Taxonomies_count.php:48
#: core/Requirement/Taxonomies_count.php:49
msgid "%d "
msgstr "%d "

#: core/Requirement/Filled_excerpt.php:41
msgid "%d character in excerpt"
msgstr "%d carattere nel riassunto"

#: core/Requirement/Title_count.php:39
msgid "%d character in title"
msgstr "%d carattere nel titolo"

#: core/Requirement/Filled_excerpt.php:42
msgid "%d characters in excerpt"
msgstr "%d caratteri nel riassunto"

#: core/Requirement/Title_count.php:40
msgid "%d characters in title"
msgstr "%d caratteri nel titolo"

#: core/Requirement/External_links.php:41
msgid "%d external link in content"
msgstr "%d link esterno nel contenuto"

#: core/Requirement/External_links.php:42
msgid "%d external links in content"
msgstr "%d link esterni nel contenuto"

#: core/Requirement/Internal_links.php:41
msgid "%d internal link in content"
msgstr "%d link interno nel contenuto"

#: core/Requirement/Internal_links.php:42
msgid "%d internal links in content"
msgstr "%d link interni nel contenuto"

#: core/Requirement/Tags_count.php:45
msgid "%d tag"
msgstr "%d tag"

#: core/Requirement/Tags_count.php:46
msgid "%d tags"
msgstr "%d tags"

#: core/Requirement/Words_count.php:41
msgid "%d word in content"
msgstr "%d parola nel contenuto"

#: core/Requirement/Words_count.php:42
msgid "%d words in content"
msgstr "%d parole nel contenuto"

#: core/Requirement/Categories_count.php:46
#, php-format
msgid "%s categories"
msgstr "%s categorie"

#: core/Requirement/Categories_count.php:45
#, php-format
msgid "%s category"
msgstr "%s categoria"

#: modules/checklists/templates/footer.php:23
msgid "About"
msgstr "Chi siamo"

#: modules/checklists/checklists.php:953
msgid "Add custom task"
msgstr "Aggiungi attività personalizzata"

#: modules/checklists/templates/global-checklists.php:94
msgid "Add OpenAI Prompt task"
msgstr "Aggiungi attività prompt OpenAI"

#: modules/settings/settings.php:684
msgid "Add to these post types:"
msgstr "Aggiungi a questi tipi di articoli:"

#: core/Requirement/Image_alt.php:36 core/Requirement/Image_alt.php:37
msgid "All images have Alt text"
msgstr "Tutte le immagini hanno un testo Alt"

#: core/Requirement/Validate_links.php:58
#: core/Requirement/Validate_links.php:59
msgid "All links use a valid format"
msgstr "Tutti i link utilizzano un formato valido"

#: core/Requirement/Openai_item.php:266
msgid "An error occured."
msgstr "Si è verificato un errore."

#: core/Requirement/Openai_item.php:340
#, php-format
msgid "API Error: %1s."
msgstr "Errore API: %1s."

#: core/Requirement/Approved_by.php:41
#, php-format
msgid "Approved by %s"
msgstr "Approvato da %s"

#: core/Requirement/Approved_by.php:42
msgid "Approved by a user in this role"
msgstr "Approvato da un utente in questo ruolo"

#: modules/checklists/checklists.php:736
msgid "Are you sure you want to publish anyway?"
msgstr "Sei certo di voler pubblicare comunque?"

#: modules/checklists/checklists.php:740
msgid "Are you sure you want to update the published post anyway?"
msgstr "Se certo di voler aggiornare in qualsiasi caso l'articolo pubblicato?"

#: core/Requirement/Taxonomies_count.php:50
msgid "Between %d and %d "
msgstr "Tra %d e %d "

#: core/Requirement/Filled_excerpt.php:43
msgid "Between %d and %d characters in excerpt"
msgstr "Tra %d e %d caratteri nel riassunto"

#: core/Requirement/Title_count.php:41
msgid "Between %d and %d characters in title"
msgstr "Tra %d e %d caratteri nel titolo"

#: core/Requirement/External_links.php:43
msgid "Between %d and %d external links in content"
msgstr "Tra %d e %d link esterni nel contenuto"

#: core/Requirement/Internal_links.php:43
msgid "Between %d and %d internal links in content"
msgstr "Tra %d e %d link interni nel contenuto"

#: core/Requirement/Tags_count.php:47
msgid "Between %d and %d tags"
msgstr "Tra %d e %d tag"

#: core/Requirement/Words_count.php:43
msgid "Between %d and %d words in content"
msgstr "Tra %d e %d parole nel contenuto"

#: core/Requirement/Categories_count.php:47
#, php-format
msgid "Between %s and %s categories"
msgstr "Tra %s e %s categorie"

#: core/Legacy/LegacyPlugin.php:213 modules/settings/settings.php:444
msgid "Cheatin&#8217; uh?"
msgstr "Si tenta di fare i furbi, eh?"

#: modules/checklists/checklists.php:788
#: modules/checklists/assets/js/gutenberg-panel.jsx:242
msgid "Check Now"
msgstr "Controlla adesso"

#: modules/checklists/checklists.php:669 modules/checklists/checklists.php:734
msgid "Checklist"
msgstr "Lista di controllo"

#: core/Legacy/LegacyPlugin.php:349 modules/checklists/checklists.php:101
#: modules/checklists/checklists.php:887 modules/checklists/checklists.php:902
#: modules/checklists/assets/js/gutenberg-panel.jsx:197
#: modules/checklists/assets/js/gutenberg-panel.jsx:201
msgid "Checklists"
msgstr "Liste di controllo"

#: modules/settings/settings.php:61 modules/settings/settings.php:112
msgid "Checklists Settings"
msgstr "Impostazioni Liste di controllo"

#: core/Legacy/LegacyPlugin.php:206 modules/checklists/checklists.php:735
msgid "Configure"
msgstr "Configura"

#: modules/checklists/templates/footer.php:37
msgid "Contact"
msgstr "Contatto"

#: core/Requirement/Custom_item.php:52 core/Requirement/Custom_item.php:53
msgid "Custom"
msgstr "Personalizzato"

#: modules/checklists/templates/global-checklists.php:87
msgid ""
"Custom tasks do not complete automatically. Users must check the box to show "
"they have completed the task."
msgstr ""
"Le attività personalizzate non hanno il completamento automatico. Gli utenti "
"devono spuntare questa casella di verifica per mostrare che hanno ultimato "
"l'attività."

#: modules/permalinks/permalinks.php:66
msgid "Define tasks related to permalinks"
msgstr "Definisci le attività relative ai permalink"

#: modules/yoastseo/yoastseo.php:67
msgid "Define tasks related to Yoast SEO"
msgstr "Definisci le attività correlate a Yoast SEO"

#: modules/checklists/templates/global-checklists.php:75
msgid "Description"
msgstr "Descrizione"

#: modules/settings/settings.php:700
msgid "Disable the \"Status\" option when using \"Quick Edit\":"
msgstr ""
"Disabilita l'opzione \"Stato\" mentre stai usando la \"Modifica rapida\":"

#: modules/checklists/checklists.php:975
msgid "Disabled"
msgstr "Disabilitato"

#: modules/settings/settings.php:408
#, php-format
msgid ""
"Disabled because add_post_type_support('%1$s', '%2$s') is included in a "
"loaded file."
msgstr ""
"Disabilitato perché add_post_type_support('%1$s', '%2$s') è incluso in un "
"file caricato."

#: modules/checklists/checklists.php:951
msgid "Disabled, Recommended or Required"
msgstr "Disabilitato, Raccomandato o Richiesto"

#: modules/checklists/templates/footer.php:30
msgid "Documentation"
msgstr "Documentazione"

#: modules/settings/settings.php:261
msgid "Enabled features"
msgstr "Funzionalità abilitate"

#: modules/settings/settings.php:781
msgid "Enter your API Key to use OpenAI prompts in checklist tasks."
msgstr ""
"Inserisci la tua chiave API per utilizzare i prompt di OpenAI nelle attività "
"delle liste di controllo."

#: core/Requirement/Featured_image_alt.php:36
#: core/Requirement/Featured_image_alt.php:37
msgid "Featured image has Alt text"
msgstr "L'immagine in evidenza ha testo alternativo"

#: core/Requirement/Featured_image.php:35
#: core/Requirement/Featured_image.php:36
msgid "Featured image is added"
msgstr "L'immagine in evidenza è stata aggiunta"

#: modules/settings/settings.php:252
msgid "Features"
msgstr "Funzionalità"

#: modules/settings/settings.php:253
msgid "Feel free to select only the features you need."
msgstr "Puoi selezionare anche solo le funzionalità di cui hai bisogno."

#: modules/settings/settings.php:675
msgid "General:"
msgstr "Generale:"

#: modules/yoastseo/lib/Requirement/Seo_Analysis.php:129
#: modules/yoastseo/lib/Requirement/Seo_Analysis.php:160
#: modules/yoastseo/lib/Requirement/Readability_Analysis.php:130
#: modules/yoastseo/lib/Requirement/Readability_Analysis.php:161
msgid "Good"
msgstr "Buono"

#. Author URI of the plugin
msgid "https://publishpress.com"
msgstr "https://publishpress.com"

#. URI of the plugin
msgid "https://publishpress.com/"
msgstr "https://publishpress.com/"

#: modules/settings/settings.php:762
msgid ""
"If the \"Status\" option is enabled, it can be used to avoid using the "
"Checklists requirements."
msgstr ""
"Se l'opzione \"Stato\" è abilitata, può essere utilizzata per evitare di "
"utilizzare i requisiti delle liste di controllo."

#: core/Legacy/Module.php:288
#, php-format
msgid "If you like %s please leave us a %s rating. Thank you!"
msgstr "Se sei soddisfatto di %s, lasciaci una valutazione di %s!"

#: core/Requirement/Openai_item.php:386
msgid "Invalid response"
msgstr "Risposta non valida"

#: modules/permalinks/lib/Requirement/ValidChars.php:37
msgid "Latin characters in permalink"
msgstr "Caratteri latini nel permalink"

#: core/Requirement/Base_counter.php:293
msgid "Max"
msgstr "Massimo"

#: core/Requirement/Taxonomies_count.php:46
#: core/Requirement/Taxonomies_count.php:47
msgid "Maximum of %d "
msgstr "Massimo di %d "

#: core/Requirement/Filled_excerpt.php:39
msgid "Maximum of %d character in excerpt"
msgstr "Massimo di %d carattere nel riassunto"

#: core/Requirement/Title_count.php:37
msgid "Maximum of %d character in title"
msgstr "Massimo di %d carattere nel titolo"

#: core/Requirement/Filled_excerpt.php:40
msgid "Maximum of %d characters in excerpt"
msgstr "Massimo di %d caratteri nel riassunto"

#: core/Requirement/Title_count.php:38
msgid "Maximum of %d characters in title"
msgstr "Massimo di %d caratteri nel titolo"

#: core/Requirement/External_links.php:39
msgid "Maximum of %d external link in content"
msgstr "Massimo di %d link esterno nel contenuto"

#: core/Requirement/External_links.php:40
msgid "Maximum of %d external links in content"
msgstr "Massimo di %d link esterni nel contenuto"

#: core/Requirement/Internal_links.php:39
msgid "Maximum of %d internal link in content"
msgstr "Massimo di %d link interno nel contenuto"

#: core/Requirement/Internal_links.php:40
msgid "Maximum of %d internal links in content"
msgstr "Massimo di %d link interni nel contenuto"

#: core/Requirement/Tags_count.php:43
msgid "Maximum of %d tag"
msgstr "Massimo di %d tag"

#: core/Requirement/Tags_count.php:44
msgid "Maximum of %d tags"
msgstr "Massimo di %d tag"

#: core/Requirement/Words_count.php:39
msgid "Maximum of %d word in content"
msgstr "Massimo di %d parola nel contenuto"

#: core/Requirement/Words_count.php:40
msgid "Maximum of %d words in content"
msgstr "Massimo di %d parole nel contenuto"

#: core/Requirement/Categories_count.php:44
#, php-format
msgid "Maximum of %s categories"
msgstr "Massimo di %s categorie"

#: core/Requirement/Categories_count.php:43
#, php-format
msgid "Maximum of %s category"
msgstr "Massimo di %s categoria"

#: core/Requirement/Base_counter.php:285
msgid "Min"
msgstr "Minimo"

#: core/Requirement/Taxonomies_count.php:44
#: core/Requirement/Taxonomies_count.php:45
msgid "Minimum of %d "
msgstr "Minimo di %d "

#: core/Requirement/Filled_excerpt.php:37
msgid "Minimum of %d character in excerpt"
msgstr "Minimo di %d carattere nel riassunto"

#: core/Requirement/Title_count.php:35
msgid "Minimum of %d character in title"
msgstr "Minimo di %d carattere nel titolo"

#: core/Requirement/Filled_excerpt.php:38
msgid "Minimum of %d characters in excerpt"
msgstr "Minimo di %d caratteri nel riassunto"

#: core/Requirement/Title_count.php:36
msgid "Minimum of %d characters in title"
msgstr "Minimo di %d caratteri nel titolo"

#: core/Requirement/External_links.php:37
msgid "Minimum of %d external link in content"
msgstr "Minimo di %d link esterno nel contenuto"

#: core/Requirement/External_links.php:38
msgid "Minimum of %d external links in content"
msgstr "Minimo di %d link esterni nel contenuto"

#: core/Requirement/Internal_links.php:37
msgid "Minimum of %d internal link in content"
msgstr "Minimo di %d link interno nel contenuto"

#: core/Requirement/Internal_links.php:38
msgid "Minimum of %d internal links in content"
msgstr "Minimo di %d link interni nel contenuto"

#: core/Requirement/Tags_count.php:41
msgid "Minimum of %d tag"
msgstr "Minimo di %d tag"

#: core/Requirement/Tags_count.php:42
msgid "Minimum of %d tags"
msgstr "Minimo di %d tag"

#: core/Requirement/Words_count.php:37
msgid "Minimum of %d word in content"
msgstr "Minimo di %d parola nel contenuto"

#: core/Requirement/Words_count.php:38
msgid "Minimum of %d words in content"
msgstr "Minimo di %d parole nel contenuto"

#: core/Requirement/Categories_count.php:42
#, php-format
msgid "Minimum of %s categories"
msgstr "Minimo di %s categorie"

#: core/Requirement/Categories_count.php:41
#, php-format
msgid "Minimum of %s category"
msgstr "Minimo di %s categoria"

#: modules/yoastseo/lib/Requirement/Seo_Analysis.php:46
msgid "Minimum Yoast SEO analysis score"
msgstr "Punteggio minimo di analisi Yoast SEO"

#: modules/yoastseo/lib/Requirement/Readability_Analysis.php:47
msgid "Minimum Yoast SEO readability score"
msgstr "Punteggio minimo di leggibilità Yoast SEO"

#: modules/checklists/templates/global-checklists.php:74
msgid "New Item"
msgstr "Nuovo elemento"

#: modules/checklists/checklists.php:790
msgid "No"
msgstr "No"

#: modules/checklists/templates/global-checklists.php:101
msgid "No %s requirements for this post type."
msgstr "Nessun requisito %s per questo tipo di post."

#: modules/checklists/checklists.php:752 modules/checklists/checklists.php:756
msgid "Not required, but important: "
msgstr "Non richiesto, ma importante: "

#: core/Requirement/Taxonomies_count.php:43
msgid "Number of "
msgstr "Numero di "

#: core/Requirement/Categories_count.php:40
msgid "Number of categories"
msgstr "Numero di categorie"

#: core/Requirement/Image_alt_count.php:42
#: core/Requirement/Image_alt_count.php:43
msgid "Number of characters in Alt text"
msgstr "Numero di caratteri nel testo alternativo"

#: core/Requirement/Title_count.php:33 core/Requirement/Title_count.php:34
msgid "Number of characters in title"
msgstr "Numero di caratteri nel titolo"

#: core/Requirement/External_links.php:36
msgid "Number of external links in content"
msgstr "Numero di link esterni nel contenuto"

#: core/Requirement/Internal_links.php:36
msgid "Number of internal links in content"
msgstr "Numero di link interni nel contenuto"

#: core/Requirement/Tags_count.php:40
msgid "Number of tags"
msgstr "Numero di tag"

#: core/Requirement/Words_count.php:36
msgid "Number of words in content"
msgstr "Numero di parole nel contenuto"

#: core/Requirement/Filled_excerpt.php:35
#: core/Requirement/Filled_excerpt.php:36
msgid "Numbers of characters in excerpt"
msgstr "Numero di caratteri nel riassunto"

#: modules/yoastseo/lib/Requirement/Seo_Analysis.php:124
#: modules/yoastseo/lib/Requirement/Seo_Analysis.php:159
#: modules/yoastseo/lib/Requirement/Readability_Analysis.php:125
#: modules/yoastseo/lib/Requirement/Readability_Analysis.php:160
msgid "OK"
msgstr "OK"

#: modules/checklists/checklists.php:789
msgid "Ok"
msgstr "Ok"

#: core/Requirement/Openai_item.php:61 core/Requirement/Openai_item.php:62
msgid "Openai"
msgstr "Openai"

#: modules/settings/settings.php:708
msgid "OpenAI API Key:"
msgstr "Chiave API di OpenAI:"

#: core/Requirement/Openai_item.php:287
msgid ""
"OpenAI tasks require an API Key. Please add your API Key in the Settings "
"area."
msgstr ""
"Le attività di OpenAI richiedono una chiave API. Aggiungi la chiave API "
"nelle impostazioni."

#: modules/checklists/checklists.php:952
msgid "Options"
msgstr "Opzioni"

#: modules/permalinks/permalinks.php:65
msgid "Permalinks"
msgstr "Permalink"

#: modules/permissions/permissions.php:68
msgid "Permissions"
msgstr "Autorizzazioni"

#: modules/checklists/checklists.php:744
msgid "Please complete the following tasks before publishing:"
msgstr "Completare le seguenti attività prima della pubblicazione:"

#: modules/checklists/checklists.php:748
msgid "Please complete the following tasks before updating the published post:"
msgstr ""
"Completa le seguenti attività prima di aggiornare l'articolo pubblicato:"

#: modules/checklists/assets/js/gutenberg-panel.jsx:167
msgid "Please complete the required(*) checklists task."
msgstr "Devi completare le attività obbligatorie (*) delle liste di controllo."

#: core/Legacy/LegacyPlugin.php:209
msgid "Please correct your form errors below and try again."
msgstr "Correggi gli errori nel modulo sottostante e riprova."

#: modules/checklists/checklists.php:594
msgid "Please make sure to add a name for all the custom tasks."
msgstr "Assicurati di aggiungere un nome per tutte le attività personalizzate."

#: modules/checklists/checklists.php:590
msgid "Please make sure to complete the settings for"
msgstr "Assicurati di completare le impostazioni per"

#: core/Requirement/Openai_item.php:278
msgid "Post content is empty."
msgstr "Il contenuto dell'articolo è vuoto."

#: core/Legacy/LegacyPlugin.php:218
msgid "Post does not exist"
msgstr "L'articolo non esiste"

#: core/Requirement/Prohibited_categories.php:43
msgid "Prohibited categories"
msgstr "Categorie vietate"

#: core/Requirement/Prohibited_categories.php:42
msgid "Prohibited categories: %s"
msgstr "Categorie vietate: %s"

#: core/Requirement/Prohibited_tags.php:43
msgid "Prohibited tags"
msgstr "Tag vietati"

#: core/Requirement/Prohibited_tags.php:42
msgid "Prohibited tags: %s"
msgstr "Tag vietati: %s"

#. Author of the plugin
msgid "PublishPress"
msgstr "PublishPress"

#. Name of the plugin
#: core/Legacy/Module.php:285
msgid "PublishPress Checklists"
msgstr "PublishPress Checklists"

#: modules/checklists/checklists.php:976
msgid "Recommended"
msgstr "Raccomandato"

#: core/Requirement/Custom_item.php:121 core/Requirement/Openai_item.php:139
#: modules/checklists/checklists.php:602
msgid "Remove"
msgstr "Rimuovi"

#: modules/checklists/checklists.php:787 modules/checklists/checklists.php:977
msgid "Required"
msgstr "Richiesto"

#: modules/checklists/assets/js/gutenberg-panel.jsx:255
msgid "required"
msgstr "richiesto"

#: core/Requirement/Required_categories.php:43
msgid "Required categories"
msgstr "Categorie obbligatorie"

#: core/Requirement/Required_categories.php:42
msgid "Required categories: %s"
msgstr "Categorie obbligatorie: %s"

#: core/Requirement/Required_tags.php:43
msgid "Required tags"
msgstr "Tag obbligatori"

#: core/Requirement/Required_tags.php:42
msgid "Required tags: %s"
msgstr "Tag obbligatori: %s"

#: modules/reviews/reviews.php:61
msgid "Reviews"
msgstr "Recensioni"

#: modules/checklists/templates/global-checklists.php:108
msgid "Save Changes"
msgstr "Salva modifiche"

#: core/Requirement/Openai_item.php:365
msgid "See the full response."
msgstr "Vedi la risposta completa."

#: modules/settings/settings.php:114
msgid "Settings"
msgstr "Impostazioni"

#: modules/settings/settings.php:692
msgid "Show warning icon:"
msgstr "Mostra l'icona di avviso:"

#: modules/checklists/checklists.php:950
msgid "Task"
msgstr "Attività"

#: modules/checklists/templates/global-checklists.php:101
msgid "The prompt should be in form of a question."
msgstr "Il prompt deve essere nel formato di una domanda."

#: modules/settings/settings.php:312
msgid "There are no PublishPress modules registered"
msgstr "Non ci sono moduli PublishPress registrati"

#: modules/checklists/templates/global-checklists.php:101
msgid "This feature requires an OpenAI API Key."
msgstr "Questa funzionalità richiede una chiave API di OpenAI."

#: modules/settings/settings.php:741
msgid "This will display a warning icon in the \"Publish\" box."
msgstr "Questo visualizzerà una icona di attenzione nel box \"Pubblica\"."

#: core/Requirement/Openai_item.php:273
msgid "Validation error. Kindly reload this page and try agai.n"
msgstr "Errore di convalida. Ricarica questa pagina e riprova."

#: modules/checklists/checklists.php:606
msgid "What's the expected OpenAI response to mark the requirement as pass?"
msgstr ""
"Qual è la risposta attesa da OpenAI per contrassegnare il requisito come "
"superato?"

#: core/Requirement/Custom_item.php:54 modules/checklists/checklists.php:598
msgid "Which roles can mark this task as complete?"
msgstr "Quali ruoli possono contrassegnare questa attività come completata?"

#: modules/permissions/permissions.php:143
msgid "Who can ignore the task?"
msgstr "Chi può ignorare l'attività?"

#. Description of the plugin
msgid ""
"With PublishPress Checklists, you can choose publishing requirements for "
"your content."
msgstr ""
"Con le checklist di PublishPress puoi scegliere i requisiti di pubblicazione "
"per i tuoi contenuti."

#: modules/checklists/checklists.php:791
msgid "Yes"
msgstr "Sì"

#. %s expands to the readability score
#: modules/yoastseo/lib/Requirement/Readability_Analysis.php:124
#: modules/yoastseo/lib/Requirement/Readability_Analysis.php:129
#, php-format
msgid "Yoast Readability: %s"
msgstr "Leggibilità Yoast: %s"

#: modules/yoastseo/yoastseo.php:66
msgid "Yoast SEO"
msgstr "Yoast SEO"

#. %s expands to the seo score
#: modules/yoastseo/lib/Requirement/Seo_Analysis.php:123
#: modules/yoastseo/lib/Requirement/Seo_Analysis.php:128
#, php-format
msgid "Yoast SEO: %s"
msgstr "SEO di Yoast: %s"

#: core/Legacy/LegacyPlugin.php:214
msgid "You do not have necessary permissions to complete this action."
msgstr ""
"Non disponi delle autorizzazioni necessarie per completare questa azione."

#: modules/checklists/checklists.php:783
#, php-format
msgid "You don't have to complete any %sChecklist tasks%s."
msgstr "Non è necessario completare nessuna %sattività di Checklist%s."

#: modules/checklists/checklists.php:730
#: modules/checklists/assets/js/gutenberg-panel.jsx:208
msgid "You don't have to complete any Checklist tasks."
msgstr "Non è necessario completare nessuna attività delle liste di controllo."
