msgid ""
msgstr ""
"Project-Id-Version: PublishPress - PublishPress Checklists\n"
"POT-Creation-Date: 2024-10-15 21:56+0700\n"
"PO-Revision-Date: 2024-10-17 20:54+0000\n"
"Last-Translator: \n"
"Language-Team: Portuguese (Brazil)\n"
"Language: pt_BR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: Loco https://localise.biz/\n"
"Report-Msgid-Bugs-To: \n"
"X-Loco-Version: 2.6.11; wp-6.6.2"

#: core/Requirement/Taxonomies_count.php:48
#: core/Requirement/Taxonomies_count.php:49
msgid "%d "
msgstr "%d "

#: core/Requirement/Filled_excerpt.php:41
msgid "%d character in excerpt"
msgstr "%d caracter no resumo"

#: core/Requirement/Title_count.php:39
msgid "%d character in title"
msgstr "%d caracter no título"

#: core/Requirement/Filled_excerpt.php:42
msgid "%d characters in excerpt"
msgstr "%d caracteres no resumo"

#: core/Requirement/Title_count.php:40
msgid "%d characters in title"
msgstr "%d caracteres no título"

#: core/Requirement/External_links.php:41
msgid "%d external link in content"
msgstr "%d link externo no conteúdo"

#: core/Requirement/External_links.php:42
msgid "%d external links in content"
msgstr "%d links externos no conteúdo"

#: core/Requirement/Internal_links.php:41
msgid "%d internal link in content"
msgstr "%d link interno no conteúdo"

#: core/Requirement/Internal_links.php:42
msgid "%d internal links in content"
msgstr "%d links internos no conteúdo"

#: core/Requirement/Tags_count.php:45
msgid "%d tag"
msgstr "%d tag"

#: core/Requirement/Tags_count.php:46
msgid "%d tags"
msgstr "%d tags"

#: core/Requirement/Words_count.php:41
msgid "%d word in content"
msgstr "%d palavra no conteúdo"

#: core/Requirement/Words_count.php:42
msgid "%d words in content"
msgstr "%d palavras no conteúdo"

#: core/Requirement/Categories_count.php:46
#, php-format
msgid "%s categories"
msgstr "%s categorias"

#: core/Requirement/Categories_count.php:45
#, php-format
msgid "%s category"
msgstr "%s categoria"

#: modules/checklists/templates/footer.php:23
msgid "About"
msgstr "Sobre"

#: modules/checklists/checklists.php:953
msgid "Add custom task"
msgstr "Adicionar item personalizado"

#: modules/checklists/templates/global-checklists.php:94
msgid "Add OpenAI Prompt task"
msgstr "Adicionar tarefa do OpenAI Prompt"

#: modules/settings/settings.php:684
msgid "Add to these post types:"
msgstr "Adicionar a estes tipos de post:"

#: core/Requirement/Image_alt.php:36 core/Requirement/Image_alt.php:37
msgid "All images have Alt text"
msgstr "Todas as imagens têm texto alternativo"

#: core/Requirement/Validate_links.php:58
#: core/Requirement/Validate_links.php:59
msgid "All links use a valid format"
msgstr "Todos os links usam um formato válido"

#: core/Requirement/Openai_item.php:266
msgid "An error occured."
msgstr "Ocorreu um erro."

#: core/Requirement/Openai_item.php:340
#, php-format
msgid "API Error: %1s."
msgstr "Erro de API: %1s."

#: core/Requirement/Approved_by.php:41
#, php-format
msgid "Approved by %s"
msgstr "Aprovado por %s"

#: core/Requirement/Approved_by.php:42
msgid "Approved by a user in this role"
msgstr "Aprovado por um usuário nesta função"

#: modules/checklists/checklists.php:736
msgid "Are you sure you want to publish anyway?"
msgstr "Tem certeza que quer publicar de qualquer maneira?"

#: modules/checklists/checklists.php:740
msgid "Are you sure you want to update the published post anyway?"
msgstr "Tem certeza que deseja atualizar este post já publicado?"

#: core/Requirement/Taxonomies_count.php:50
msgid "Between %d and %d "
msgstr "Entre %d e %d "

#: core/Requirement/Filled_excerpt.php:43
msgid "Between %d and %d characters in excerpt"
msgstr "Entre %d e %d caracteres no resumo"

#: core/Requirement/Title_count.php:41
msgid "Between %d and %d characters in title"
msgstr "Entre %d e %d caracteres no título"

#: core/Requirement/External_links.php:43
msgid "Between %d and %d external links in content"
msgstr "Entre %d e %d links externos no conteúdo"

#: core/Requirement/Internal_links.php:43
msgid "Between %d and %d internal links in content"
msgstr "Entre %d e %d links internos no conteúdo"

#: core/Requirement/Tags_count.php:47
msgid "Between %d and %d tags"
msgstr "Entre %d e %d tags"

#: core/Requirement/Words_count.php:43
msgid "Between %d and %d words in content"
msgstr "Entre %d e %d palavras no conteúdo"

#: core/Requirement/Categories_count.php:47
#, php-format
msgid "Between %s and %s categories"
msgstr "Entre %s e %s categorias"

#: core/Legacy/LegacyPlugin.php:213 modules/settings/settings.php:444
msgid "Cheatin&#8217; uh?"
msgstr "Trapaceando&#8217; hein?"

#: modules/checklists/checklists.php:788
#: modules/checklists/assets/js/gutenberg-panel.jsx:242
msgid "Check Now"
msgstr "Verifique agora"

#: modules/checklists/checklists.php:669 modules/checklists/checklists.php:734
msgid "Checklist"
msgstr "Checklist"

#: core/Legacy/LegacyPlugin.php:349 modules/checklists/checklists.php:101
#: modules/checklists/checklists.php:887 modules/checklists/checklists.php:902
#: modules/checklists/assets/js/gutenberg-panel.jsx:197
#: modules/checklists/assets/js/gutenberg-panel.jsx:201
msgid "Checklists"
msgstr "Checklists"

#: modules/settings/settings.php:61 modules/settings/settings.php:112
msgid "Checklists Settings"
msgstr "Configurações de listas de verificação"

#: core/Legacy/LegacyPlugin.php:206 modules/checklists/checklists.php:735
msgid "Configure"
msgstr "Configurar"

#: modules/checklists/templates/footer.php:37
msgid "Contact"
msgstr "Contato"

#: core/Requirement/Custom_item.php:52 core/Requirement/Custom_item.php:53
msgid "Custom"
msgstr "Personalizado"

#: modules/checklists/templates/global-checklists.php:87
msgid ""
"Custom tasks do not complete automatically. Users must check the box to show "
"they have completed the task."
msgstr ""
"Tarefas personalizadas não são concluídas automaticamente. Os usuários devem "
"marcar a caixa para mostrar que concluíram a tarefa."

#: modules/permalinks/permalinks.php:66
msgid "Define tasks related to permalinks"
msgstr "Defina tarefas relacionadas aos links permanentes"

#: modules/yoastseo/yoastseo.php:67
msgid "Define tasks related to Yoast SEO"
msgstr "Definir tarefas relacionadas ao Yoast SEO"

#: modules/checklists/templates/global-checklists.php:75
msgid "Description"
msgstr "Descrição"

#: modules/settings/settings.php:700
msgid "Disable the \"Status\" option when using \"Quick Edit\":"
msgstr "Desabilite a opção \"Status\" ao usar \"Edição rápida\":"

#: modules/checklists/checklists.php:975
msgid "Disabled"
msgstr "Inativo"

#: modules/settings/settings.php:408
#, php-format
msgid ""
"Disabled because add_post_type_support('%1$s', '%2$s') is included in a "
"loaded file."
msgstr ""
"Desabilitado, pois add_post_type_support('%1$s', '%2$s') está incluído em um "
"arquivo já carregado."

#: modules/checklists/checklists.php:951
msgid "Disabled, Recommended or Required"
msgstr "Inativos, recomendados ou necessários"

#: modules/checklists/templates/footer.php:30
msgid "Documentation"
msgstr "Documentação"

#: modules/settings/settings.php:261
msgid "Enabled features"
msgstr "Funcionalidades ativadas"

#: modules/settings/settings.php:781
msgid "Enter your API Key to use OpenAI prompts in checklist tasks."
msgstr ""
"Insira sua chave de API para usar prompts do OpenAI em tarefas de lista de "
"verificação."

#: core/Requirement/Featured_image_alt.php:36
#: core/Requirement/Featured_image_alt.php:37
msgid "Featured image has Alt text"
msgstr "A imagem em destaque tem texto alternativo"

#: core/Requirement/Featured_image.php:35
#: core/Requirement/Featured_image.php:36
msgid "Featured image is added"
msgstr "A imagem em destaque foi adicionada"

#: modules/settings/settings.php:252
msgid "Features"
msgstr "Funcionalidades"

#: modules/settings/settings.php:253
msgid "Feel free to select only the features you need."
msgstr ""
"Sinta-se livre para selecionar somente as funcionalidades de que precisa."

#: modules/settings/settings.php:675
msgid "General:"
msgstr "Geral:"

#: modules/yoastseo/lib/Requirement/Seo_Analysis.php:129
#: modules/yoastseo/lib/Requirement/Seo_Analysis.php:160
#: modules/yoastseo/lib/Requirement/Readability_Analysis.php:130
#: modules/yoastseo/lib/Requirement/Readability_Analysis.php:161
msgid "Good"
msgstr "Bom"

#. Author URI of the plugin
msgid "https://publishpress.com"
msgstr "https://publishpress.com"

#. URI of the plugin
msgid "https://publishpress.com/"
msgstr "https://publishpress.com/"

#: modules/settings/settings.php:762
msgid ""
"If the \"Status\" option is enabled, it can be used to avoid using the "
"Checklists requirements."
msgstr ""
"Se a opção \"Status\" estiver habilitada, ela poderá ser usada para evitar o "
"uso dos requisitos das Listas de Verificação."

#: core/Legacy/Module.php:288
#, php-format
msgid "If you like %s please leave us a %s rating. Thank you!"
msgstr "Se você gosta do %s por favor nos deixe uma avaliação %s. Obrigado!"

#: core/Requirement/Openai_item.php:386
msgid "Invalid response"
msgstr "Resposta inválida"

#: modules/permalinks/lib/Requirement/ValidChars.php:37
msgid "Latin characters in permalink"
msgstr "Caracteres latinos no link permanente"

#: core/Requirement/Base_counter.php:293
msgid "Max"
msgstr "Máx."

#: core/Requirement/Taxonomies_count.php:46
#: core/Requirement/Taxonomies_count.php:47
msgid "Maximum of %d "
msgstr "Máximo de %d "

#: core/Requirement/Filled_excerpt.php:39
msgid "Maximum of %d character in excerpt"
msgstr "Máximo de %d caracteres no resumo"

#: core/Requirement/Title_count.php:37
msgid "Maximum of %d character in title"
msgstr "Máximo de %d caracter no título"

#: core/Requirement/Filled_excerpt.php:40
msgid "Maximum of %d characters in excerpt"
msgstr "Máximo de %d caracteres no resumo"

#: core/Requirement/Title_count.php:38
msgid "Maximum of %d characters in title"
msgstr "Máximo de %d caracteres no título"

#: core/Requirement/External_links.php:39
msgid "Maximum of %d external link in content"
msgstr "Máximo de %d link externo no conteúdo"

#: core/Requirement/External_links.php:40
msgid "Maximum of %d external links in content"
msgstr "Máximo de %d links externos no conteúdo"

#: core/Requirement/Internal_links.php:39
msgid "Maximum of %d internal link in content"
msgstr "Máximo de %d link interno no conteúdo"

#: core/Requirement/Internal_links.php:40
msgid "Maximum of %d internal links in content"
msgstr "Máximo de %d links internos no conteúdo"

#: core/Requirement/Tags_count.php:43
msgid "Maximum of %d tag"
msgstr "Máximo de %d tag"

#: core/Requirement/Tags_count.php:44
msgid "Maximum of %d tags"
msgstr "Máximo de %d tags"

#: core/Requirement/Words_count.php:39
msgid "Maximum of %d word in content"
msgstr "Máximo de %d palavra no conteúdo"

#: core/Requirement/Words_count.php:40
msgid "Maximum of %d words in content"
msgstr "Máximo de %d palavras no conteúdo"

#: core/Requirement/Categories_count.php:44
#, php-format
msgid "Maximum of %s categories"
msgstr "Máximo de %s categorias"

#: core/Requirement/Categories_count.php:43
#, php-format
msgid "Maximum of %s category"
msgstr "Máximo de %s categoria"

#: core/Requirement/Base_counter.php:285
msgid "Min"
msgstr "Mínimo"

#: core/Requirement/Taxonomies_count.php:44
#: core/Requirement/Taxonomies_count.php:45
msgid "Minimum of %d "
msgstr "Mínimo de %d "

#: core/Requirement/Filled_excerpt.php:37
msgid "Minimum of %d character in excerpt"
msgstr "Mínimo de %d categorias"

#: core/Requirement/Title_count.php:35
msgid "Minimum of %d character in title"
msgstr "Mínimo de %d caracter no título"

#: core/Requirement/Filled_excerpt.php:38
msgid "Minimum of %d characters in excerpt"
msgstr "Mínimo de %d caracteres no resumo"

#: core/Requirement/Title_count.php:36
msgid "Minimum of %d characters in title"
msgstr "Mínimo de %d caracteres no título"

#: core/Requirement/External_links.php:37
msgid "Minimum of %d external link in content"
msgstr "Mínimo de %d link externo no conteúdo"

#: core/Requirement/External_links.php:38
msgid "Minimum of %d external links in content"
msgstr "Mínimo de %d links externos no conteúdo"

#: core/Requirement/Internal_links.php:37
msgid "Minimum of %d internal link in content"
msgstr "Mínimo de %d link interno no conteúdo"

#: core/Requirement/Internal_links.php:38
msgid "Minimum of %d internal links in content"
msgstr "Mínimo de %d links internos no conteúdo"

#: core/Requirement/Tags_count.php:41
msgid "Minimum of %d tag"
msgstr "Mínimo de %d tag"

#: core/Requirement/Tags_count.php:42
msgid "Minimum of %d tags"
msgstr "Mínimo de %d tags"

#: core/Requirement/Words_count.php:37
msgid "Minimum of %d word in content"
msgstr "Mínimo de %d palavra no conteúdo"

#: core/Requirement/Words_count.php:38
msgid "Minimum of %d words in content"
msgstr "Mínimo de %d palavras no conteúdo"

#: core/Requirement/Categories_count.php:42
#, php-format
msgid "Minimum of %s categories"
msgstr "Mínimo de %s categorias"

#: core/Requirement/Categories_count.php:41
#, php-format
msgid "Minimum of %s category"
msgstr "Mínimo de %s categoria"

#: modules/yoastseo/lib/Requirement/Seo_Analysis.php:46
msgid "Minimum Yoast SEO analysis score"
msgstr "Passar na análise de legibilidade do Yoast SEO"

#: modules/yoastseo/lib/Requirement/Readability_Analysis.php:47
msgid "Minimum Yoast SEO readability score"
msgstr "Pontuação mínima de legibilidade do Yoast SEO"

#: modules/checklists/templates/global-checklists.php:74
msgid "New Item"
msgstr "Novo Item"

#: modules/checklists/checklists.php:790
msgid "No"
msgstr "Não"

#: modules/checklists/templates/global-checklists.php:101
msgid "No %s requirements for this post type."
msgstr "Nenhum requisito %s para este tipo de postagem."

#: modules/checklists/checklists.php:752 modules/checklists/checklists.php:756
msgid "Not required, but important: "
msgstr "Não é obrigatório, mas é importante: "

#: core/Requirement/Taxonomies_count.php:43
msgid "Number of "
msgstr "Número de "

#: core/Requirement/Categories_count.php:40
msgid "Number of categories"
msgstr "Número de categorias"

#: core/Requirement/Image_alt_count.php:42
#: core/Requirement/Image_alt_count.php:43
msgid "Number of characters in Alt text"
msgstr "Número de caracteres no texto Alt"

#: core/Requirement/Title_count.php:33 core/Requirement/Title_count.php:34
msgid "Number of characters in title"
msgstr "Número de caracteres no título"

#: core/Requirement/External_links.php:36
msgid "Number of external links in content"
msgstr "Número de links externos no conteúdo"

#: core/Requirement/Internal_links.php:36
msgid "Number of internal links in content"
msgstr "Número de links internos no conteúdo"

#: core/Requirement/Tags_count.php:40
msgid "Number of tags"
msgstr "Número de tags"

#: core/Requirement/Words_count.php:36
msgid "Number of words in content"
msgstr "Número de palavras no conteúdo"

#: core/Requirement/Filled_excerpt.php:35
#: core/Requirement/Filled_excerpt.php:36
msgid "Numbers of characters in excerpt"
msgstr "Números de caracteres no resumo"

#: modules/yoastseo/lib/Requirement/Seo_Analysis.php:124
#: modules/yoastseo/lib/Requirement/Seo_Analysis.php:159
#: modules/yoastseo/lib/Requirement/Readability_Analysis.php:125
#: modules/yoastseo/lib/Requirement/Readability_Analysis.php:160
msgid "OK"
msgstr "OK"

#: modules/checklists/checklists.php:789
msgid "Ok"
msgstr "Ok"

#: core/Requirement/Openai_item.php:61 core/Requirement/Openai_item.php:62
msgid "Openai"
msgstr "Aberto"

#: modules/settings/settings.php:708
msgid "OpenAI API Key:"
msgstr "Chave da API OpenAI:"

#: core/Requirement/Openai_item.php:287
msgid ""
"OpenAI tasks require an API Key. Please add your API Key in the Settings "
"area."
msgstr ""
"As tarefas OpenAI exigem uma API Key. Adicione sua API Key na área Settings."

#: modules/checklists/checklists.php:952
msgid "Options"
msgstr "Opções"

#: modules/permalinks/permalinks.php:65
msgid "Permalinks"
msgstr "Links Permanentes"

#: modules/permissions/permissions.php:68
msgid "Permissions"
msgstr "Permissões"

#: modules/checklists/checklists.php:744
msgid "Please complete the following tasks before publishing:"
msgstr "Por favor, complete as seguintes tarefas antes de publicar:"

#: modules/checklists/checklists.php:748
msgid "Please complete the following tasks before updating the published post:"
msgstr ""
"Por favor, complete as seguintes tarefas antes de atualizar este post já "
"publicado:"

#: modules/checklists/assets/js/gutenberg-panel.jsx:167
msgid "Please complete the required(*) checklists task."
msgstr "Por favor, complete a tarefa de listas de verificação obrigatória(*)."

#: core/Legacy/LegacyPlugin.php:209
msgid "Please correct your form errors below and try again."
msgstr "Por favor corrija os erros do formulário abaixo e tente novamente."

#: modules/checklists/checklists.php:594
msgid "Please make sure to add a name for all the custom tasks."
msgstr ""
"Certifique-se de adicionar um nome para todas as tarefas personalizadas."

#: modules/checklists/checklists.php:590
msgid "Please make sure to complete the settings for"
msgstr "Certifique-se de concluir as configurações para"

#: core/Requirement/Openai_item.php:278
msgid "Post content is empty."
msgstr "O conteúdo da postagem está vazio"

#: core/Legacy/LegacyPlugin.php:218
msgid "Post does not exist"
msgstr "O post não existe"

#: core/Requirement/Prohibited_categories.php:43
msgid "Prohibited categories"
msgstr "Categorias proibidas"

#: core/Requirement/Prohibited_categories.php:42
msgid "Prohibited categories: %s"
msgstr "Categorias proibidas: %s"

#: core/Requirement/Prohibited_tags.php:43
msgid "Prohibited tags"
msgstr "Tags proibidas"

#: core/Requirement/Prohibited_tags.php:42
msgid "Prohibited tags: %s"
msgstr "Tags proibidas: %s"

#. Author of the plugin
msgid "PublishPress"
msgstr "PublishPress"

#. Name of the plugin
#: core/Legacy/Module.php:285
msgid "PublishPress Checklists"
msgstr "PublishPress Checklists"

#: modules/checklists/checklists.php:976
msgid "Recommended"
msgstr "Recomendado"

#: core/Requirement/Custom_item.php:121 core/Requirement/Openai_item.php:139
#: modules/checklists/checklists.php:602
msgid "Remove"
msgstr "Remover"

#: modules/checklists/checklists.php:787 modules/checklists/checklists.php:977
msgid "Required"
msgstr "Obrigatório"

#: modules/checklists/assets/js/gutenberg-panel.jsx:255
msgid "required"
msgstr "Obrigatório"

#: core/Requirement/Required_categories.php:43
msgid "Required categories"
msgstr "Categorias obrigatórias"

#: core/Requirement/Required_categories.php:42
msgid "Required categories: %s"
msgstr "Categorias obrigatórias: %s"

#: core/Requirement/Required_tags.php:43
msgid "Required tags"
msgstr "Tags obrigatórias"

#: core/Requirement/Required_tags.php:42
msgid "Required tags: %s"
msgstr "Tags obrigatórias: %s"

#: modules/reviews/reviews.php:61
msgid "Reviews"
msgstr "Avaliações"

#: modules/checklists/templates/global-checklists.php:108
msgid "Save Changes"
msgstr "Salvar Alterações"

#: core/Requirement/Openai_item.php:365
msgid "See the full response."
msgstr "Veja a resposta completa."

#: modules/settings/settings.php:114
msgid "Settings"
msgstr "Configurações"

#: modules/settings/settings.php:692
msgid "Show warning icon:"
msgstr "Mostrar ícone de aviso:"

#: modules/checklists/checklists.php:950
msgid "Task"
msgstr "Tarefa"

#: modules/checklists/templates/global-checklists.php:101
msgid "The prompt should be in form of a question."
msgstr "O prompt deve ser em forma de pergunta."

#: modules/settings/settings.php:312
msgid "There are no PublishPress modules registered"
msgstr "Não há módulos do PublishPress registrados"

#: modules/checklists/templates/global-checklists.php:101
msgid "This feature requires an OpenAI API Key."
msgstr "Este recurso requer uma chave de API OpenAI."

#: modules/settings/settings.php:741
msgid "This will display a warning icon in the \"Publish\" box."
msgstr "Isso exibirá um ícone de aviso na caixa \"Publicar\"."

#: core/Requirement/Openai_item.php:273
msgid "Validation error. Kindly reload this page and try agai.n"
msgstr ""
"Erro de validação. Por favor, recarregue esta página e tente novamente.n"

#: modules/checklists/checklists.php:606
msgid "What's the expected OpenAI response to mark the requirement as pass?"
msgstr ""
"Qual é a resposta esperada da OpenAI para marcar o requisito como aprovado?"

#: core/Requirement/Custom_item.php:54 modules/checklists/checklists.php:598
msgid "Which roles can mark this task as complete?"
msgstr "Quais funções podem marcar esta tarefa como concluída?"

#: modules/permissions/permissions.php:143
msgid "Who can ignore the task?"
msgstr "Quem pode ignorar a tarefa?"

#. Description of the plugin
msgid ""
"With PublishPress Checklists, you can choose publishing requirements for "
"your content."
msgstr ""
"Com as listas de verificação do PublishPress, você pode escolher os "
"requisitos de publicação para seu conteúdo."

#: modules/checklists/checklists.php:791
msgid "Yes"
msgstr "Sim"

#. %s expands to the readability score
#: modules/yoastseo/lib/Requirement/Readability_Analysis.php:124
#: modules/yoastseo/lib/Requirement/Readability_Analysis.php:129
#, php-format
msgid "Yoast Readability: %s"
msgstr "Legibilidade do Yoast: %s"

#: modules/yoastseo/yoastseo.php:66
msgid "Yoast SEO"
msgstr "Yoast SEO"

#. %s expands to the seo score
#: modules/yoastseo/lib/Requirement/Seo_Analysis.php:123
#: modules/yoastseo/lib/Requirement/Seo_Analysis.php:128
#, php-format
msgid "Yoast SEO: %s"
msgstr "Yoast SEO: %s"

#: core/Legacy/LegacyPlugin.php:214
msgid "You do not have necessary permissions to complete this action."
msgstr "Você não tem as permissões necessárias para completar esta ação."

#: modules/checklists/checklists.php:783
#, php-format
msgid "You don't have to complete any %sChecklist tasks%s."
msgstr "Você não completou nenhuma das %starefas da checklist%s."

#: modules/checklists/checklists.php:730
#: modules/checklists/assets/js/gutenberg-panel.jsx:208
msgid "You don't have to complete any Checklist tasks."
msgstr "Você não precisa concluir nenhuma tarefa da Lista de Verificação."
