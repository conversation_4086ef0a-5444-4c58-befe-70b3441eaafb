/*
 * ATTENTION: The "eval" devtool has been used (maybe by default in mode: "development").
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
/******/ (() => { // webpackBootstrap
/******/ 	var __webpack_modules__ = ({

/***/ "./node_modules/@babel/runtime/helpers/arrayWithHoles.js":
/*!***************************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/arrayWithHoles.js ***!
  \***************************************************************/
/***/ ((module) => {

eval("function _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\n\nmodule.exports = _arrayWithHoles;\n\n//# sourceURL=webpack://publishpress-checklists/./node_modules/@babel/runtime/helpers/arrayWithHoles.js?");

/***/ }),

/***/ "./node_modules/@babel/runtime/helpers/assertThisInitialized.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/assertThisInitialized.js ***!
  \**********************************************************************/
/***/ ((module) => {

eval("function _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n\n  return self;\n}\n\nmodule.exports = _assertThisInitialized;\n\n//# sourceURL=webpack://publishpress-checklists/./node_modules/@babel/runtime/helpers/assertThisInitialized.js?");

/***/ }),

/***/ "./node_modules/@babel/runtime/helpers/classCallCheck.js":
/*!***************************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/classCallCheck.js ***!
  \***************************************************************/
/***/ ((module) => {

eval("function _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\n\nmodule.exports = _classCallCheck;\n\n//# sourceURL=webpack://publishpress-checklists/./node_modules/@babel/runtime/helpers/classCallCheck.js?");

/***/ }),

/***/ "./node_modules/@babel/runtime/helpers/createClass.js":
/*!************************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/createClass.js ***!
  \************************************************************/
/***/ ((module) => {

eval("function _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\n\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  return Constructor;\n}\n\nmodule.exports = _createClass;\n\n//# sourceURL=webpack://publishpress-checklists/./node_modules/@babel/runtime/helpers/createClass.js?");

/***/ }),

/***/ "./node_modules/@babel/runtime/helpers/defineProperty.js":
/*!***************************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/defineProperty.js ***!
  \***************************************************************/
/***/ ((module) => {

eval("function _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nmodule.exports = _defineProperty;\n\n//# sourceURL=webpack://publishpress-checklists/./node_modules/@babel/runtime/helpers/defineProperty.js?");

/***/ }),

/***/ "./node_modules/@babel/runtime/helpers/getPrototypeOf.js":
/*!***************************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/getPrototypeOf.js ***!
  \***************************************************************/
/***/ ((module) => {

eval("function _getPrototypeOf(o) {\n  module.exports = _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\n\nmodule.exports = _getPrototypeOf;\n\n//# sourceURL=webpack://publishpress-checklists/./node_modules/@babel/runtime/helpers/getPrototypeOf.js?");

/***/ }),

/***/ "./node_modules/@babel/runtime/helpers/inherits.js":
/*!*********************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/inherits.js ***!
  \*********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var setPrototypeOf = __webpack_require__(/*! ./setPrototypeOf */ \"./node_modules/@babel/runtime/helpers/setPrototypeOf.js\");\n\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) setPrototypeOf(subClass, superClass);\n}\n\nmodule.exports = _inherits;\n\n//# sourceURL=webpack://publishpress-checklists/./node_modules/@babel/runtime/helpers/inherits.js?");

/***/ }),

/***/ "./node_modules/@babel/runtime/helpers/iterableToArrayLimit.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/iterableToArrayLimit.js ***!
  \*********************************************************************/
/***/ ((module) => {

eval("function _iterableToArrayLimit(arr, i) {\n  if (!(Symbol.iterator in Object(arr) || Object.prototype.toString.call(arr) === \"[object Arguments]\")) {\n    return;\n  }\n\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n  var _e = undefined;\n\n  try {\n    for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n\n  return _arr;\n}\n\nmodule.exports = _iterableToArrayLimit;\n\n//# sourceURL=webpack://publishpress-checklists/./node_modules/@babel/runtime/helpers/iterableToArrayLimit.js?");

/***/ }),

/***/ "./node_modules/@babel/runtime/helpers/nonIterableRest.js":
/*!****************************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/nonIterableRest.js ***!
  \****************************************************************/
/***/ ((module) => {

eval("function _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance\");\n}\n\nmodule.exports = _nonIterableRest;\n\n//# sourceURL=webpack://publishpress-checklists/./node_modules/@babel/runtime/helpers/nonIterableRest.js?");

/***/ }),

/***/ "./node_modules/@babel/runtime/helpers/possibleConstructorReturn.js":
/*!**************************************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/possibleConstructorReturn.js ***!
  \**************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var _typeof = __webpack_require__(/*! ../helpers/typeof */ \"./node_modules/@babel/runtime/helpers/typeof.js\");\n\nvar assertThisInitialized = __webpack_require__(/*! ./assertThisInitialized */ \"./node_modules/@babel/runtime/helpers/assertThisInitialized.js\");\n\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  }\n\n  return assertThisInitialized(self);\n}\n\nmodule.exports = _possibleConstructorReturn;\n\n//# sourceURL=webpack://publishpress-checklists/./node_modules/@babel/runtime/helpers/possibleConstructorReturn.js?");

/***/ }),

/***/ "./node_modules/@babel/runtime/helpers/setPrototypeOf.js":
/*!***************************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/setPrototypeOf.js ***!
  \***************************************************************/
/***/ ((module) => {

eval("function _setPrototypeOf(o, p) {\n  module.exports = _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n\n  return _setPrototypeOf(o, p);\n}\n\nmodule.exports = _setPrototypeOf;\n\n//# sourceURL=webpack://publishpress-checklists/./node_modules/@babel/runtime/helpers/setPrototypeOf.js?");

/***/ }),

/***/ "./node_modules/@babel/runtime/helpers/slicedToArray.js":
/*!**************************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/slicedToArray.js ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var arrayWithHoles = __webpack_require__(/*! ./arrayWithHoles */ \"./node_modules/@babel/runtime/helpers/arrayWithHoles.js\");\n\nvar iterableToArrayLimit = __webpack_require__(/*! ./iterableToArrayLimit */ \"./node_modules/@babel/runtime/helpers/iterableToArrayLimit.js\");\n\nvar nonIterableRest = __webpack_require__(/*! ./nonIterableRest */ \"./node_modules/@babel/runtime/helpers/nonIterableRest.js\");\n\nfunction _slicedToArray(arr, i) {\n  return arrayWithHoles(arr) || iterableToArrayLimit(arr, i) || nonIterableRest();\n}\n\nmodule.exports = _slicedToArray;\n\n//# sourceURL=webpack://publishpress-checklists/./node_modules/@babel/runtime/helpers/slicedToArray.js?");

/***/ }),

/***/ "./node_modules/@babel/runtime/helpers/typeof.js":
/*!*******************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/typeof.js ***!
  \*******************************************************/
/***/ ((module) => {

eval("function _typeof2(obj) { if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof2 = function _typeof2(obj) { return typeof obj; }; } else { _typeof2 = function _typeof2(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof2(obj); }\n\nfunction _typeof(obj) {\n  if (typeof Symbol === \"function\" && _typeof2(Symbol.iterator) === \"symbol\") {\n    module.exports = _typeof = function _typeof(obj) {\n      return _typeof2(obj);\n    };\n  } else {\n    module.exports = _typeof = function _typeof(obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : _typeof2(obj);\n    };\n  }\n\n  return _typeof(obj);\n}\n\nmodule.exports = _typeof;\n\n//# sourceURL=webpack://publishpress-checklists/./node_modules/@babel/runtime/helpers/typeof.js?");

/***/ }),

/***/ "./node_modules/@babel/runtime/regenerator/index.js":
/*!**********************************************************!*\
  !*** ./node_modules/@babel/runtime/regenerator/index.js ***!
  \**********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = __webpack_require__(/*! regenerator-runtime */ \"./node_modules/regenerator-runtime/runtime.js\");\n\n\n//# sourceURL=webpack://publishpress-checklists/./node_modules/@babel/runtime/regenerator/index.js?");

/***/ }),

/***/ "./modules/checklists/assets/js/CheckListIcon.jsx":
/*!********************************************************!*\
  !*** ./modules/checklists/assets/js/CheckListIcon.jsx ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// CheckListIcon.jsx\nvar CheckListIcon=function(){return wp.element.createElement(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",width:\"20\",height:\"20\",viewBox:\"0 0 256 256\",version:\"1.1\"},wp.element.createElement(\"path\",{d:\"M -0 128.004 L -0 256.008 128.250 255.754 L 256.500 255.500 256.754 127.750 L 257.008 0 128.504 0 L 0 0 -0 128.004 M 0.485 128.500 C 0.485 198.900, 0.604 227.553, 0.750 192.172 C 0.896 156.792, 0.896 99.192, 0.750 64.172 C 0.604 29.153, 0.485 58.100, 0.485 128.500 M 79.882 26.750 C 79.816 27.163, 79.816 27.837, 79.882 28.250 C 79.947 28.663, 79.438 28.887, 78.750 28.750 C 77.819 28.564, 77.428 31.500, 77.218 40.250 C 76.967 50.725, 76.744 52, 75.164 52 C 74.189 52, 71.254 53.356, 68.641 55.013 C 63.756 58.111, 60.067 64.017, 60.022 68.816 C 59.993 71.829, 58.714 74.132, 55.013 77.833 C 52.697 80.149, 52 81.745, 52 84.727 C 52 89.414, 56.042 96.241, 61.463 100.709 C 65.159 103.755, 65.178 103.808, 62.855 104.525 C 61.560 104.925, 57.666 107.220, 54.202 109.626 C 45.125 115.930, 43.269 115.388, 46.603 107.408 C 48.258 103.447, 48.033 96.633, 46.185 94.785 C 45.555 94.155, 43.598 94.527, 40.625 95.842 C 38.012 96.997, 35.789 97.468, 35.460 96.935 C 34.455 95.310, 31.801 95.914, 28.335 98.558 C 24.455 101.517, 21.550 106.795, 20.667 112.485 C 20.076 116.297, 20.279 116.687, 25.349 121.485 C 28.263 124.243, 33.273 129.788, 36.482 133.807 C 40.333 138.629, 43.210 141.292, 44.945 141.639 C 47.330 142.116, 47.593 142.634, 47.794 147.253 C 48.231 157.298, 51.932 166.258, 58.917 174.178 C 61.186 176.751, 62.630 179.110, 62.126 179.422 C 61.622 179.734, 60.942 179.290, 60.614 178.436 C 60.176 177.296, 58.889 176.966, 55.759 177.192 C 52.073 177.459, 51.455 177.837, 51.165 180 C 50.981 181.375, 50.368 182.950, 49.803 183.500 C 49.103 184.181, 48.997 183.767, 49.470 182.202 C 50.441 178.991, 48.594 177.212, 43.493 176.447 C 38.882 175.755, 31.576 177.505, 32.543 179.069 C 32.851 179.569, 32.478 179.737, 31.712 179.443 C 30.947 179.149, 29.686 179.691, 28.910 180.646 C 27.683 182.159, 27.694 182.274, 29 181.532 C 29.894 181.024, 30.163 181.047, 29.667 181.590 C 29.208 182.090, 28.308 182.500, 27.667 182.500 C 23.754 182.500, 21.449 198.351, 24.351 205.298 C 26.382 210.158, 32.177 214.756, 37.208 215.499 C 43.375 216.410, 49.412 214.251, 49.792 210.999 C 50.340 206.315, 48.227 204.967, 41.751 205.866 C 38.589 206.306, 36.222 206.444, 36.492 206.175 C 37.234 205.432, 34.891 202.949, 33.980 203.512 C 33.544 203.782, 33.409 202.990, 33.680 201.751 C 34.089 199.885, 33.975 199.757, 33.011 201 C 31.977 202.333, 31.894 202.333, 32.267 201 C 32.721 199.378, 36.575 199.126, 43.500 200.266 C 45.700 200.628, 48.267 200.941, 49.204 200.962 C 50.608 200.993, 50.961 202.191, 51.204 207.750 L 51.500 214.500 56 214.500 L 60.500 214.500 60.798 208.250 C 61.095 202.008, 61.443 201.465, 64.418 202.607 C 66.594 203.442, 66.489 209.802, 64.259 212.267 C 62.594 214.106, 62.621 214.274, 64.858 216.086 C 71.800 221.707, 93.549 224.531, 129.250 224.447 C 156.544 224.382, 162.885 224.146, 171 222.897 C 174.575 222.347, 179.075 221.667, 181 221.388 C 192.401 219.732, 199.668 215.715, 197.025 212.530 C 196.336 211.701, 195.380 211.265, 194.898 211.563 C 194.417 211.860, 194.291 211.406, 194.618 210.552 C 195.329 208.701, 197.993 208.465, 198.015 210.250 C 198.026 211.086, 198.357 211.004, 199.018 210 C 199.791 208.826, 199.951 209.034, 199.753 210.954 C 199.401 214.367, 203.442 216.296, 208.648 215.201 C 211.950 214.506, 212.466 214.041, 212.259 211.945 C 212.024 209.557, 212.040 209.552, 212.957 211.698 C 215.230 217.014, 227.668 216.780, 231.946 211.341 C 236.029 206.151, 233.842 200.226, 226.628 196.927 L 222.500 195.039 227 195.270 C 231.059 195.477, 231.493 195.284, 231.426 193.300 C 231.385 192.090, 231.766 191.355, 232.272 191.668 C 232.778 191.981, 233.032 191.396, 232.835 190.368 C 232.292 187.526, 225.117 185.362, 220.323 186.594 C 218.220 187.135, 216.613 188.122, 216.750 188.789 C 216.888 189.455, 216.438 189.888, 215.750 189.750 C 214.875 189.575, 214.563 190.848, 214.709 194 L 214.918 198.500 213.709 193 C 212.631 188.098, 212.201 187.466, 209.750 187.184 C 207.854 186.965, 207 186.292, 207 185.015 C 207 180.915, 206.056 180.087, 202.673 181.224 C 198.518 182.620, 196.798 184.031, 197.385 185.562 C 198.075 187.359, 194.528 187.984, 190.285 186.813 C 188.268 186.256, 185.092 186.048, 183.227 186.351 L 179.837 186.901 183.834 182.565 C 186.032 180.180, 188.582 176.776, 189.500 175 L 191.169 171.772 194.464 174.965 L 197.758 178.158 199.409 173.329 C 201.946 165.903, 202.557 155.247, 200.699 150.813 C 198.579 145.753, 199.398 143.868, 204.836 141.283 C 210.845 138.426, 221.784 128.500, 229.181 119.191 C 234.185 112.895, 234.999 111.283, 234.994 107.684 C 234.988 102.735, 233.275 96, 232.023 96 C 231.538 96, 230.177 94.071, 229 91.714 C 225.799 85.305, 220.402 79.260, 219.054 80.574 C 218.448 81.164, 217.812 85.269, 217.639 89.696 C 217.374 96.517, 216.882 98.467, 214.413 102.476 C 210.830 108.295, 204.881 112.626, 199.258 113.508 C 195.361 114.119, 194.938 113.968, 194.055 111.645 C 193.527 110.255, 192.960 107.883, 192.797 106.375 C 192.532 103.930, 192.911 103.544, 196.282 102.825 C 202.658 101.464, 206 99.258, 206 96.410 C 206 94.840, 207.099 92.946, 208.881 91.447 C 214.517 86.704, 216.554 78.503, 213.533 72.712 C 212.480 70.693, 212.344 69.225, 213.039 67.396 C 217.062 56.816, 206.777 43.237, 192.250 39.947 C 188.435 39.083, 188.284 39.673, 191.099 44.443 C 193.478 48.476, 193.087 48.819, 189.210 46.100 C 185.483 43.487, 180.553 42.392, 181.302 44.344 C 181.745 45.499, 181.091 45.688, 178.058 45.281 C 174.032 44.741, 171.887 46.287, 173.887 48.287 C 175.537 49.937, 175.256 50.700, 172.758 51.353 C 170.639 51.907, 170.791 52.231, 175.494 57.220 C 178.233 60.124, 172.717 54.855, 163.237 45.511 C 153.757 36.167, 146 28.299, 146 28.026 C 146 27.753, 151.738 33.148, 158.750 40.015 L 171.500 52.500 158.514 39.250 L 145.529 26 112.764 26 C 94.744 26, 79.947 26.337, 79.882 26.750 M 96.750 27.748 C 105.687 27.914, 120.312 27.914, 129.250 27.748 C 138.188 27.582, 130.875 27.447, 113 27.447 C 95.125 27.447, 87.813 27.582, 96.750 27.748 M 78.242 92.628 C 78.015 151.727, 78.112 155.843, 79.750 157.128 C 81.299 158.342, 81.357 158.317, 80.258 156.907 C 79.304 155.684, 78.953 140.671, 78.750 92.407 L 78.485 29.500 78.242 92.628 M 84.465 34.234 C 84.199 34.930, 84.101 61.825, 84.249 94 L 84.516 152.500 84.758 93.250 L 85 34 112.974 34 L 140.948 34 141.264 49.001 L 141.581 64.002 141.540 48.751 L 141.500 33.500 113.225 33.234 C 91.144 33.027, 84.843 33.246, 84.465 34.234 M 86.202 41.182 C 86.479 46.935, 86.686 47.413, 89.178 48.076 C 91.325 48.647, 91.741 48.489, 91.277 47.279 C 90.314 44.770, 94.840 46.697, 101.037 51.434 L 106.573 55.667 108.287 53.395 C 110.423 50.563, 110.420 50.238, 108.250 49.767 C 105.845 49.245, 112.026 45.335, 117.750 43.757 C 120.088 43.112, 122 42.276, 122 41.898 C 122 41.521, 123.306 39.814, 124.901 38.106 L 127.802 35 106.853 35 L 85.905 35 86.202 41.182 M 132 38.378 C 132 42.105, 133.130 43.397, 137.250 44.381 C 139.972 45.031, 140 44.987, 140 40.019 L 140 35 136 35 C 132.123 35, 132 35.104, 132 38.378 M 147.378 49 C 147.378 55.325, 147.541 57.912, 147.739 54.750 C 147.937 51.587, 147.937 46.412, 147.739 43.250 C 147.541 40.087, 147.378 42.675, 147.378 49 M 149 45.229 C 149 48.295, 149.810 49.715, 153.887 53.792 C 158.563 58.467, 158.866 58.610, 160.900 57.075 C 163.002 55.490, 162.949 55.395, 156.013 48.513 L 149 41.554 149 45.229 M 205.833 52.500 C 209.374 57.258, 210.579 62.049, 208.986 65.027 C 208.354 66.206, 208.083 67.416, 208.382 67.715 C 208.681 68.014, 209.449 67.110, 210.088 65.706 C 211.826 61.893, 210.414 56.785, 206.410 52.396 L 202.855 48.500 205.833 52.500 M 150.500 56 C 151.495 57.100, 152.535 58, 152.810 58 C 153.085 58, 152.495 57.100, 151.500 56 C 150.505 54.900, 149.465 54, 149.190 54 C 148.915 54, 149.505 54.900, 150.500 56 M 69.421 59.419 C 66.126 60.854, 64.519 63.894, 63.493 70.635 C 63.146 72.909, 61.976 75.722, 60.893 76.885 L 58.922 79 61.737 79 C 64.392 79, 64.532 78.784, 64.192 75.215 C 63.939 72.568, 64.407 70.719, 65.747 69.064 C 69.414 64.536, 74.720 68.345, 74.251 75.169 C 74.188 76.087, 74.781 77.086, 75.568 77.388 C 76.748 77.840, 77 76.288, 77 68.576 C 77 60.962, 76.705 59.101, 75.418 58.607 C 73.334 57.807, 73.027 57.849, 69.421 59.419 M 134.500 60.155 C 132.300 61.291, 129.037 63.609, 127.250 65.307 C 125.463 67.004, 124 67.937, 124 67.380 C 124 66.016, 116.722 62, 114.249 62 C 113.160 62, 111.137 62.891, 109.754 63.979 C 107.691 65.601, 107.108 67.171, 106.517 72.683 C 106.121 76.382, 105.020 82.370, 104.069 85.990 C 102.424 92.256, 102.425 92.571, 104.097 92.571 C 105.063 92.571, 105.635 92.218, 105.368 91.786 C 105.100 91.354, 106.285 91, 108 91 C 110.102 91, 110.886 91.376, 110.405 92.155 C 109.953 92.885, 110.131 93.037, 110.889 92.568 C 112.210 91.752, 115.255 93.778, 114.513 94.980 C 114.257 95.394, 114.862 96.470, 115.857 97.371 C 117.451 98.813, 117.667 98.824, 117.667 97.461 C 117.667 96.021, 114.197 92.644, 108.897 88.925 C 105.989 86.884, 106.962 84, 110.558 84 L 113.178 84 110.589 80.923 C 107.082 76.756, 107.197 72.650, 110.923 68.923 C 114.753 65.093, 118.100 65.142, 121.411 69.077 C 124.140 72.319, 124.688 75.846, 122.965 79.066 C 122.057 80.762, 122.272 81.002, 124.715 81.014 C 126.247 81.021, 128.564 81.312, 129.863 81.660 C 132.006 82.233, 132.123 82.098, 131.113 80.212 C 129.582 77.351, 129.719 73.444, 131.470 70.059 C 133.137 66.834, 137.799 64.748, 140.259 66.125 C 141.263 66.687, 142.107 66.635, 142.508 65.988 C 142.860 65.417, 142.553 65.074, 141.824 65.225 C 140.952 65.406, 140.394 64.221, 140.190 61.750 C 140.019 59.688, 139.569 58.020, 139.190 58.044 C 138.810 58.069, 136.700 59.018, 134.500 60.155 M 86 62.965 C 86 65.809, 86.527 67.211, 87.863 67.927 C 90.440 69.306, 92.366 74.418, 91.653 77.986 C 91.186 80.322, 91.494 81.194, 93.090 82.048 C 96.509 83.878, 95.130 87.134, 90.250 88.755 L 86 90.167 86 108.648 L 86 127.129 92.413 121.464 C 98.414 116.162, 98.764 115.621, 97.858 113.022 C 96.466 109.029, 92.528 105.629, 90.048 106.277 C 86.155 107.295, 88.017 104.865, 92.778 102.713 C 96.271 101.134, 97.989 99.606, 99.168 97.027 C 101.129 92.738, 102.295 84.300, 101.035 83.522 C 100.532 83.211, 100.376 79.905, 100.688 76.177 C 101.120 71.029, 100.868 68.646, 99.643 66.277 C 97.514 62.159, 92.935 59, 89.096 59 C 86.155 59, 86 59.199, 86 62.965 M 179.079 65.583 C 179.127 66.748, 179.364 66.985, 179.683 66.188 C 179.972 65.466, 179.936 64.603, 179.604 64.271 C 179.272 63.939, 179.036 64.529, 179.079 65.583 M 134.174 69.314 C 131.582 72.074, 131.288 77.538, 133.557 80.777 C 135.252 83.198, 137.469 83.668, 138.595 81.845 C 139 81.190, 138.873 80.960, 138.302 81.313 C 137.748 81.656, 136.578 81.388, 135.702 80.718 C 131.058 77.166, 133.897 69.183, 139.320 70.544 C 141.389 71.064, 142.267 72.040, 142.760 74.371 C 143.125 76.092, 143.290 77.950, 143.129 78.500 C 142.967 79.050, 143.997 78.409, 145.418 77.076 C 148.184 74.479, 148.990 72.934, 146.500 75 C 145.245 76.041, 145 75.891, 145 74.077 C 145 71.416, 140.769 67, 138.220 67 C 137.191 67, 135.370 68.042, 134.174 69.314 M 144.416 68.250 C 144.737 68.938, 145.266 70.246, 145.592 71.158 C 146.180 72.802, 149.116 74.052, 152 73.887 C 152.825 73.839, 152.375 73.406, 151 72.925 C 148.722 72.127, 148.788 72.076, 151.750 72.351 C 154.722 72.628, 155 72.411, 155 69.827 C 155 67.062, 154.878 67, 149.417 67 C 145.306 67, 143.987 67.330, 144.416 68.250 M 67.278 69.250 C 65.586 71.436, 64.983 74.085, 65.609 76.579 C 66.498 80.119, 67.707 79.536, 67.328 75.750 C 67.150 73.963, 67.266 73.065, 67.587 73.756 C 67.908 74.447, 68.604 74.745, 69.134 74.417 C 69.727 74.051, 69.647 73.279, 68.926 72.411 C 68.002 71.298, 68.087 71, 69.328 71 C 70.193 71, 71.359 71.787, 71.920 72.750 C 72.791 74.246, 72.944 74.269, 72.970 72.905 C 72.998 71.437, 70.071 68, 68.794 68 C 68.492 68, 67.810 68.563, 67.278 69.250 M 111.557 70.223 C 109.257 73.505, 109.623 77.714, 112.455 80.545 C 115.510 83.601, 117.059 83.631, 119.826 80.686 C 124.076 76.161, 121.615 68, 116 68 C 114.151 68, 112.554 68.799, 111.557 70.223 M 184.500 68.826 C 182.453 69.295, 181.401 70.205, 181.189 71.693 C 180.900 73.716, 181.091 73.792, 183.838 72.755 C 186.256 71.842, 187.271 71.948, 189.383 73.332 C 194.594 76.746, 193.476 82.916, 186.924 86.900 L 183.957 88.704 188.972 88.602 C 192.356 88.533, 193.990 88.078, 193.994 87.205 C 193.997 86.492, 195.105 84.805, 196.455 83.455 C 202.316 77.593, 209.333 83.294, 204.598 90.072 C 203.994 90.936, 205.188 89.866, 207.250 87.693 C 213.519 81.089, 212.009 77, 203.300 77 C 198.128 77, 198 76.931, 198 74.122 C 198 72.540, 197.343 70.699, 196.539 70.033 C 194.732 68.533, 188.509 67.909, 184.500 68.826 M 86 69.880 C 86 70.535, 86.725 72.176, 87.610 73.528 C 88.639 75.099, 88.986 76.920, 88.570 78.577 C 88.148 80.258, 88.285 80.942, 88.960 80.525 C 90.828 79.370, 90.167 72.461, 88 70.500 C 86.801 69.415, 86 69.167, 86 69.880 M 115.500 73 C 116.208 75.229, 115.037 75.637, 113.200 73.800 C 112.267 72.867, 112 73.277, 112 75.645 C 112 77.407, 112.832 79.443, 113.974 80.477 C 115.823 82.150, 116.109 82.158, 118.474 80.609 C 121.374 78.709, 121.819 74.962, 119.429 72.571 C 117.123 70.265, 114.715 70.528, 115.500 73 M 135.500 73 C 134.684 74.320, 135.880 76.192, 137.069 75.457 C 138.284 74.707, 138.276 72, 137.059 72 C 136.541 72, 135.840 72.450, 135.500 73 M 173.300 78 C 173.300 81.025, 173.487 82.263, 173.716 80.750 C 173.945 79.237, 173.945 76.763, 173.716 75.250 C 173.487 73.737, 173.300 74.975, 173.300 78 M 182.637 75.276 C 180.900 76.546, 181.244 80, 183.107 80 C 184.445 80, 184.183 78.247, 182.750 77.615 C 182.063 77.312, 182.850 77.162, 184.500 77.282 C 186.802 77.449, 187.577 78.043, 187.830 79.832 C 188.012 81.115, 187.380 83.028, 186.425 84.082 C 185.471 85.137, 185.177 86, 185.772 86 C 187.334 86, 191 81.047, 191 78.937 C 191 74.877, 186.110 72.737, 182.637 75.276 M 170.044 77.277 C 168.992 79.379, 168.936 80.336, 169.810 81.210 C 171.483 82.883, 172.192 81.675, 171.788 77.846 L 171.434 74.500 170.044 77.277 M 69.750 79.668 C 68.787 79.931, 68 80.564, 68 81.073 C 68 81.583, 67.325 82, 66.500 82 C 65.675 82, 65 82.413, 65 82.918 C 65 83.423, 64.727 84.548, 64.393 85.418 C 63.949 86.576, 64.418 87.019, 66.143 87.070 C 68.352 87.136, 68.375 87.194, 66.500 88 C 64.564 88.832, 64.569 88.862, 66.668 88.930 C 69.439 89.020, 72.119 88.001, 71.547 87.076 C 71.304 86.683, 72.462 86.561, 74.121 86.804 C 77.073 87.238, 77.129 87.166, 76.818 83.373 C 76.545 80.044, 76.149 79.478, 74 79.345 C 72.625 79.260, 70.713 79.405, 69.750 79.668 M 149.507 79.989 C 149.144 80.577, 149.626 80.718, 150.698 80.338 C 151.830 79.937, 151.233 81.042, 149.092 83.311 C 147.217 85.297, 145.355 86.720, 144.955 86.472 C 144.554 86.225, 143.613 86.778, 142.863 87.703 C 141.926 88.859, 141.890 89.149, 142.750 88.633 C 143.438 88.220, 144 88.280, 144 88.767 C 144 89.987, 139.473 94.139, 138.836 93.503 C 138.553 93.220, 137.461 93.968, 136.411 95.166 C 135.360 96.364, 135.063 97.015, 135.750 96.613 C 136.438 96.211, 137 96.280, 137 96.767 C 137 97.987, 132.473 102.139, 131.836 101.503 C 131.553 101.220, 130.461 101.968, 129.411 103.166 C 128.360 104.364, 128.063 105.015, 128.750 104.613 C 130.852 103.384, 130.140 105.520, 127.676 107.835 C 126.398 109.036, 124.975 109.677, 124.515 109.259 C 124.056 108.842, 124.003 109.063, 124.399 109.750 C 124.878 110.583, 124.411 111, 123 111 C 121.835 111, 121.141 110.581, 121.457 110.069 C 121.774 109.557, 121.506 108.813, 120.862 108.415 C 120.131 107.963, 119.959 108.124, 120.405 108.845 C 120.797 109.480, 120.711 110, 120.214 110 C 119.011 110, 115.859 106.474, 116.494 105.839 C 116.768 105.565, 115.796 104.252, 114.335 102.921 C 112.873 101.589, 112.002 101.063, 112.398 101.750 C 113.453 103.582, 112.168 103.277, 109.290 101.014 C 106.737 99.006, 104.987 99.126, 105.015 101.309 C 105.027 102.185, 105.240 102.170, 105.822 101.253 C 106.393 100.355, 107.562 100.984, 110.003 103.503 C 111.867 105.426, 114.034 107, 114.819 107 C 115.892 107, 115.972 107.329, 115.141 108.330 C 114.293 109.352, 114.778 110.339, 117.230 112.580 C 118.987 114.186, 119.823 114.713, 119.089 113.750 C 117.343 111.459, 118.571 111.517, 121.558 113.867 C 122.864 114.894, 123.610 116.127, 123.216 116.607 C 122.822 117.086, 123.513 116.626, 124.750 115.584 C 125.987 114.542, 127 113.480, 127 113.223 C 127 112.965, 126.325 113.315, 125.500 114 C 124.675 114.685, 124 114.824, 124 114.310 C 124 113.040, 129.506 107.840, 130.159 108.492 C 130.445 108.778, 131.539 108.032, 132.589 106.834 C 133.640 105.636, 133.938 104.985, 133.250 105.387 C 132.563 105.789, 132 105.720, 132 105.233 C 132 104.013, 136.527 99.861, 137.164 100.497 C 137.447 100.780, 138.539 100.032, 139.589 98.834 C 140.640 97.636, 140.938 96.985, 140.250 97.387 C 138.080 98.656, 138.895 96.447, 141.500 94 C 142.875 92.708, 144 92.025, 144 92.481 C 144 92.937, 144.889 92.505, 145.976 91.521 C 147.063 90.538, 147.694 89.314, 147.378 88.803 C 146.689 87.687, 149.946 83.849, 151.020 84.513 C 151.435 84.769, 152.329 84.308, 153.009 83.489 C 153.991 82.306, 153.989 81.691, 153 80.500 C 151.557 78.762, 150.373 78.588, 149.507 79.989 M 159.158 82 C 159.158 83.375, 159.385 83.938, 159.662 83.250 C 159.940 82.563, 159.940 81.438, 159.662 80.750 C 159.385 80.063, 159.158 80.625, 159.158 82 M 179.286 84.500 C 179.294 87.250, 179.488 88.256, 179.718 86.736 C 179.947 85.216, 179.941 82.966, 179.704 81.736 C 179.467 80.506, 179.279 81.750, 179.286 84.500 M 151.564 82.707 C 151.022 84.132, 151.136 84.247, 152.124 83.267 C 152.808 82.588, 153.115 81.781, 152.807 81.474 C 152.499 81.166, 151.940 81.721, 151.564 82.707 M 56.015 84.800 C 55.985 91.250, 61.528 97.704, 71.984 103.395 L 77 106.125 77 101.736 C 77 97.447, 76.932 97.356, 74.059 97.778 C 71.861 98.101, 70.461 97.552, 68.514 95.605 C 67.081 94.172, 65.078 93, 64.062 93 C 63.045 93, 61.958 92.333, 61.645 91.517 C 61.281 90.569, 60.430 90.278, 59.288 90.711 C 57.585 91.356, 57.583 91.304, 59.250 89.623 C 60.212 88.652, 61 87.200, 61 86.397 C 61 85.594, 60.212 84.647, 59.250 84.292 C 58.288 83.936, 57.169 83.163, 56.765 82.573 C 56.361 81.983, 56.024 82.985, 56.015 84.800 M 68.500 83 C 68.160 83.550, 68.359 84, 68.941 84 C 69.523 84, 70 83.550, 70 83 C 70 82.450, 69.802 82, 69.559 82 C 69.316 82, 68.840 82.450, 68.500 83 M 86 83.480 C 86 84.566, 87.057 85, 89.700 85 C 93.881 85, 93.176 83.262, 88.663 82.444 C 86.735 82.094, 86 82.380, 86 83.480 M 119.721 83.605 C 118.882 84.452, 117.333 84.886, 112.250 85.699 C 108.675 86.271, 108.037 87.419, 110.750 88.399 C 111.713 88.747, 113.525 89.635, 114.778 90.372 C 117.021 91.692, 122.393 91.445, 121.538 90.062 C 121.297 89.671, 122.039 89.385, 123.188 89.426 C 124.481 89.472, 125.386 88.799, 125.563 87.658 C 125.721 86.645, 126.151 85.520, 126.520 85.158 C 126.889 84.796, 127.035 85.258, 126.845 86.184 C 126.655 87.111, 126.838 87.898, 127.250 87.934 C 130.379 88.208, 132.023 87.847, 131.500 87 C 131.160 86.450, 131.584 86, 132.441 86 C 134.287 86, 134.566 84.387, 132.750 84.214 C 132.063 84.149, 129.025 83.808, 126 83.457 C 122.975 83.106, 120.149 83.173, 119.721 83.605 M 138.009 84.489 C 137.329 85.308, 136.305 85.688, 135.732 85.334 C 135.108 84.949, 134.993 85.179, 135.445 85.911 C 136.011 86.827, 136.671 86.703, 138.099 85.410 C 139.145 84.464, 140 83.535, 140 83.345 C 140 82.602, 139.189 83.068, 138.009 84.489 M 197.571 84.571 C 196.707 85.436, 196 86.261, 196 86.405 C 196 87.151, 199.591 86.557, 200.138 85.721 C 201.275 83.981, 203.109 86.075, 202.428 88.334 C 201.984 89.809, 202.104 90.181, 202.804 89.500 C 203.845 88.488, 204.335 86.532, 204.118 84.250 C 203.950 82.477, 199.445 82.698, 197.571 84.571 M 119.733 87.124 C 120.412 87.808, 121.219 88.115, 121.526 87.807 C 121.834 87.499, 121.279 86.940, 120.293 86.564 C 118.868 86.022, 118.753 86.136, 119.733 87.124 M 173.343 93.500 C 173.346 97.900, 173.522 99.576, 173.733 97.224 C 173.945 94.872, 173.942 91.272, 173.727 89.224 C 173.512 87.176, 173.339 89.100, 173.343 93.500 M 144.872 90.750 C 143.629 92.336, 143.664 92.371, 145.250 91.128 C 146.213 90.373, 147 89.585, 147 89.378 C 147 88.555, 146.179 89.084, 144.872 90.750 M 171.252 92.500 C 171.263 94.700, 171.468 95.482, 171.707 94.238 C 171.946 92.994, 171.937 91.194, 171.687 90.238 C 171.437 89.282, 171.241 90.300, 171.252 92.500 M 221.158 92.500 C 220.905 98.190, 216.619 106.726, 211.706 111.326 C 209.393 113.491, 208.400 114.792, 209.500 114.217 C 215.614 111.022, 222.413 98.823, 221.730 92.275 L 221.336 88.500 221.158 92.500 M 152.569 91.924 C 150.913 93.754, 150.914 93.836, 152.580 93.591 C 153.545 93.450, 154.444 92.584, 154.578 91.667 C 154.889 89.551, 154.694 89.575, 152.569 91.924 M 181.177 91.666 C 181.551 93.593, 188.068 97.444, 192.624 98.428 C 195.403 99.029, 195.800 98.826, 196.229 96.585 C 197.062 92.224, 193.907 90, 186.888 90 C 181.794 90, 180.904 90.260, 181.177 91.666 M 130.405 93.250 L 128.500 95.500 130.750 93.595 C 131.988 92.547, 133 91.535, 133 91.345 C 133 90.545, 132.195 91.136, 130.405 93.250 M 179.286 97.500 C 179.294 100.250, 179.488 101.256, 179.718 99.736 C 179.947 98.216, 179.941 95.966, 179.704 94.736 C 179.467 93.506, 179.279 94.750, 179.286 97.500 M 65 95.902 C 65 96.940, 66.902 98.443, 67.466 97.850 C 67.649 97.658, 67.169 96.920, 66.399 96.211 C 65.630 95.501, 65 95.362, 65 95.902 M 150.521 96.976 C 149.538 98.063, 148.403 98.749, 148 98.500 C 147.597 98.251, 146.420 99.014, 145.384 100.195 C 144.348 101.377, 144.063 102.015, 144.750 101.613 C 146.534 100.570, 146.294 101.988, 144.275 104.421 C 143.327 105.565, 144.487 104.700, 146.855 102.500 C 150.670 98.954, 153.746 95, 152.690 95 C 152.481 95, 151.505 95.889, 150.521 96.976 M 199.733 97.124 C 200.412 97.808, 201.219 98.115, 201.526 97.807 C 201.834 97.499, 201.279 96.940, 200.293 96.564 C 198.868 96.022, 198.753 96.136, 199.733 97.124 M 99.158 101 C 99.158 102.375, 99.385 102.938, 99.662 102.250 C 99.940 101.563, 99.940 100.438, 99.662 99.750 C 99.385 99.063, 99.158 99.625, 99.158 101 M 123.405 101.250 L 121.500 103.500 123.750 101.595 C 124.987 100.547, 126 99.535, 126 99.345 C 126 98.545, 125.195 99.136, 123.405 101.250 M 113 103.378 C 113 103.585, 113.787 104.373, 114.750 105.128 C 116.336 106.371, 116.371 106.336, 115.128 104.750 C 113.821 103.084, 113 102.555, 113 103.378 M 102 106.500 C 103.292 107.875, 104.574 109, 104.849 109 C 105.124 109, 104.292 107.875, 103 106.500 C 101.708 105.125, 100.426 104, 100.151 104 C 99.876 104, 100.708 105.125, 102 106.500 M 181 106.291 C 181 108.146, 183.965 110.039, 186.684 109.921 C 188.081 109.860, 188.154 109.715, 187 109.294 C 186.175 108.992, 184.488 107.824, 183.250 106.698 C 181.331 104.953, 181 104.893, 181 106.291 M 137.924 108.661 C 136.591 110.125, 136.063 110.998, 136.750 110.602 C 137.438 110.206, 138 110.280, 138 110.767 C 138 111.987, 133.473 116.139, 132.836 115.503 C 132.553 115.220, 131.461 115.968, 130.411 117.166 C 129.360 118.364, 129.063 119.015, 129.750 118.613 C 132.158 117.205, 130.969 119.637, 127.702 122.804 C 125.889 124.562, 123.730 126, 122.905 126 C 122.081 126, 119.627 124.090, 117.453 121.756 C 113.667 117.692, 113.648 117.639, 117 120.504 L 120.500 123.496 117.054 119.748 C 113.696 116.096, 112.251 114.979, 113.500 117 C 113.840 117.550, 113.475 118, 112.689 118 C 111.173 118, 110.003 122.888, 108.021 137.500 C 107.425 141.900, 106.717 146.512, 106.449 147.750 L 105.962 150 128.481 150 C 140.866 150, 151 149.801, 151 149.558 C 151 148.426, 143.794 116.066, 142.803 112.750 C 142.171 110.634, 141.045 109, 140.219 109 C 139.031 109, 138.990 108.717, 140 107.500 C 142.130 104.933, 140.479 105.857, 137.924 108.661 M 72.241 108.986 C 70.683 111.480, 70.685 111.996, 72.250 112.015 C 72.938 112.024, 74.157 112.446, 74.960 112.954 C 76.231 113.758, 76.217 114.113, 74.852 115.689 C 73.595 117.140, 71 123.159, 71 124.624 C 71 124.802, 72.350 125.647, 74 126.500 L 77 128.051 77 117.526 C 77 108.146, 76.808 107, 75.241 107 C 74.273 107, 72.923 107.894, 72.241 108.986 M 73.872 109.750 C 72.629 111.336, 72.664 111.371, 74.250 110.128 C 75.916 108.821, 76.445 108, 75.622 108 C 75.415 108, 74.627 108.787, 73.872 109.750 M 173.336 115 C 173.336 119.125, 173.513 120.813, 173.728 118.750 C 173.944 116.688, 173.944 113.313, 173.728 111.250 C 173.513 109.188, 173.336 110.875, 173.336 115 M 59 110 C 58.099 110.582, 57.975 110.975, 58.691 110.985 C 59.346 110.993, 60.160 110.550, 60.500 110 C 61.267 108.758, 60.921 108.758, 59 110 M 115 109.378 C 115 109.585, 115.787 110.373, 116.750 111.128 C 118.336 112.371, 118.371 112.336, 117.128 110.750 C 115.821 109.084, 115 108.555, 115 109.378 M 169.975 113.565 C 168.680 116.971, 168.735 117.535, 170.500 119 C 171.797 120.076, 172 119.618, 172 115.622 C 172 110.412, 171.404 109.806, 169.975 113.565 M 184.750 112.662 C 185.438 112.940, 186.563 112.940, 187.250 112.662 C 187.938 112.385, 187.375 112.158, 186 112.158 C 184.625 112.158, 184.063 112.385, 184.750 112.662 M 181 120.719 C 181 127.823, 181.253 128.889, 183.426 130.930 C 185.791 133.152, 188.582 132.959, 187.691 130.636 C 187.446 129.998, 187.583 127.247, 187.996 124.525 C 188.817 119.119, 187.595 114, 185.484 114 C 184.758 114, 183.452 113.727, 182.582 113.393 C 181.221 112.871, 181 113.896, 181 120.719 M 123.500 124 C 121.870 124.701, 121.812 124.872, 123.191 124.930 C 124.121 124.968, 125.160 124.550, 125.500 124 C 126.211 122.850, 126.176 122.850, 123.500 124 M 179.232 131 C 179.232 132.925, 179.438 133.713, 179.689 132.750 C 179.941 131.787, 179.941 130.213, 179.689 129.250 C 179.438 128.287, 179.232 129.075, 179.232 131 M 179.355 143.500 C 179.352 148.450, 179.521 150.601, 179.731 148.280 C 179.940 145.959, 179.943 141.909, 179.736 139.280 C 179.530 136.651, 179.358 138.550, 179.355 143.500 M 64.547 151.209 C 65.898 161.958, 68.800 168.674, 71.626 167.590 C 72.576 167.226, 72.848 167.437, 72.388 168.181 C 71.905 168.962, 72.231 169.125, 73.388 168.681 C 74.334 168.318, 74.850 168.434, 74.537 168.939 C 73.803 170.128, 77.627 171.349, 79.146 170.410 C 79.828 169.988, 80.041 170.125, 79.660 170.741 C 78.944 171.899, 85.970 172.902, 89.780 172.184 C 92.582 171.657, 93.543 169.880, 94.342 163.750 L 94.962 159 87.552 159 C 80.495 159, 78.013 158.117, 76.839 155.189 C 76.653 154.724, 73.694 152.709, 70.264 150.711 L 64.028 147.079 64.547 151.209 M 95 149 C 94.099 149.582, 93.975 149.975, 94.691 149.985 C 95.346 149.993, 96.160 149.550, 96.500 149 C 97.267 147.758, 96.921 147.758, 95 149 M 92.269 151.693 C 93.242 151.947, 94.592 151.930, 95.269 151.656 C 95.946 151.382, 95.150 151.175, 93.500 151.195 C 91.850 151.215, 91.296 151.439, 92.269 151.693 M 117.250 151.747 C 123.712 151.921, 134.287 151.921, 140.750 151.747 C 147.213 151.573, 141.925 151.430, 129 151.430 C 116.075 151.430, 110.787 151.573, 117.250 151.747 M 104.607 160.582 C 103.021 164.716, 104.774 178.620, 107.574 184.108 C 108.789 186.490, 109.320 186.676, 113.713 186.254 C 117.497 185.890, 119.047 186.218, 120.990 187.791 C 123.131 189.525, 123.327 190.169, 122.459 192.636 C 121.909 194.200, 120.793 195.528, 119.979 195.587 C 119.104 195.649, 119.010 195.521, 119.750 195.273 C 120.438 195.042, 121 194.421, 121 193.891 C 121 193.299, 120.328 193.275, 119.250 193.830 C 118.287 194.325, 116.858 194.930, 116.073 195.174 C 114.585 195.637, 120.699 200, 122.835 200 C 123.552 200, 124.033 196.089, 124.217 188.750 L 124.500 177.500 129 177.500 L 133.500 177.500 134 185 C 134.340 190.100, 134.924 192.588, 135.824 192.775 C 136.553 192.926, 136.869 192.598, 136.528 192.045 C 136.187 191.493, 136.394 190.132, 136.989 189.021 C 137.795 187.515, 139.080 187, 142.035 187 C 144.216 187, 146 187.477, 146 188.059 C 146 188.641, 146.450 188.840, 147 188.500 C 147.550 188.160, 148.068 185.771, 148.150 183.191 L 148.300 178.500 149 183 L 149.700 187.500 149.850 183.250 C 149.996 179.103, 150.083 179, 153.441 179 C 155.334 179, 157.096 178.654, 157.357 178.232 C 157.618 177.809, 155.957 177.561, 153.666 177.680 C 151.375 177.800, 150.236 177.673, 151.135 177.398 C 153.608 176.641, 154.932 167.078, 153.370 161.250 L 152.767 159 128.990 159 C 108.232 159, 105.137 159.201, 104.607 160.582 M 68.031 161.500 C 68.031 162.050, 68.467 163.175, 69 164 C 69.533 164.825, 69.969 165.050, 69.969 164.500 C 69.969 163.950, 69.533 162.825, 69 162 C 68.467 161.175, 68.031 160.950, 68.031 161.500 M 171.488 163.527 C 171.769 164.887, 172.243 167.162, 172.539 168.581 L 173.079 171.163 175.589 167.464 C 176.969 165.430, 177.852 163.518, 177.549 163.216 C 177.247 162.914, 177 163.278, 177 164.024 C 177 165.065, 176.708 165.020, 175.750 163.831 C 175.063 162.978, 173.707 162.004, 172.738 161.666 C 171.291 161.163, 171.067 161.497, 171.488 163.527 M 169 171.333 C 169 174.450, 169.434 177, 169.965 177 C 170.862 177, 170.351 167.059, 169.404 166.083 C 169.182 165.854, 169 168.217, 169 171.333 M 39.813 177.683 C 40.534 177.972, 41.397 177.936, 41.729 177.604 C 42.061 177.272, 41.471 177.036, 40.417 177.079 C 39.252 177.127, 39.015 177.364, 39.813 177.683 M 163.553 177.915 C 163.231 178.436, 163.394 179.125, 163.915 179.447 C 164.436 179.769, 165.125 179.606, 165.447 179.085 C 165.769 178.564, 165.606 177.875, 165.085 177.553 C 164.564 177.231, 163.875 177.394, 163.553 177.915 M 46 178.668 C 46.825 179.015, 47.872 179.795, 48.326 180.400 C 48.857 181.107, 49.006 180.965, 48.743 180 C 48.518 179.175, 47.472 178.395, 46.417 178.268 C 45.070 178.104, 44.946 178.223, 46 178.668 M 52 196 L 52 214 56 214 L 60 214 59.796 205.250 L 59.593 196.500 59.256 204.750 L 58.919 213 55.960 213 L 53 213 53 196 L 53 179 55.952 179 C 58.869 179, 58.909 179.075, 59.260 185.250 L 59.615 191.500 59.807 184.750 L 60 178 56 178 L 52 178 52 196 M 125 196.060 L 125 214.121 128.750 213.810 L 132.500 213.500 132.595 208 L 132.691 202.500 132.293 207.750 C 131.910 212.806, 131.787 213, 128.948 213 L 126 213 126 196 L 126 179 128.962 179 L 131.925 179 132.255 188.250 L 132.585 197.500 132.792 187.750 L 133 178 129 178 L 125 178 125 196.060 M 39.762 184.707 C 41.006 184.946, 42.806 184.937, 43.762 184.687 C 44.718 184.437, 43.700 184.241, 41.500 184.252 C 39.300 184.263, 38.518 184.468, 39.762 184.707 M 67.500 185.062 L 65.500 185.983 67.500 185.712 C 68.600 185.563, 70.620 186.466, 71.990 187.720 C 74.836 190.326, 76.288 190.562, 77.855 188.675 C 78.711 187.643, 78.176 186.977, 75.444 185.675 C 71.533 183.810, 70.407 183.723, 67.500 185.062 M 36.001 187.499 C 34.931 188.788, 35.160 188.920, 37.628 188.436 C 39.208 188.127, 41.274 187.765, 42.221 187.632 C 45.809 187.130, 45.164 186, 41.289 186 C 38.915 186, 36.731 186.619, 36.001 187.499 M 47.655 188.829 C 46.745 189.835, 46.014 191.072, 46.030 191.579 C 46.047 192.085, 46.571 191.620, 47.195 190.544 C 48.321 188.603, 48.335 188.603, 49.099 190.544 C 49.727 192.140, 49.841 191.994, 49.716 189.750 C 49.632 188.238, 49.507 187, 49.437 187 C 49.367 187, 48.565 187.823, 47.655 188.829 M 65.750 187.662 C 66.438 187.940, 67.563 187.940, 68.250 187.662 C 68.938 187.385, 68.375 187.158, 67 187.158 C 65.625 187.158, 65.063 187.385, 65.750 187.662 M 88.750 187.662 C 89.438 187.940, 90.563 187.940, 91.250 187.662 C 91.938 187.385, 91.375 187.158, 90 187.158 C 88.625 187.158, 88.063 187.385, 88.750 187.662 M 113.269 187.693 C 114.242 187.947, 115.592 187.930, 116.269 187.656 C 116.946 187.382, 116.150 187.175, 114.500 187.195 C 112.850 187.215, 112.296 187.439, 113.269 187.693 M 168.250 187.732 L 164 188.115 164 200.990 C 164 213.449, 163.927 213.890, 161.750 214.631 C 160.512 215.052, 159.129 215.296, 158.676 215.173 C 158.222 215.051, 158.136 215.412, 158.485 215.975 C 159.278 217.259, 159.025 217.253, 155.150 215.903 C 153.419 215.299, 152.222 214.450, 152.490 214.016 C 152.758 213.582, 152.195 212.613, 151.239 211.863 C 149.665 210.630, 149.630 210.666, 150.872 212.250 C 151.771 213.395, 151.857 214, 151.122 214 C 150.505 214, 149.786 214.070, 149.524 214.155 C 145.756 215.380, 139.812 215.478, 138.859 214.330 C 137.949 213.234, 137.437 213.216, 136.175 214.238 C 134.900 215.270, 134.764 215.254, 135.429 214.150 C 136.087 213.057, 135.839 213.016, 134.121 213.935 C 132.954 214.560, 132 215.306, 132 215.594 C 132 215.882, 132.521 215.796, 133.158 215.403 C 133.958 214.908, 134.133 215.385, 133.723 216.953 C 133.148 219.148, 133.296 219.200, 138.505 218.632 C 141.785 218.274, 145.364 218.545, 147.690 219.325 C 152.093 220.804, 154.716 220.757, 157.726 219.147 C 158.904 218.516, 161.505 218, 163.506 218 C 165.536 218, 167.864 217.279, 168.775 216.368 C 170.284 214.859, 170.247 214.787, 168.288 215.409 C 167.122 215.779, 165.879 215.613, 165.525 215.041 C 165.126 214.395, 166.214 214, 168.387 214 L 171.892 214 172.196 200.498 C 172.363 193.072, 172.500 187.076, 172.500 187.173 C 172.500 187.270, 170.588 187.522, 168.250 187.732 M 184.269 187.693 C 185.242 187.947, 186.592 187.930, 187.269 187.656 C 187.946 187.382, 187.150 187.175, 185.500 187.195 C 183.850 187.215, 183.296 187.439, 184.269 187.693 M 222.269 187.693 C 223.242 187.947, 224.592 187.930, 225.269 187.656 C 225.946 187.382, 225.150 187.175, 223.500 187.195 C 221.850 187.215, 221.296 187.439, 222.269 187.693 M 94 188.393 C 94 188.609, 94.698 189.054, 95.552 189.382 C 96.442 189.723, 96.843 189.555, 96.493 188.989 C 95.906 188.038, 94 187.583, 94 188.393 M 106.325 188.658 C 106.687 189.020, 106.424 190.032, 105.741 190.908 C 104.645 192.314, 104.703 192.341, 106.239 191.137 C 107.803 189.910, 107.825 188, 106.274 188 C 105.940 188, 105.963 188.296, 106.325 188.658 M 119.600 188.674 C 120.205 189.128, 120.985 190.175, 121.332 191 C 121.777 192.054, 121.896 191.930, 121.732 190.583 C 121.605 189.528, 120.825 188.482, 120 188.257 C 119.035 187.994, 118.893 188.143, 119.600 188.674 M 138.629 188.791 C 138.360 189.226, 139.684 189.402, 141.570 189.182 C 143.457 188.961, 145 188.605, 145 188.391 C 145 187.638, 139.113 188.008, 138.629 188.791 M 195.187 191.173 C 195.359 192.918, 196.074 194.459, 196.775 194.598 C 197.476 194.737, 197.589 194.621, 197.025 194.342 C 195.298 193.485, 195.849 189.186, 197.750 188.689 C 199.032 188.354, 198.881 188.201, 197.187 188.116 C 195.170 188.015, 194.913 188.407, 195.187 191.173 M 207.250 188.689 C 208.213 188.941, 209.787 188.941, 210.750 188.689 C 211.713 188.438, 210.925 188.232, 209 188.232 C 207.075 188.232, 206.287 188.438, 207.250 188.689 M 165 201 L 165 213 168 213 L 171 213 171 201 L 171 189 168 189 L 165 189 165 201 M 24.079 191.583 C 24.127 192.748, 24.364 192.985, 24.683 192.188 C 24.972 191.466, 24.936 190.603, 24.604 190.271 C 24.272 189.939, 24.036 190.529, 24.079 191.583 M 144.603 190.833 C 144.320 191.292, 144.723 191.667, 145.500 191.667 C 146.277 191.667, 146.680 191.292, 146.397 190.833 C 146.114 190.375, 145.710 190, 145.500 190 C 145.290 190, 144.886 190.375, 144.603 190.833 M 176.232 195 C 176.232 196.925, 176.438 197.713, 176.689 196.750 C 176.941 195.787, 176.941 194.213, 176.689 193.250 C 176.438 192.287, 176.232 193.075, 176.232 195 M 88.750 193.662 C 89.438 193.940, 90.563 193.940, 91.250 193.662 C 91.938 193.385, 91.375 193.158, 90 193.158 C 88.625 193.158, 88.063 193.385, 88.750 193.662 M 183.629 193.791 C 183.360 194.226, 184.684 194.402, 186.570 194.182 C 188.457 193.961, 190 193.605, 190 193.391 C 190 192.638, 184.113 193.008, 183.629 193.791 M 221.612 193.819 C 221.333 194.270, 222.432 194.443, 224.053 194.206 C 225.674 193.968, 227 193.599, 227 193.387 C 227 192.625, 222.107 193.017, 221.612 193.819 M 74.150 203.250 L 74 213 71.039 213 L 68.078 213 67.782 204.250 L 67.486 195.500 66.993 205.005 L 66.500 214.510 70.369 214.139 C 72.497 213.934, 74.522 214.227, 74.869 214.789 C 75.216 215.351, 75.371 211.916, 75.214 207.156 C 74.782 194.059, 74.317 192.353, 74.150 203.250 M 173.604 195.750 C 171.902 201.367, 173.935 205.825, 178 205.391 C 178.825 205.303, 180.149 205.179, 180.941 205.115 C 182.007 205.030, 181.877 204.618, 180.441 203.532 C 179.374 202.724, 178.893 202.049, 179.372 202.032 C 179.852 202.014, 179.711 201.356, 179.058 200.570 C 178.405 199.783, 177.045 199.299, 176.035 199.493 C 174.514 199.786, 174.278 199.344, 174.659 196.923 C 175.162 193.732, 174.454 192.945, 173.604 195.750 M 191.834 194.876 C 192.890 195.545, 192.805 195.861, 191.428 196.389 C 189.951 196.956, 190.195 197.430, 193.038 199.528 C 197.322 202.689, 198.347 202.726, 194.699 199.589 C 193.159 198.264, 192.344 196.905, 192.888 196.569 C 193.432 196.233, 194.341 196.755, 194.908 197.729 C 195.475 198.703, 195.953 199.007, 195.970 198.405 C 196.005 197.124, 192.761 193.993, 191.421 194.015 C 190.915 194.024, 191.100 194.411, 191.834 194.876 M 208.250 194.689 C 209.213 194.941, 210.787 194.941, 211.750 194.689 C 212.713 194.438, 211.925 194.232, 210 194.232 C 208.075 194.232, 207.287 194.438, 208.250 194.689 M 87.504 195.993 C 87.148 196.569, 87.964 196.898, 89.445 196.775 C 90.850 196.659, 92 196.212, 92 195.782 C 92 194.645, 88.228 194.822, 87.504 195.993 M 100.232 198 C 100.232 199.925, 100.438 200.713, 100.689 199.750 C 100.941 198.787, 100.941 197.213, 100.689 196.250 C 100.438 195.287, 100.232 196.075, 100.232 198 M 187.510 196.016 C 187.856 196.575, 188.557 196.774, 189.069 196.457 C 190.456 195.600, 190.198 195, 188.441 195 C 187.584 195, 187.165 195.457, 187.510 196.016 M 78.079 197.583 C 78.127 198.748, 78.364 198.985, 78.683 198.188 C 78.972 197.466, 78.936 196.603, 78.604 196.271 C 78.272 195.939, 78.036 196.529, 78.079 197.583 M 24.158 201 C 24.158 202.375, 24.385 202.938, 24.662 202.250 C 24.940 201.563, 24.940 200.438, 24.662 199.750 C 24.385 199.063, 24.158 199.625, 24.158 201 M 215 199.378 C 215 199.585, 215.787 200.373, 216.750 201.128 C 218.336 202.371, 218.371 202.336, 217.128 200.750 C 215.821 199.084, 215 198.555, 215 199.378 M 177 201 C 177 201.550, 177.477 202, 178.059 202 C 178.641 202, 178.840 201.550, 178.500 201 C 178.160 200.450, 177.684 200, 177.441 200 C 177.198 200, 177 200.450, 177 201 M 78.158 204 C 78.158 205.375, 78.385 205.938, 78.662 205.250 C 78.940 204.563, 78.940 203.438, 78.662 202.750 C 78.385 202.063, 78.158 202.625, 78.158 204 M 93.750 202.706 C 94.987 202.944, 97.013 202.944, 98.250 202.706 C 99.487 202.467, 98.475 202.272, 96 202.272 C 93.525 202.272, 92.513 202.467, 93.750 202.706 M 186.731 206.080 C 187.085 207.432, 187.538 207.886, 187.788 207.137 C 188.028 206.415, 187.750 205.350, 187.168 204.768 C 186.426 204.026, 186.296 204.417, 186.731 206.080 M 194.158 206 C 194.158 207.375, 194.385 207.938, 194.662 207.250 C 194.940 206.563, 194.940 205.438, 194.662 204.750 C 194.385 204.063, 194.158 204.625, 194.158 206 M 217.477 205.037 C 216.935 205.915, 219.388 207, 221.915 207 C 222.878 207, 223.666 206.438, 223.665 205.750 C 223.663 204.408, 218.251 203.784, 217.477 205.037 M 232.158 206 C 232.158 207.375, 232.385 207.938, 232.662 207.250 C 232.940 206.563, 232.940 205.438, 232.662 204.750 C 232.385 204.063, 232.158 204.625, 232.158 206 M 212.039 206.479 C 209.608 207.780, 209.609 207.782, 212.802 207.652 C 214.561 207.580, 216 207.205, 216 206.820 C 216 205.360, 214.389 205.221, 212.039 206.479 M 225.079 206.583 C 225.127 207.748, 225.364 207.985, 225.683 207.188 C 225.972 206.466, 225.936 205.603, 225.604 205.271 C 225.272 204.939, 225.036 205.529, 225.079 206.583 M 39.250 207.689 C 40.212 207.941, 41.788 207.941, 42.750 207.689 C 43.712 207.438, 42.925 207.232, 41 207.232 C 39.075 207.232, 38.288 207.438, 39.250 207.689 M 78.144 209.750 C 77.127 212.128, 77.152 212.266, 78.333 210.766 C 79.083 209.812, 80.015 209.348, 80.402 209.735 C 80.789 210.122, 80.809 209.665, 80.446 208.720 C 79.589 206.486, 79.530 206.512, 78.144 209.750 M 98.439 208.099 C 97.941 208.905, 98.112 209.049, 98.953 208.529 C 99.884 207.954, 100.058 208.372, 99.616 210.125 C 99.288 211.431, 99.277 211.938, 99.593 211.250 C 99.908 210.563, 100.842 210, 101.667 210 C 103.604 210, 102.827 207.596, 100.760 207.195 C 99.895 207.027, 98.850 207.434, 98.439 208.099 M 160.731 209.080 C 161.085 210.432, 161.538 210.886, 161.788 210.137 C 162.028 209.415, 161.750 208.350, 161.168 207.768 C 160.426 207.026, 160.296 207.417, 160.731 209.080 M 28.500 210 C 29.495 211.100, 30.535 212, 30.810 212 C 31.085 212, 30.495 211.100, 29.500 210 C 28.505 208.900, 27.465 208, 27.190 208 C 26.915 208, 27.505 208.900, 28.500 210 M 90.269 208.693 C 91.242 208.947, 92.592 208.930, 93.269 208.656 C 93.946 208.382, 93.150 208.175, 91.500 208.195 C 89.850 208.215, 89.296 208.439, 90.269 208.693 M 182.750 208.662 C 183.438 208.940, 184.563 208.940, 185.250 208.662 C 185.938 208.385, 185.375 208.158, 184 208.158 C 182.625 208.158, 182.063 208.385, 182.750 208.662 M 220.750 208.662 C 221.438 208.940, 222.563 208.940, 223.250 208.662 C 223.938 208.385, 223.375 208.158, 222 208.158 C 220.625 208.158, 220.063 208.385, 220.750 208.662 M 101.478 211.640 C 100.422 212.412, 98.959 212.814, 98.227 212.534 C 97.495 212.253, 97.163 212.455, 97.489 212.982 C 97.815 213.510, 96.826 214.242, 95.291 214.609 C 90.851 215.671, 90.653 216.111, 93.762 218.001 C 97.489 220.268, 102.457 219.858, 105.461 217.037 C 109.837 212.926, 106.291 208.120, 101.478 211.640 M 191.543 211.931 C 191.226 212.443, 191.350 213.098, 191.817 213.387 C 192.284 213.676, 192.667 213.257, 192.667 212.456 C 192.667 210.770, 192.351 210.623, 191.543 211.931 M 79.569 213.682 C 78.606 216.191, 80.086 216.894, 84.500 216.021 C 86.700 215.586, 87.588 215.178, 86.473 215.115 C 85.357 215.052, 83.491 214.331, 82.324 213.514 C 80.425 212.184, 80.137 212.202, 79.569 213.682 M 160 213 C 159.099 213.582, 158.975 213.975, 159.691 213.985 C 160.346 213.993, 161.160 213.550, 161.500 213 C 162.267 211.758, 161.921 211.758, 160 213 M 44.813 213.683 C 45.534 213.972, 46.397 213.936, 46.729 213.604 C 47.061 213.272, 46.471 213.036, 45.417 213.079 C 44.252 213.127, 44.015 213.364, 44.813 213.683 M 141.264 213.718 C 142.784 213.947, 145.034 213.941, 146.264 213.704 C 147.494 213.467, 146.250 213.279, 143.500 213.286 C 140.750 213.294, 139.744 213.488, 141.264 213.718 M 174.500 214 C 174.840 214.550, 175.568 215, 176.118 215 C 176.668 215, 176.840 214.550, 176.500 214 C 176.160 213.450, 175.432 213, 174.882 213 C 174.332 213, 174.160 213.450, 174.500 214 M 101.583 215.866 C 101.152 216.563, 101.070 217.403, 101.400 217.733 C 102.295 218.628, 103.271 217.312, 102.787 215.862 C 102.449 214.846, 102.213 214.846, 101.583 215.866 M 171.325 215.658 C 171.687 216.020, 171.511 216.884, 170.934 217.579 C 170.131 218.547, 170.372 218.715, 171.964 218.299 C 173.599 217.871, 173.799 217.462, 172.899 216.378 C 172.270 215.620, 171.510 215, 171.211 215 C 170.912 215, 170.963 215.296, 171.325 215.658\",stroke:\"none\",fill:\"#635597\",fillRule:\"evenodd\"}),wp.element.createElement(\"path\",{d:\"M -0 128.004 L -0 256.008 128.250 255.754 L 256.500 255.500 256.754 127.750 L 257.008 0 128.504 0 L 0 0 -0 128.004 M 0.485 128.500 C 0.485 198.900, 0.604 227.553, 0.750 192.172 C 0.896 156.792, 0.896 99.192, 0.750 64.172 C 0.604 29.153, 0.485 58.100, 0.485 128.500 M 79.882 26.750 C 79.816 27.163, 79.816 27.837, 79.882 28.250 C 79.947 28.663, 79.438 28.887, 78.750 28.750 C 77.819 28.564, 77.428 31.500, 77.218 40.250 C 76.967 50.725, 76.744 52, 75.164 52 C 74.189 52, 71.254 53.356, 68.641 55.013 C 63.756 58.111, 60.067 64.017, 60.022 68.816 C 59.993 71.829, 58.714 74.132, 55.013 77.833 C 52.697 80.149, 52 81.745, 52 84.727 C 52 89.414, 56.042 96.241, 61.463 100.709 C 65.159 103.755, 65.178 103.808, 62.855 104.525 C 61.560 104.925, 57.666 107.220, 54.202 109.626 C 45.125 115.930, 43.269 115.388, 46.603 107.408 C 48.258 103.447, 48.033 96.633, 46.185 94.785 C 45.555 94.155, 43.598 94.527, 40.625 95.842 C 38.012 96.997, 35.789 97.468, 35.460 96.935 C 34.455 95.310, 31.801 95.914, 28.335 98.558 C 24.455 101.517, 21.550 106.795, 20.667 112.485 C 20.076 116.297, 20.279 116.687, 25.349 121.485 C 28.263 124.243, 33.273 129.788, 36.482 133.807 C 40.333 138.629, 43.210 141.292, 44.945 141.639 C 47.330 142.116, 47.593 142.634, 47.794 147.253 C 48.231 157.298, 51.932 166.258, 58.917 174.178 C 61.186 176.751, 62.630 179.110, 62.126 179.422 C 61.622 179.734, 60.942 179.290, 60.614 178.436 C 60.176 177.296, 58.889 176.966, 55.759 177.192 C 52.073 177.459, 51.455 177.837, 51.165 180 C 50.981 181.375, 50.368 182.950, 49.803 183.500 C 49.103 184.181, 48.997 183.767, 49.470 182.202 C 50.441 178.991, 48.594 177.212, 43.493 176.447 C 38.882 175.755, 31.576 177.505, 32.543 179.069 C 32.851 179.569, 32.478 179.737, 31.712 179.443 C 30.947 179.149, 29.686 179.691, 28.910 180.646 C 27.683 182.159, 27.694 182.274, 29 181.532 C 29.894 181.024, 30.163 181.047, 29.667 181.590 C 29.208 182.090, 28.308 182.500, 27.667 182.500 C 23.754 182.500, 21.449 198.351, 24.351 205.298 C 26.382 210.158, 32.177 214.756, 37.208 215.499 C 43.375 216.410, 49.412 214.251, 49.792 210.999 C 50.340 206.315, 48.227 204.967, 41.751 205.866 C 38.589 206.306, 36.222 206.444, 36.492 206.175 C 37.234 205.432, 34.891 202.949, 33.980 203.512 C 33.544 203.782, 33.409 202.990, 33.680 201.751 C 34.089 199.885, 33.975 199.757, 33.011 201 C 31.977 202.333, 31.894 202.333, 32.267 201 C 32.721 199.378, 36.575 199.126, 43.500 200.266 C 45.700 200.628, 48.267 200.941, 49.204 200.962 C 50.608 200.993, 50.961 202.191, 51.204 207.750 L 51.500 214.500 56 214.500 L 60.500 214.500 60.798 208.250 C 61.095 202.008, 61.443 201.465, 64.418 202.607 C 66.594 203.442, 66.489 209.802, 64.259 212.267 C 62.594 214.106, 62.621 214.274, 64.858 216.086 C 71.800 221.707, 93.549 224.531, 129.250 224.447 C 156.544 224.382, 162.885 224.146, 171 222.897 C 174.575 222.347, 179.075 221.667, 181 221.388 C 192.401 219.732, 199.668 215.715, 197.025 212.530 C 196.336 211.701, 195.380 211.265, 194.898 211.563 C 194.417 211.860, 194.291 211.406, 194.618 210.552 C 195.329 208.701, 197.993 208.465, 198.015 210.250 C 198.026 211.086, 198.357 211.004, 199.018 210 C 199.791 208.826, 199.951 209.034, 199.753 210.954 C 199.401 214.367, 203.442 216.296, 208.648 215.201 C 211.950 214.506, 212.466 214.041, 212.259 211.945 C 212.024 209.557, 212.040 209.552, 212.957 211.698 C 215.230 217.014, 227.668 216.780, 231.946 211.341 C 236.029 206.151, 233.842 200.226, 226.628 196.927 L 222.500 195.039 227 195.270 C 231.059 195.477, 231.493 195.284, 231.426 193.300 C 231.385 192.090, 231.766 191.355, 232.272 191.668 C 232.778 191.981, 233.032 191.396, 232.835 190.368 C 232.292 187.526, 225.117 185.362, 220.323 186.594 C 218.220 187.135, 216.613 188.122, 216.750 188.789 C 216.888 189.455, 216.438 189.888, 215.750 189.750 C 214.875 189.575, 214.563 190.848, 214.709 194 L 214.918 198.500 213.709 193 C 212.631 188.098, 212.201 187.466, 209.750 187.184 C 207.854 186.965, 207 186.292, 207 185.015 C 207 180.915, 206.056 180.087, 202.673 181.224 C 198.518 182.620, 196.798 184.031, 197.385 185.562 C 198.075 187.359, 194.528 187.984, 190.285 186.813 C 188.268 186.256, 185.092 186.048, 183.227 186.351 L 179.837 186.901 183.834 182.565 C 186.032 180.180, 188.582 176.776, 189.500 175 L 191.169 171.772 194.464 174.965 L 197.758 178.158 199.409 173.329 C 201.946 165.903, 202.557 155.247, 200.699 150.813 C 198.579 145.753, 199.398 143.868, 204.836 141.283 C 210.845 138.426, 221.784 128.500, 229.181 119.191 C 234.185 112.895, 234.999 111.283, 234.994 107.684 C 234.988 102.735, 233.275 96, 232.023 96 C 231.538 96, 230.177 94.071, 229 91.714 C 225.799 85.305, 220.402 79.260, 219.054 80.574 C 218.448 81.164, 217.812 85.269, 217.639 89.696 C 217.374 96.517, 216.882 98.467, 214.413 102.476 C 210.830 108.295, 204.881 112.626, 199.258 113.508 C 195.361 114.119, 194.938 113.968, 194.055 111.645 C 193.527 110.255, 192.960 107.883, 192.797 106.375 C 192.532 103.930, 192.911 103.544, 196.282 102.825 C 202.658 101.464, 206 99.258, 206 96.410 C 206 94.840, 207.099 92.946, 208.881 91.447 C 214.517 86.704, 216.554 78.503, 213.533 72.712 C 212.480 70.693, 212.344 69.225, 213.039 67.396 C 217.062 56.816, 206.777 43.237, 192.250 39.947 C 188.435 39.083, 188.284 39.673, 191.099 44.443 C 193.478 48.476, 193.087 48.819, 189.210 46.100 C 185.483 43.487, 180.553 42.392, 181.302 44.344 C 181.745 45.499, 181.091 45.688, 178.058 45.281 C 174.032 44.741, 171.887 46.287, 173.887 48.287 C 175.537 49.937, 175.256 50.700, 172.758 51.353 C 170.639 51.907, 170.791 52.231, 175.494 57.220 C 178.233 60.124, 172.717 54.855, 163.237 45.511 C 153.757 36.167, 146 28.299, 146 28.026 C 146 27.753, 151.738 33.148, 158.750 40.015 L 171.500 52.500 158.514 39.250 L 145.529 26 112.764 26 C 94.744 26, 79.947 26.337, 79.882 26.750 M 96.750 27.748 C 105.687 27.914, 120.312 27.914, 129.250 27.748 C 138.188 27.582, 130.875 27.447, 113 27.447 C 95.125 27.447, 87.813 27.582, 96.750 27.748 M 78.242 92.628 C 78.015 151.727, 78.112 155.843, 79.750 157.128 C 81.299 158.342, 81.357 158.317, 80.258 156.907 C 79.304 155.684, 78.953 140.671, 78.750 92.407 L 78.485 29.500 78.242 92.628 M 84.465 34.234 C 84.199 34.930, 84.101 61.825, 84.249 94 L 84.516 152.500 84.758 93.250 L 85 34 112.974 34 L 140.948 34 141.264 49.001 L 141.581 64.002 141.540 48.751 L 141.500 33.500 113.225 33.234 C 91.144 33.027, 84.843 33.246, 84.465 34.234 M 86.202 41.182 C 86.479 46.935, 86.686 47.413, 89.178 48.076 C 91.325 48.647, 91.741 48.489, 91.277 47.279 C 90.314 44.770, 94.840 46.697, 101.037 51.434 L 106.573 55.667 108.287 53.395 C 110.423 50.563, 110.420 50.238, 108.250 49.767 C 105.845 49.245, 112.026 45.335, 117.750 43.757 C 120.088 43.112, 122 42.276, 122 41.898 C 122 41.521, 123.306 39.814, 124.901 38.106 L 127.802 35 106.853 35 L 85.905 35 86.202 41.182 M 132 38.378 C 132 42.105, 133.130 43.397, 137.250 44.381 C 139.972 45.031, 140 44.987, 140 40.019 L 140 35 136 35 C 132.123 35, 132 35.104, 132 38.378 M 147.378 49 C 147.378 55.325, 147.541 57.912, 147.739 54.750 C 147.937 51.587, 147.937 46.412, 147.739 43.250 C 147.541 40.087, 147.378 42.675, 147.378 49 M 149 45.229 C 149 48.295, 149.810 49.715, 153.887 53.792 C 158.563 58.467, 158.866 58.610, 160.900 57.075 C 163.002 55.490, 162.949 55.395, 156.013 48.513 L 149 41.554 149 45.229 M 205.833 52.500 C 209.374 57.258, 210.579 62.049, 208.986 65.027 C 208.354 66.206, 208.083 67.416, 208.382 67.715 C 208.681 68.014, 209.449 67.110, 210.088 65.706 C 211.826 61.893, 210.414 56.785, 206.410 52.396 L 202.855 48.500 205.833 52.500 M 150.500 56 C 151.495 57.100, 152.535 58, 152.810 58 C 153.085 58, 152.495 57.100, 151.500 56 C 150.505 54.900, 149.465 54, 149.190 54 C 148.915 54, 149.505 54.900, 150.500 56 M 69.421 59.419 C 66.126 60.854, 64.519 63.894, 63.493 70.635 C 63.146 72.909, 61.976 75.722, 60.893 76.885 L 58.922 79 61.737 79 C 64.392 79, 64.532 78.784, 64.192 75.215 C 63.939 72.568, 64.407 70.719, 65.747 69.064 C 69.414 64.536, 74.720 68.345, 74.251 75.169 C 74.188 76.087, 74.781 77.086, 75.568 77.388 C 76.748 77.840, 77 76.288, 77 68.576 C 77 60.962, 76.705 59.101, 75.418 58.607 C 73.334 57.807, 73.027 57.849, 69.421 59.419 M 134.500 60.155 C 132.300 61.291, 129.037 63.609, 127.250 65.307 C 125.463 67.004, 124 67.937, 124 67.380 C 124 66.016, 116.722 62, 114.249 62 C 113.160 62, 111.137 62.891, 109.754 63.979 C 107.691 65.601, 107.108 67.171, 106.517 72.683 C 106.121 76.382, 105.020 82.370, 104.069 85.990 C 102.424 92.256, 102.425 92.571, 104.097 92.571 C 105.063 92.571, 105.635 92.218, 105.368 91.786 C 105.100 91.354, 106.285 91, 108 91 C 110.102 91, 110.886 91.376, 110.405 92.155 C 109.953 92.885, 110.131 93.037, 110.889 92.568 C 112.210 91.752, 115.255 93.778, 114.513 94.980 C 114.257 95.394, 114.862 96.470, 115.857 97.371 C 117.451 98.813, 117.667 98.824, 117.667 97.461 C 117.667 96.021, 114.197 92.644, 108.897 88.925 C 105.989 86.884, 106.962 84, 110.558 84 L 113.178 84 110.589 80.923 C 107.082 76.756, 107.197 72.650, 110.923 68.923 C 114.753 65.093, 118.100 65.142, 121.411 69.077 C 124.140 72.319, 124.688 75.846, 122.965 79.066 C 122.057 80.762, 122.272 81.002, 124.715 81.014 C 126.247 81.021, 128.564 81.312, 129.863 81.660 C 132.006 82.233, 132.123 82.098, 131.113 80.212 C 129.582 77.351, 129.719 73.444, 131.470 70.059 C 133.137 66.834, 137.799 64.748, 140.259 66.125 C 141.263 66.687, 142.107 66.635, 142.508 65.988 C 142.860 65.417, 142.553 65.074, 141.824 65.225 C 140.952 65.406, 140.394 64.221, 140.190 61.750 C 140.019 59.688, 139.569 58.020, 139.190 58.044 C 138.810 58.069, 136.700 59.018, 134.500 60.155 M 86 62.965 C 86 65.809, 86.527 67.211, 87.863 67.927 C 90.440 69.306, 92.366 74.418, 91.653 77.986 C 91.186 80.322, 91.494 81.194, 93.090 82.048 C 96.509 83.878, 95.130 87.134, 90.250 88.755 L 86 90.167 86 108.648 L 86 127.129 92.413 121.464 C 98.414 116.162, 98.764 115.621, 97.858 113.022 C 96.466 109.029, 92.528 105.629, 90.048 106.277 C 86.155 107.295, 88.017 104.865, 92.778 102.713 C 96.271 101.134, 97.989 99.606, 99.168 97.027 C 101.129 92.738, 102.295 84.300, 101.035 83.522 C 100.532 83.211, 100.376 79.905, 100.688 76.177 C 101.120 71.029, 100.868 68.646, 99.643 66.277 C 97.514 62.159, 92.935 59, 89.096 59 C 86.155 59, 86 59.199, 86 62.965 M 179.079 65.583 C 179.127 66.748, 179.364 66.985, 179.683 66.188 C 179.972 65.466, 179.936 64.603, 179.604 64.271 C 179.272 63.939, 179.036 64.529, 179.079 65.583 M 134.174 69.314 C 131.582 72.074, 131.288 77.538, 133.557 80.777 C 135.252 83.198, 137.469 83.668, 138.595 81.845 C 139 81.190, 138.873 80.960, 138.302 81.313 C 137.748 81.656, 136.578 81.388, 135.702 80.718 C 131.058 77.166, 133.897 69.183, 139.320 70.544 C 141.389 71.064, 142.267 72.040, 142.760 74.371 C 143.125 76.092, 143.290 77.950, 143.129 78.500 C 142.967 79.050, 143.997 78.409, 145.418 77.076 C 148.184 74.479, 148.990 72.934, 146.500 75 C 145.245 76.041, 145 75.891, 145 74.077 C 145 71.416, 140.769 67, 138.220 67 C 137.191 67, 135.370 68.042, 134.174 69.314 M 144.416 68.250 C 144.737 68.938, 145.266 70.246, 145.592 71.158 C 146.180 72.802, 149.116 74.052, 152 73.887 C 152.825 73.839, 152.375 73.406, 151 72.925 C 148.722 72.127, 148.788 72.076, 151.750 72.351 C 154.722 72.628, 155 72.411, 155 69.827 C 155 67.062, 154.878 67, 149.417 67 C 145.306 67, 143.987 67.330, 144.416 68.250 M 67.278 69.250 C 65.586 71.436, 64.983 74.085, 65.609 76.579 C 66.498 80.119, 67.707 79.536, 67.328 75.750 C 67.150 73.963, 67.266 73.065, 67.587 73.756 C 67.908 74.447, 68.604 74.745, 69.134 74.417 C 69.727 74.051, 69.647 73.279, 68.926 72.411 C 68.002 71.298, 68.087 71, 69.328 71 C 70.193 71, 71.359 71.787, 71.920 72.750 C 72.791 74.246, 72.944 74.269, 72.970 72.905 C 72.998 71.437, 70.071 68, 68.794 68 C 68.492 68, 67.810 68.563, 67.278 69.250 M 111.557 70.223 C 109.257 73.505, 109.623 77.714, 112.455 80.545 C 115.510 83.601, 117.059 83.631, 119.826 80.686 C 124.076 76.161, 121.615 68, 116 68 C 114.151 68, 112.554 68.799, 111.557 70.223 M 184.500 68.826 C 182.453 69.295, 181.401 70.205, 181.189 71.693 C 180.900 73.716, 181.091 73.792, 183.838 72.755 C 186.256 71.842, 187.271 71.948, 189.383 73.332 C 194.594 76.746, 193.476 82.916, 186.924 86.900 L 183.957 88.704 188.972 88.602 C 192.356 88.533, 193.990 88.078, 193.994 87.205 C 193.997 86.492, 195.105 84.805, 196.455 83.455 C 202.316 77.593, 209.333 83.294, 204.598 90.072 C 203.994 90.936, 205.188 89.866, 207.250 87.693 C 213.519 81.089, 212.009 77, 203.300 77 C 198.128 77, 198 76.931, 198 74.122 C 198 72.540, 197.343 70.699, 196.539 70.033 C 194.732 68.533, 188.509 67.909, 184.500 68.826 M 86 69.880 C 86 70.535, 86.725 72.176, 87.610 73.528 C 88.639 75.099, 88.986 76.920, 88.570 78.577 C 88.148 80.258, 88.285 80.942, 88.960 80.525 C 90.828 79.370, 90.167 72.461, 88 70.500 C 86.801 69.415, 86 69.167, 86 69.880 M 115.500 73 C 116.208 75.229, 115.037 75.637, 113.200 73.800 C 112.267 72.867, 112 73.277, 112 75.645 C 112 77.407, 112.832 79.443, 113.974 80.477 C 115.823 82.150, 116.109 82.158, 118.474 80.609 C 121.374 78.709, 121.819 74.962, 119.429 72.571 C 117.123 70.265, 114.715 70.528, 115.500 73 M 135.500 73 C 134.684 74.320, 135.880 76.192, 137.069 75.457 C 138.284 74.707, 138.276 72, 137.059 72 C 136.541 72, 135.840 72.450, 135.500 73 M 173.300 78 C 173.300 81.025, 173.487 82.263, 173.716 80.750 C 173.945 79.237, 173.945 76.763, 173.716 75.250 C 173.487 73.737, 173.300 74.975, 173.300 78 M 182.637 75.276 C 180.900 76.546, 181.244 80, 183.107 80 C 184.445 80, 184.183 78.247, 182.750 77.615 C 182.063 77.312, 182.850 77.162, 184.500 77.282 C 186.802 77.449, 187.577 78.043, 187.830 79.832 C 188.012 81.115, 187.380 83.028, 186.425 84.082 C 185.471 85.137, 185.177 86, 185.772 86 C 187.334 86, 191 81.047, 191 78.937 C 191 74.877, 186.110 72.737, 182.637 75.276 M 170.044 77.277 C 168.992 79.379, 168.936 80.336, 169.810 81.210 C 171.483 82.883, 172.192 81.675, 171.788 77.846 L 171.434 74.500 170.044 77.277 M 69.750 79.668 C 68.787 79.931, 68 80.564, 68 81.073 C 68 81.583, 67.325 82, 66.500 82 C 65.675 82, 65 82.413, 65 82.918 C 65 83.423, 64.727 84.548, 64.393 85.418 C 63.949 86.576, 64.418 87.019, 66.143 87.070 C 68.352 87.136, 68.375 87.194, 66.500 88 C 64.564 88.832, 64.569 88.862, 66.668 88.930 C 69.439 89.020, 72.119 88.001, 71.547 87.076 C 71.304 86.683, 72.462 86.561, 74.121 86.804 C 77.073 87.238, 77.129 87.166, 76.818 83.373 C 76.545 80.044, 76.149 79.478, 74 79.345 C 72.625 79.260, 70.713 79.405, 69.750 79.668 M 149.507 79.989 C 149.144 80.577, 149.626 80.718, 150.698 80.338 C 151.830 79.937, 151.233 81.042, 149.092 83.311 C 147.217 85.297, 145.355 86.720, 144.955 86.472 C 144.554 86.225, 143.613 86.778, 142.863 87.703 C 141.926 88.859, 141.890 89.149, 142.750 88.633 C 143.438 88.220, 144 88.280, 144 88.767 C 144 89.987, 139.473 94.139, 138.836 93.503 C 138.553 93.220, 137.461 93.968, 136.411 95.166 C 135.360 96.364, 135.063 97.015, 135.750 96.613 C 136.438 96.211, 137 96.280, 137 96.767 C 137 97.987, 132.473 102.139, 131.836 101.503 C 131.553 101.220, 130.461 101.968, 129.411 103.166 C 128.360 104.364, 128.063 105.015, 128.750 104.613 C 130.852 103.384, 130.140 105.520, 127.676 107.835 C 126.398 109.036, 124.975 109.677, 124.515 109.259 C 124.056 108.842, 124.003 109.063, 124.399 109.750 C 124.878 110.583, 124.411 111, 123 111 C 121.835 111, 121.141 110.581, 121.457 110.069 C 121.774 109.557, 121.506 108.813, 120.862 108.415 C 120.131 107.963, 119.959 108.124, 120.405 108.845 C 120.797 109.480, 120.711 110, 120.214 110 C 119.011 110, 115.859 106.474, 116.494 105.839 C 116.768 105.565, 115.796 104.252, 114.335 102.921 C 112.873 101.589, 112.002 101.063, 112.398 101.750 C 113.453 103.582, 112.168 103.277, 109.290 101.014 C 106.737 99.006, 104.987 99.126, 105.015 101.309 C 105.027 102.185, 105.240 102.170, 105.822 101.253 C 106.393 100.355, 107.562 100.984, 110.003 103.503 C 111.867 105.426, 114.034 107, 114.819 107 C 115.892 107, 115.972 107.329, 115.141 108.330 C 114.293 109.352, 114.778 110.339, 117.230 112.580 C 118.987 114.186, 119.823 114.713, 119.089 113.750 C 117.343 111.459, 118.571 111.517, 121.558 113.867 C 122.864 114.894, 123.610 116.127, 123.216 116.607 C 122.822 117.086, 123.513 116.626, 124.750 115.584 C 125.987 114.542, 127 113.480, 127 113.223 C 127 112.965, 126.325 113.315, 125.500 114 C 124.675 114.685, 124 114.824, 124 114.310 C 124 113.040, 129.506 107.840, 130.159 108.492 C 130.445 108.778, 131.539 108.032, 132.589 106.834 C 133.640 105.636, 133.938 104.985, 133.250 105.387 C 132.563 105.789, 132 105.720, 132 105.233 C 132 104.013, 136.527 99.861, 137.164 100.497 C 137.447 100.780, 138.539 100.032, 139.589 98.834 C 140.640 97.636, 140.938 96.985, 140.250 97.387 C 138.080 98.656, 138.895 96.447, 141.500 94 C 142.875 92.708, 144 92.025, 144 92.481 C 144 92.937, 144.889 92.505, 145.976 91.521 C 147.063 90.538, 147.694 89.314, 147.378 88.803 C 146.689 87.687, 149.946 83.849, 151.020 84.513 C 151.435 84.769, 152.329 84.308, 153.009 83.489 C 153.991 82.306, 153.989 81.691, 153 80.500 C 151.557 78.762, 150.373 78.588, 149.507 79.989 M 159.158 82 C 159.158 83.375, 159.385 83.938, 159.662 83.250 C 159.940 82.563, 159.940 81.438, 159.662 80.750 C 159.385 80.063, 159.158 80.625, 159.158 82 M 179.286 84.500 C 179.294 87.250, 179.488 88.256, 179.718 86.736 C 179.947 85.216, 179.941 82.966, 179.704 81.736 C 179.467 80.506, 179.279 81.750, 179.286 84.500 M 151.564 82.707 C 151.022 84.132, 151.136 84.247, 152.124 83.267 C 152.808 82.588, 153.115 81.781, 152.807 81.474 C 152.499 81.166, 151.940 81.721, 151.564 82.707 M 56.015 84.800 C 55.985 91.250, 61.528 97.704, 71.984 103.395 L 77 106.125 77 101.736 C 77 97.447, 76.932 97.356, 74.059 97.778 C 71.861 98.101, 70.461 97.552, 68.514 95.605 C 67.081 94.172, 65.078 93, 64.062 93 C 63.045 93, 61.958 92.333, 61.645 91.517 C 61.281 90.569, 60.430 90.278, 59.288 90.711 C 57.585 91.356, 57.583 91.304, 59.250 89.623 C 60.212 88.652, 61 87.200, 61 86.397 C 61 85.594, 60.212 84.647, 59.250 84.292 C 58.288 83.936, 57.169 83.163, 56.765 82.573 C 56.361 81.983, 56.024 82.985, 56.015 84.800 M 68.500 83 C 68.160 83.550, 68.359 84, 68.941 84 C 69.523 84, 70 83.550, 70 83 C 70 82.450, 69.802 82, 69.559 82 C 69.316 82, 68.840 82.450, 68.500 83 M 86 83.480 C 86 84.566, 87.057 85, 89.700 85 C 93.881 85, 93.176 83.262, 88.663 82.444 C 86.735 82.094, 86 82.380, 86 83.480 M 119.721 83.605 C 118.882 84.452, 117.333 84.886, 112.250 85.699 C 108.675 86.271, 108.037 87.419, 110.750 88.399 C 111.713 88.747, 113.525 89.635, 114.778 90.372 C 117.021 91.692, 122.393 91.445, 121.538 90.062 C 121.297 89.671, 122.039 89.385, 123.188 89.426 C 124.481 89.472, 125.386 88.799, 125.563 87.658 C 125.721 86.645, 126.151 85.520, 126.520 85.158 C 126.889 84.796, 127.035 85.258, 126.845 86.184 C 126.655 87.111, 126.838 87.898, 127.250 87.934 C 130.379 88.208, 132.023 87.847, 131.500 87 C 131.160 86.450, 131.584 86, 132.441 86 C 134.287 86, 134.566 84.387, 132.750 84.214 C 132.063 84.149, 129.025 83.808, 126 83.457 C 122.975 83.106, 120.149 83.173, 119.721 83.605 M 138.009 84.489 C 137.329 85.308, 136.305 85.688, 135.732 85.334 C 135.108 84.949, 134.993 85.179, 135.445 85.911 C 136.011 86.827, 136.671 86.703, 138.099 85.410 C 139.145 84.464, 140 83.535, 140 83.345 C 140 82.602, 139.189 83.068, 138.009 84.489 M 197.571 84.571 C 196.707 85.436, 196 86.261, 196 86.405 C 196 87.151, 199.591 86.557, 200.138 85.721 C 201.275 83.981, 203.109 86.075, 202.428 88.334 C 201.984 89.809, 202.104 90.181, 202.804 89.500 C 203.845 88.488, 204.335 86.532, 204.118 84.250 C 203.950 82.477, 199.445 82.698, 197.571 84.571 M 119.733 87.124 C 120.412 87.808, 121.219 88.115, 121.526 87.807 C 121.834 87.499, 121.279 86.940, 120.293 86.564 C 118.868 86.022, 118.753 86.136, 119.733 87.124 M 173.343 93.500 C 173.346 97.900, 173.522 99.576, 173.733 97.224 C 173.945 94.872, 173.942 91.272, 173.727 89.224 C 173.512 87.176, 173.339 89.100, 173.343 93.500 M 144.872 90.750 C 143.629 92.336, 143.664 92.371, 145.250 91.128 C 146.213 90.373, 147 89.585, 147 89.378 C 147 88.555, 146.179 89.084, 144.872 90.750 M 171.252 92.500 C 171.263 94.700, 171.468 95.482, 171.707 94.238 C 171.946 92.994, 171.937 91.194, 171.687 90.238 C 171.437 89.282, 171.241 90.300, 171.252 92.500 M 221.158 92.500 C 220.905 98.190, 216.619 106.726, 211.706 111.326 C 209.393 113.491, 208.400 114.792, 209.500 114.217 C 215.614 111.022, 222.413 98.823, 221.730 92.275 L 221.336 88.500 221.158 92.500 M 152.569 91.924 C 150.913 93.754, 150.914 93.836, 152.580 93.591 C 153.545 93.450, 154.444 92.584, 154.578 91.667 C 154.889 89.551, 154.694 89.575, 152.569 91.924 M 181.177 91.666 C 181.551 93.593, 188.068 97.444, 192.624 98.428 C 195.403 99.029, 195.800 98.826, 196.229 96.585 C 197.062 92.224, 193.907 90, 186.888 90 C 181.794 90, 180.904 90.260, 181.177 91.666 M 130.405 93.250 L 128.500 95.500 130.750 93.595 C 131.988 92.547, 133 91.535, 133 91.345 C 133 90.545, 132.195 91.136, 130.405 93.250 M 179.286 97.500 C 179.294 100.250, 179.488 101.256, 179.718 99.736 C 179.947 98.216, 179.941 95.966, 179.704 94.736 C 179.467 93.506, 179.279 94.750, 179.286 97.500 M 65 95.902 C 65 96.940, 66.902 98.443, 67.466 97.850 C 67.649 97.658, 67.169 96.920, 66.399 96.211 C 65.630 95.501, 65 95.362, 65 95.902 M 150.521 96.976 C 149.538 98.063, 148.403 98.749, 148 98.500 C 147.597 98.251, 146.420 99.014, 145.384 100.195 C 144.348 101.377, 144.063 102.015, 144.750 101.613 C 146.534 100.570, 146.294 101.988, 144.275 104.421 C 143.327 105.565, 144.487 104.700, 146.855 102.500 C 150.670 98.954, 153.746 95, 152.690 95 C 152.481 95, 151.505 95.889, 150.521 96.976 M 199.733 97.124 C 200.412 97.808, 201.219 98.115, 201.526 97.807 C 201.834 97.499, 201.279 96.940, 200.293 96.564 C 198.868 96.022, 198.753 96.136, 199.733 97.124 M 99.158 101 C 99.158 102.375, 99.385 102.938, 99.662 102.250 C 99.940 101.563, 99.940 100.438, 99.662 99.750 C 99.385 99.063, 99.158 99.625, 99.158 101 M 123.405 101.250 L 121.500 103.500 123.750 101.595 C 124.987 100.547, 126 99.535, 126 99.345 C 126 98.545, 125.195 99.136, 123.405 101.250 M 113 103.378 C 113 103.585, 113.787 104.373, 114.750 105.128 C 116.336 106.371, 116.371 106.336, 115.128 104.750 C 113.821 103.084, 113 102.555, 113 103.378 M 102 106.500 C 103.292 107.875, 104.574 109, 104.849 109 C 105.124 109, 104.292 107.875, 103 106.500 C 101.708 105.125, 100.426 104, 100.151 104 C 99.876 104, 100.708 105.125, 102 106.500 M 181 106.291 C 181 108.146, 183.965 110.039, 186.684 109.921 C 188.081 109.860, 188.154 109.715, 187 109.294 C 186.175 108.992, 184.488 107.824, 183.250 106.698 C 181.331 104.953, 181 104.893, 181 106.291 M 137.924 108.661 C 136.591 110.125, 136.063 110.998, 136.750 110.602 C 137.438 110.206, 138 110.280, 138 110.767 C 138 111.987, 133.473 116.139, 132.836 115.503 C 132.553 115.220, 131.461 115.968, 130.411 117.166 C 129.360 118.364, 129.063 119.015, 129.750 118.613 C 132.158 117.205, 130.969 119.637, 127.702 122.804 C 125.889 124.562, 123.730 126, 122.905 126 C 122.081 126, 119.627 124.090, 117.453 121.756 C 113.667 117.692, 113.648 117.639, 117 120.504 L 120.500 123.496 117.054 119.748 C 113.696 116.096, 112.251 114.979, 113.500 117 C 113.840 117.550, 113.475 118, 112.689 118 C 111.173 118, 110.003 122.888, 108.021 137.500 C 107.425 141.900, 106.717 146.512, 106.449 147.750 L 105.962 150 128.481 150 C 140.866 150, 151 149.801, 151 149.558 C 151 148.426, 143.794 116.066, 142.803 112.750 C 142.171 110.634, 141.045 109, 140.219 109 C 139.031 109, 138.990 108.717, 140 107.500 C 142.130 104.933, 140.479 105.857, 137.924 108.661 M 72.241 108.986 C 70.683 111.480, 70.685 111.996, 72.250 112.015 C 72.938 112.024, 74.157 112.446, 74.960 112.954 C 76.231 113.758, 76.217 114.113, 74.852 115.689 C 73.595 117.140, 71 123.159, 71 124.624 C 71 124.802, 72.350 125.647, 74 126.500 L 77 128.051 77 117.526 C 77 108.146, 76.808 107, 75.241 107 C 74.273 107, 72.923 107.894, 72.241 108.986 M 73.872 109.750 C 72.629 111.336, 72.664 111.371, 74.250 110.128 C 75.916 108.821, 76.445 108, 75.622 108 C 75.415 108, 74.627 108.787, 73.872 109.750 M 173.336 115 C 173.336 119.125, 173.513 120.813, 173.728 118.750 C 173.944 116.688, 173.944 113.313, 173.728 111.250 C 173.513 109.188, 173.336 110.875, 173.336 115 M 59 110 C 58.099 110.582, 57.975 110.975, 58.691 110.985 C 59.346 110.993, 60.160 110.550, 60.500 110 C 61.267 108.758, 60.921 108.758, 59 110 M 115 109.378 C 115 109.585, 115.787 110.373, 116.750 111.128 C 118.336 112.371, 118.371 112.336, 117.128 110.750 C 115.821 109.084, 115 108.555, 115 109.378 M 169.975 113.565 C 168.680 116.971, 168.735 117.535, 170.500 119 C 171.797 120.076, 172 119.618, 172 115.622 C 172 110.412, 171.404 109.806, 169.975 113.565 M 184.750 112.662 C 185.438 112.940, 186.563 112.940, 187.250 112.662 C 187.938 112.385, 187.375 112.158, 186 112.158 C 184.625 112.158, 184.063 112.385, 184.750 112.662 M 181 120.719 C 181 127.823, 181.253 128.889, 183.426 130.930 C 185.791 133.152, 188.582 132.959, 187.691 130.636 C 187.446 129.998, 187.583 127.247, 187.996 124.525 C 188.817 119.119, 187.595 114, 185.484 114 C 184.758 114, 183.452 113.727, 182.582 113.393 C 181.221 112.871, 181 113.896, 181 120.719 M 123.500 124 C 121.870 124.701, 121.812 124.872, 123.191 124.930 C 124.121 124.968, 125.160 124.550, 125.500 124 C 126.211 122.850, 126.176 122.850, 123.500 124 M 179.232 131 C 179.232 132.925, 179.438 133.713, 179.689 132.750 C 179.941 131.787, 179.941 130.213, 179.689 129.250 C 179.438 128.287, 179.232 129.075, 179.232 131 M 179.355 143.500 C 179.352 148.450, 179.521 150.601, 179.731 148.280 C 179.940 145.959, 179.943 141.909, 179.736 139.280 C 179.530 136.651, 179.358 138.550, 179.355 143.500 M 64.547 151.209 C 65.898 161.958, 68.800 168.674, 71.626 167.590 C 72.576 167.226, 72.848 167.437, 72.388 168.181 C 71.905 168.962, 72.231 169.125, 73.388 168.681 C 74.334 168.318, 74.850 168.434, 74.537 168.939 C 73.803 170.128, 77.627 171.349, 79.146 170.410 C 79.828 169.988, 80.041 170.125, 79.660 170.741 C 78.944 171.899, 85.970 172.902, 89.780 172.184 C 92.582 171.657, 93.543 169.880, 94.342 163.750 L 94.962 159 87.552 159 C 80.495 159, 78.013 158.117, 76.839 155.189 C 76.653 154.724, 73.694 152.709, 70.264 150.711 L 64.028 147.079 64.547 151.209 M 95 149 C 94.099 149.582, 93.975 149.975, 94.691 149.985 C 95.346 149.993, 96.160 149.550, 96.500 149 C 97.267 147.758, 96.921 147.758, 95 149 M 92.269 151.693 C 93.242 151.947, 94.592 151.930, 95.269 151.656 C 95.946 151.382, 95.150 151.175, 93.500 151.195 C 91.850 151.215, 91.296 151.439, 92.269 151.693 M 117.250 151.747 C 123.712 151.921, 134.287 151.921, 140.750 151.747 C 147.213 151.573, 141.925 151.430, 129 151.430 C 116.075 151.430, 110.787 151.573, 117.250 151.747 M 104.607 160.582 C 103.021 164.716, 104.774 178.620, 107.574 184.108 C 108.789 186.490, 109.320 186.676, 113.713 186.254 C 117.497 185.890, 119.047 186.218, 120.990 187.791 C 123.131 189.525, 123.327 190.169, 122.459 192.636 C 121.909 194.200, 120.793 195.528, 119.979 195.587 C 119.104 195.649, 119.010 195.521, 119.750 195.273 C 120.438 195.042, 121 194.421, 121 193.891 C 121 193.299, 120.328 193.275, 119.250 193.830 C 118.287 194.325, 116.858 194.930, 116.073 195.174 C 114.585 195.637, 120.699 200, 122.835 200 C 123.552 200, 124.033 196.089, 124.217 188.750 L 124.500 177.500 129 177.500 L 133.500 177.500 134 185 C 134.340 190.100, 134.924 192.588, 135.824 192.775 C 136.553 192.926, 136.869 192.598, 136.528 192.045 C 136.187 191.493, 136.394 190.132, 136.989 189.021 C 137.795 187.515, 139.080 187, 142.035 187 C 144.216 187, 146 187.477, 146 188.059 C 146 188.641, 146.450 188.840, 147 188.500 C 147.550 188.160, 148.068 185.771, 148.150 183.191 L 148.300 178.500 149 183 L 149.700 187.500 149.850 183.250 C 149.996 179.103, 150.083 179, 153.441 179 C 155.334 179, 157.096 178.654, 157.357 178.232 C 157.618 177.809, 155.957 177.561, 153.666 177.680 C 151.375 177.800, 150.236 177.673, 151.135 177.398 C 153.608 176.641, 154.932 167.078, 153.370 161.250 L 152.767 159 128.990 159 C 108.232 159, 105.137 159.201, 104.607 160.582 M 68.031 161.500 C 68.031 162.050, 68.467 163.175, 69 164 C 69.533 164.825, 69.969 165.050, 69.969 164.500 C 69.969 163.950, 69.533 162.825, 69 162 C 68.467 161.175, 68.031 160.950, 68.031 161.500 M 171.488 163.527 C 171.769 164.887, 172.243 167.162, 172.539 168.581 L 173.079 171.163 175.589 167.464 C 176.969 165.430, 177.852 163.518, 177.549 163.216 C 177.247 162.914, 177 163.278, 177 164.024 C 177 165.065, 176.708 165.020, 175.750 163.831 C 175.063 162.978, 173.707 162.004, 172.738 161.666 C 171.291 161.163, 171.067 161.497, 171.488 163.527 M 169 171.333 C 169 174.450, 169.434 177, 169.965 177 C 170.862 177, 170.351 167.059, 169.404 166.083 C 169.182 165.854, 169 168.217, 169 171.333 M 39.813 177.683 C 40.534 177.972, 41.397 177.936, 41.729 177.604 C 42.061 177.272, 41.471 177.036, 40.417 177.079 C 39.252 177.127, 39.015 177.364, 39.813 177.683 M 163.553 177.915 C 163.231 178.436, 163.394 179.125, 163.915 179.447 C 164.436 179.769, 165.125 179.606, 165.447 179.085 C 165.769 178.564, 165.606 177.875, 165.085 177.553 C 164.564 177.231, 163.875 177.394, 163.553 177.915 M 46 178.668 C 46.825 179.015, 47.872 179.795, 48.326 180.400 C 48.857 181.107, 49.006 180.965, 48.743 180 C 48.518 179.175, 47.472 178.395, 46.417 178.268 C 45.070 178.104, 44.946 178.223, 46 178.668 M 52 196 L 52 214 56 214 L 60 214 59.796 205.250 L 59.593 196.500 59.256 204.750 L 58.919 213 55.960 213 L 53 213 53 196 L 53 179 55.952 179 C 58.869 179, 58.909 179.075, 59.260 185.250 L 59.615 191.500 59.807 184.750 L 60 178 56 178 L 52 178 52 196 M 125 196.060 L 125 214.121 128.750 213.810 L 132.500 213.500 132.595 208 L 132.691 202.500 132.293 207.750 C 131.910 212.806, 131.787 213, 128.948 213 L 126 213 126 196 L 126 179 128.962 179 L 131.925 179 132.255 188.250 L 132.585 197.500 132.792 187.750 L 133 178 129 178 L 125 178 125 196.060 M 39.762 184.707 C 41.006 184.946, 42.806 184.937, 43.762 184.687 C 44.718 184.437, 43.700 184.241, 41.500 184.252 C 39.300 184.263, 38.518 184.468, 39.762 184.707 M 67.500 185.062 L 65.500 185.983 67.500 185.712 C 68.600 185.563, 70.620 186.466, 71.990 187.720 C 74.836 190.326, 76.288 190.562, 77.855 188.675 C 78.711 187.643, 78.176 186.977, 75.444 185.675 C 71.533 183.810, 70.407 183.723, 67.500 185.062 M 36.001 187.499 C 34.931 188.788, 35.160 188.920, 37.628 188.436 C 39.208 188.127, 41.274 187.765, 42.221 187.632 C 45.809 187.130, 45.164 186, 41.289 186 C 38.915 186, 36.731 186.619, 36.001 187.499 M 47.655 188.829 C 46.745 189.835, 46.014 191.072, 46.030 191.579 C 46.047 192.085, 46.571 191.620, 47.195 190.544 C 48.321 188.603, 48.335 188.603, 49.099 190.544 C 49.727 192.140, 49.841 191.994, 49.716 189.750 C 49.632 188.238, 49.507 187, 49.437 187 C 49.367 187, 48.565 187.823, 47.655 188.829 M 65.750 187.662 C 66.438 187.940, 67.563 187.940, 68.250 187.662 C 68.938 187.385, 68.375 187.158, 67 187.158 C 65.625 187.158, 65.063 187.385, 65.750 187.662 M 88.750 187.662 C 89.438 187.940, 90.563 187.940, 91.250 187.662 C 91.938 187.385, 91.375 187.158, 90 187.158 C 88.625 187.158, 88.063 187.385, 88.750 187.662 M 113.269 187.693 C 114.242 187.947, 115.592 187.930, 116.269 187.656 C 116.946 187.382, 116.150 187.175, 114.500 187.195 C 112.850 187.215, 112.296 187.439, 113.269 187.693 M 168.250 187.732 L 164 188.115 164 200.990 C 164 213.449, 163.927 213.890, 161.750 214.631 C 160.512 215.052, 159.129 215.296, 158.676 215.173 C 158.222 215.051, 158.136 215.412, 158.485 215.975 C 159.278 217.259, 159.025 217.253, 155.150 215.903 C 153.419 215.299, 152.222 214.450, 152.490 214.016 C 152.758 213.582, 152.195 212.613, 151.239 211.863 C 149.665 210.630, 149.630 210.666, 150.872 212.250 C 151.771 213.395, 151.857 214, 151.122 214 C 150.505 214, 149.786 214.070, 149.524 214.155 C 145.756 215.380, 139.812 215.478, 138.859 214.330 C 137.949 213.234, 137.437 213.216, 136.175 214.238 C 134.900 215.270, 134.764 215.254, 135.429 214.150 C 136.087 213.057, 135.839 213.016, 134.121 213.935 C 132.954 214.560, 132 215.306, 132 215.594 C 132 215.882, 132.521 215.796, 133.158 215.403 C 133.958 214.908, 134.133 215.385, 133.723 216.953 C 133.148 219.148, 133.296 219.200, 138.505 218.632 C 141.785 218.274, 145.364 218.545, 147.690 219.325 C 152.093 220.804, 154.716 220.757, 157.726 219.147 C 158.904 218.516, 161.505 218, 163.506 218 C 165.536 218, 167.864 217.279, 168.775 216.368 C 170.284 214.859, 170.247 214.787, 168.288 215.409 C 167.122 215.779, 165.879 215.613, 165.525 215.041 C 165.126 214.395, 166.214 214, 168.387 214 L 171.892 214 172.196 200.498 C 172.363 193.072, 172.500 187.076, 172.500 187.173 C 172.500 187.270, 170.588 187.522, 168.250 187.732 M 184.269 187.693 C 185.242 187.947, 186.592 187.930, 187.269 187.656 C 187.946 187.382, 187.150 187.175, 185.500 187.195 C 183.850 187.215, 183.296 187.439, 184.269 187.693 M 222.269 187.693 C 223.242 187.947, 224.592 187.930, 225.269 187.656 C 225.946 187.382, 225.150 187.175, 223.500 187.195 C 221.850 187.215, 221.296 187.439, 222.269 187.693 M 94 188.393 C 94 188.609, 94.698 189.054, 95.552 189.382 C 96.442 189.723, 96.843 189.555, 96.493 188.989 C 95.906 188.038, 94 187.583, 94 188.393 M 106.325 188.658 C 106.687 189.020, 106.424 190.032, 105.741 190.908 C 104.645 192.314, 104.703 192.341, 106.239 191.137 C 107.803 189.910, 107.825 188, 106.274 188 C 105.940 188, 105.963 188.296, 106.325 188.658 M 119.600 188.674 C 120.205 189.128, 120.985 190.175, 121.332 191 C 121.777 192.054, 121.896 191.930, 121.732 190.583 C 121.605 189.528, 120.825 188.482, 120 188.257 C 119.035 187.994, 118.893 188.143, 119.600 188.674 M 138.629 188.791 C 138.360 189.226, 139.684 189.402, 141.570 189.182 C 143.457 188.961, 145 188.605, 145 188.391 C 145 187.638, 139.113 188.008, 138.629 188.791 M 195.187 191.173 C 195.359 192.918, 196.074 194.459, 196.775 194.598 C 197.476 194.737, 197.589 194.621, 197.025 194.342 C 195.298 193.485, 195.849 189.186, 197.750 188.689 C 199.032 188.354, 198.881 188.201, 197.187 188.116 C 195.170 188.015, 194.913 188.407, 195.187 191.173 M 207.250 188.689 C 208.213 188.941, 209.787 188.941, 210.750 188.689 C 211.713 188.438, 210.925 188.232, 209 188.232 C 207.075 188.232, 206.287 188.438, 207.250 188.689 M 165 201 L 165 213 168 213 L 171 213 171 201 L 171 189 168 189 L 165 189 165 201 M 24.079 191.583 C 24.127 192.748, 24.364 192.985, 24.683 192.188 C 24.972 191.466, 24.936 190.603, 24.604 190.271 C 24.272 189.939, 24.036 190.529, 24.079 191.583 M 144.603 190.833 C 144.320 191.292, 144.723 191.667, 145.500 191.667 C 146.277 191.667, 146.680 191.292, 146.397 190.833 C 146.114 190.375, 145.710 190, 145.500 190 C 145.290 190, 144.886 190.375, 144.603 190.833 M 176.232 195 C 176.232 196.925, 176.438 197.713, 176.689 196.750 C 176.941 195.787, 176.941 194.213, 176.689 193.250 C 176.438 192.287, 176.232 193.075, 176.232 195 M 88.750 193.662 C 89.438 193.940, 90.563 193.940, 91.250 193.662 C 91.938 193.385, 91.375 193.158, 90 193.158 C 88.625 193.158, 88.063 193.385, 88.750 193.662 M 183.629 193.791 C 183.360 194.226, 184.684 194.402, 186.570 194.182 C 188.457 193.961, 190 193.605, 190 193.391 C 190 192.638, 184.113 193.008, 183.629 193.791 M 221.612 193.819 C 221.333 194.270, 222.432 194.443, 224.053 194.206 C 225.674 193.968, 227 193.599, 227 193.387 C 227 192.625, 222.107 193.017, 221.612 193.819 M 74.150 203.250 L 74 213 71.039 213 L 68.078 213 67.782 204.250 L 67.486 195.500 66.993 205.005 L 66.500 214.510 70.369 214.139 C 72.497 213.934, 74.522 214.227, 74.869 214.789 C 75.216 215.351, 75.371 211.916, 75.214 207.156 C 74.782 194.059, 74.317 192.353, 74.150 203.250 M 173.604 195.750 C 171.902 201.367, 173.935 205.825, 178 205.391 C 178.825 205.303, 180.149 205.179, 180.941 205.115 C 182.007 205.030, 181.877 204.618, 180.441 203.532 C 179.374 202.724, 178.893 202.049, 179.372 202.032 C 179.852 202.014, 179.711 201.356, 179.058 200.570 C 178.405 199.783, 177.045 199.299, 176.035 199.493 C 174.514 199.786, 174.278 199.344, 174.659 196.923 C 175.162 193.732, 174.454 192.945, 173.604 195.750 M 191.834 194.876 C 192.890 195.545, 192.805 195.861, 191.428 196.389 C 189.951 196.956, 190.195 197.430, 193.038 199.528 C 197.322 202.689, 198.347 202.726, 194.699 199.589 C 193.159 198.264, 192.344 196.905, 192.888 196.569 C 193.432 196.233, 194.341 196.755, 194.908 197.729 C 195.475 198.703, 195.953 199.007, 195.970 198.405 C 196.005 197.124, 192.761 193.993, 191.421 194.015 C 190.915 194.024, 191.100 194.411, 191.834 194.876 M 208.250 194.689 C 209.213 194.941, 210.787 194.941, 211.750 194.689 C 212.713 194.438, 211.925 194.232, 210 194.232 C 208.075 194.232, 207.287 194.438, 208.250 194.689 M 87.504 195.993 C 87.148 196.569, 87.964 196.898, 89.445 196.775 C 90.850 196.659, 92 196.212, 92 195.782 C 92 194.645, 88.228 194.822, 87.504 195.993 M 100.232 198 C 100.232 199.925, 100.438 200.713, 100.689 199.750 C 100.941 198.787, 100.941 197.213, 100.689 196.250 C 100.438 195.287, 100.232 196.075, 100.232 198 M 187.510 196.016 C 187.856 196.575, 188.557 196.774, 189.069 196.457 C 190.456 195.600, 190.198 195, 188.441 195 C 187.584 195, 187.165 195.457, 187.510 196.016 M 78.079 197.583 C 78.127 198.748, 78.364 198.985, 78.683 198.188 C 78.972 197.466, 78.936 196.603, 78.604 196.271 C 78.272 195.939, 78.036 196.529, 78.079 197.583 M 24.158 201 C 24.158 202.375, 24.385 202.938, 24.662 202.250 C 24.940 201.563, 24.940 200.438, 24.662 199.750 C 24.385 199.063, 24.158 199.625, 24.158 201 M 215 199.378 C 215 199.585, 215.787 200.373, 216.750 201.128 C 218.336 202.371, 218.371 202.336, 217.128 200.750 C 215.821 199.084, 215 198.555, 215 199.378 M 177 201 C 177 201.550, 177.477 202, 178.059 202 C 178.641 202, 178.840 201.550, 178.500 201 C 178.160 200.450, 177.684 200, 177.441 200 C 177.198 200, 177 200.450, 177 201 M 78.158 204 C 78.158 205.375, 78.385 205.938, 78.662 205.250 C 78.940 204.563, 78.940 203.438, 78.662 202.750 C 78.385 202.063, 78.158 202.625, 78.158 204 M 93.750 202.706 C 94.987 202.944, 97.013 202.944, 98.250 202.706 C 99.487 202.467, 98.475 202.272, 96 202.272 C 93.525 202.272, 92.513 202.467, 93.750 202.706 M 186.731 206.080 C 187.085 207.432, 187.538 207.886, 187.788 207.137 C 188.028 206.415, 187.750 205.350, 187.168 204.768 C 186.426 204.026, 186.296 204.417, 186.731 206.080 M 194.158 206 C 194.158 207.375, 194.385 207.938, 194.662 207.250 C 194.940 206.563, 194.940 205.438, 194.662 204.750 C 194.385 204.063, 194.158 204.625, 194.158 206 M 217.477 205.037 C 216.935 205.915, 219.388 207, 221.915 207 C 222.878 207, 223.666 206.438, 223.665 205.750 C 223.663 204.408, 218.251 203.784, 217.477 205.037 M 232.158 206 C 232.158 207.375, 232.385 207.938, 232.662 207.250 C 232.940 206.563, 232.940 205.438, 232.662 204.750 C 232.385 204.063, 232.158 204.625, 232.158 206 M 212.039 206.479 C 209.608 207.780, 209.609 207.782, 212.802 207.652 C 214.561 207.580, 216 207.205, 216 206.820 C 216 205.360, 214.389 205.221, 212.039 206.479 M 225.079 206.583 C 225.127 207.748, 225.364 207.985, 225.683 207.188 C 225.972 206.466, 225.936 205.603, 225.604 205.271 C 225.272 204.939, 225.036 205.529, 225.079 206.583 M 39.250 207.689 C 40.212 207.941, 41.788 207.941, 42.750 207.689 C 43.712 207.438, 42.925 207.232, 41 207.232 C 39.075 207.232, 38.288 207.438, 39.250 207.689 M 78.144 209.750 C 77.127 212.128, 77.152 212.266, 78.333 210.766 C 79.083 209.812, 80.015 209.348, 80.402 209.735 C 80.789 210.122, 80.809 209.665, 80.446 208.720 C 79.589 206.486, 79.530 206.512, 78.144 209.750 M 98.439 208.099 C 97.941 208.905, 98.112 209.049, 98.953 208.529 C 99.884 207.954, 100.058 208.372, 99.616 210.125 C 99.288 211.431, 99.277 211.938, 99.593 211.250 C 99.908 210.563, 100.842 210, 101.667 210 C 103.604 210, 102.827 207.596, 100.760 207.195 C 99.895 207.027, 98.850 207.434, 98.439 208.099 M 160.731 209.080 C 161.085 210.432, 161.538 210.886, 161.788 210.137 C 162.028 209.415, 161.750 208.350, 161.168 207.768 C 160.426 207.026, 160.296 207.417, 160.731 209.080 M 28.500 210 C 29.495 211.100, 30.535 212, 30.810 212 C 31.085 212, 30.495 211.100, 29.500 210 C 28.505 208.900, 27.465 208, 27.190 208 C 26.915 208, 27.505 208.900, 28.500 210 M 90.269 208.693 C 91.242 208.947, 92.592 208.930, 93.269 208.656 C 93.946 208.382, 93.150 208.175, 91.500 208.195 C 89.850 208.215, 89.296 208.439, 90.269 208.693 M 182.750 208.662 C 183.438 208.940, 184.563 208.940, 185.250 208.662 C 185.938 208.385, 185.375 208.158, 184 208.158 C 182.625 208.158, 182.063 208.385, 182.750 208.662 M 220.750 208.662 C 221.438 208.940, 222.563 208.940, 223.250 208.662 C 223.938 208.385, 223.375 208.158, 222 208.158 C 220.625 208.158, 220.063 208.385, 220.750 208.662 M 101.478 211.640 C 100.422 212.412, 98.959 212.814, 98.227 212.534 C 97.495 212.253, 97.163 212.455, 97.489 212.982 C 97.815 213.510, 96.826 214.242, 95.291 214.609 C 90.851 215.671, 90.653 216.111, 93.762 218.001 C 97.489 220.268, 102.457 219.858, 105.461 217.037 C 109.837 212.926, 106.291 208.120, 101.478 211.640 M 191.543 211.931 C 191.226 212.443, 191.350 213.098, 191.817 213.387 C 192.284 213.676, 192.667 213.257, 192.667 212.456 C 192.667 210.770, 192.351 210.623, 191.543 211.931 M 79.569 213.682 C 78.606 216.191, 80.086 216.894, 84.500 216.021 C 86.700 215.586, 87.588 215.178, 86.473 215.115 C 85.357 215.052, 83.491 214.331, 82.324 213.514 C 80.425 212.184, 80.137 212.202, 79.569 213.682 M 160 213 C 159.099 213.582, 158.975 213.975, 159.691 213.985 C 160.346 213.993, 161.160 213.550, 161.500 213 C 162.267 211.758, 161.921 211.758, 160 213 M 44.813 213.683 C 45.534 213.972, 46.397 213.936, 46.729 213.604 C 47.061 213.272, 46.471 213.036, 45.417 213.079 C 44.252 213.127, 44.015 213.364, 44.813 213.683 M 141.264 213.718 C 142.784 213.947, 145.034 213.941, 146.264 213.704 C 147.494 213.467, 146.250 213.279, 143.500 213.286 C 140.750 213.294, 139.744 213.488, 141.264 213.718 M 174.500 214 C 174.840 214.550, 175.568 215, 176.118 215 C 176.668 215, 176.840 214.550, 176.500 214 C 176.160 213.450, 175.432 213, 174.882 213 C 174.332 213, 174.160 213.450, 174.500 214 M 101.583 215.866 C 101.152 216.563, 101.070 217.403, 101.400 217.733 C 102.295 218.628, 103.271 217.312, 102.787 215.862 C 102.449 214.846, 102.213 214.846, 101.583 215.866 M 171.325 215.658 C 171.687 216.020, 171.511 216.884, 170.934 217.579 C 170.131 218.547, 170.372 218.715, 171.964 218.299 C 173.599 217.871, 173.799 217.462, 172.899 216.378 C 172.270 215.620, 171.510 215, 171.211 215 C 170.912 215, 170.963 215.296, 171.325 215.658\",stroke:\"none\",fill:\"#635597\",fillRule:\"evenodd\"}),wp.element.createElement(\"path\",{d:\"M 79.667 28.667 C 79.300 29.033, 79 57.778, 79 92.544 C 79 143.508, 79.254 155.966, 80.313 156.845 C 81.929 158.186, 175.764 158.383, 177.920 157.050 C 179.111 156.314, 179.370 148.582, 179.520 109.336 L 179.700 62.500 162.611 45.250 L 145.521 28 112.927 28 C 95.001 28, 80.033 28.300, 79.667 28.667 M 84.682 33.651 C 84.307 34.026, 84 60.808, 84 93.167 L 84 152 129 152 L 174 152 174 109.025 L 174 66.051 158.250 65.775 L 142.500 65.500 142 49.500 L 141.500 33.500 113.432 33.235 C 97.994 33.089, 85.057 33.276, 84.682 33.651 M 147 48.767 L 147 60 158.667 60 C 165.083 60, 170.131 59.887, 169.884 59.750 C 169.637 59.612, 164.387 54.558, 158.217 48.517 L 147 37.534 147 48.767 M 137.672 85.750 C 131.952 92.213, 126.198 98.729, 124.886 100.232 L 122.500 102.963 116.182 97.482 C 112.707 94.467, 108.960 92, 107.855 92 C 104.742 92, 100 97.335, 100 100.836 C 100 103.346, 101.726 105.443, 110.750 113.896 C 116.662 119.435, 122.204 123.974, 123.065 123.983 C 123.926 123.993, 132.363 115.203, 141.815 104.451 C 155.888 88.442, 159 84.359, 159 81.905 C 159 77.934, 155.059 74, 151.082 74 C 148.497 74, 146.606 75.658, 137.672 85.750 M 137.094 94.534 C 129.721 103.040, 123.389 110, 123.022 110 C 122.656 110, 119.238 107.302, 115.428 104.003 C 111.618 100.705, 108.218 98.005, 107.872 98.003 C 107.527 98.002, 106.729 98.622, 106.098 99.382 C 105.182 100.485, 106.363 102.128, 111.944 107.516 C 115.791 111.230, 119.896 114.939, 121.066 115.759 C 123.585 117.523, 121.459 119.500, 140.455 97.732 C 151.935 84.577, 154.011 81.718, 152.985 80.482 C 152.309 79.667, 151.473 79.015, 151.128 79.034 C 150.782 79.053, 144.467 86.028, 137.094 94.534 M 33.020 179.695 C 21.353 185.747, 21.353 206.239, 33.020 212.311 C 35.610 213.659, 37.879 213.994, 41.892 213.619 C 48.115 213.038, 49.159 212.325, 48.346 209.214 C 47.806 207.150, 47.356 207.017, 42.924 207.604 C 38.468 208.196, 37.855 208.008, 35.046 205.200 C 32.118 202.272, 31.436 199.860, 31.877 194 C 32.413 186.887, 36.815 183.221, 43.513 184.308 C 46.600 184.809, 47.064 184.593, 47.617 182.390 C 48.443 179.100, 47.919 178.732, 41.749 178.277 C 38.030 178.002, 35.485 178.416, 33.020 179.695 M 165.200 179.200 C 162.430 181.970, 165.342 185.633, 169.487 184.593 C 173.480 183.590, 172.219 178, 168 178 C 167.120 178, 165.860 178.540, 165.200 179.200 M 53 196 L 53 213 56 213 L 59 213 59 205 C 59 198.333, 59.333 196.667, 61 195 C 63.411 192.589, 64.370 192.513, 66.429 194.571 C 67.609 195.751, 68 198.242, 68 204.571 L 68 213 71 213 L 74 213 74 202.500 C 74 193.333, 73.746 191.746, 72 190 C 69.586 187.586, 64.344 187.370, 61.223 189.557 L 59 191.113 59 185.057 L 59 179 56 179 L 53 179 53 196 M 126 196 L 126 213 129 213 C 131.933 213, 132 212.889, 132 208 C 132 205.250, 132.424 203, 132.942 203 C 133.461 203, 135.148 205.250, 136.692 207.999 C 139.290 212.625, 139.783 212.998, 143.292 212.999 L 147.084 213 144.376 208.250 C 142.886 205.637, 140.764 202.488, 139.660 201.250 C 138.555 200.012, 138.141 199, 138.740 199 C 139.593 199, 146 189.898, 146 188.686 C 146 188.529, 144.372 188.592, 142.383 188.824 C 139.625 189.146, 138.378 189.955, 137.133 192.233 C 136.235 193.875, 134.727 196.182, 133.783 197.360 C 132.133 199.417, 132.065 199.103, 132.033 189.250 L 132 179 129 179 L 126 179 126 196 M 150 194.845 C 150 208.925, 150.204 210.874, 151.829 212.345 C 155.284 215.472, 162.271 213.751, 161.293 210.013 C 161.001 208.895, 159.930 208, 158.883 208 C 157.125 208, 157 207.034, 157 193.500 L 157 179 153.500 179 L 150 179 150 194.845 M 201.750 183.162 C 199.786 183.880, 199 184.857, 199 186.583 C 199 188.065, 198.420 189, 197.500 189 C 196.567 189, 196 189.944, 196 191.500 C 196 193.056, 196.567 194, 197.500 194 C 198.689 194, 199 195.437, 199 200.935 C 199 212.202, 201.093 214.956, 208.587 213.550 C 211.370 213.028, 212 212.456, 212 210.455 C 212 208.394, 211.519 208, 209 208 L 206 208 206 201 L 206 194 209 194 C 211.533 194, 212 193.611, 212 191.500 C 212 189.389, 211.533 189, 209 189 C 206.238 189, 206 188.722, 206 185.500 C 206 181.723, 205.876 181.655, 201.750 183.162 M 83.277 189.973 C 79.827 192.076, 78.557 195.183, 78.672 201.232 C 78.867 211.510, 85.982 216.343, 95.851 212.902 C 99.016 211.799, 99.640 210.556, 98.183 208.262 C 97.561 207.282, 96.721 207.279, 94.160 208.246 C 91.483 209.256, 90.455 209.205, 88.212 207.950 C 85.187 206.258, 83.611 201.253, 86.445 202.340 C 87.336 202.683, 90.751 202.682, 94.033 202.339 C 99.995 201.716, 100 201.712, 100 198.405 C 100 196.584, 99.500 193.998, 98.890 192.657 C 96.707 187.866, 88.926 186.528, 83.277 189.973 M 106.997 190.850 C 102.232 195.614, 101.790 204.921, 106.070 210.361 C 110.229 215.650, 122.799 214.771, 121.366 209.293 C 120.893 207.484, 120.284 207.242, 117.381 207.713 C 112.762 208.463, 110 205.964, 110 201.036 C 110 195.906, 111.833 193.839, 116.545 193.654 C 119.526 193.537, 120.563 193.046, 120.836 191.620 C 121.321 189.091, 119.366 188.034, 114.173 188.015 C 110.738 188.003, 109.259 188.587, 106.997 190.850 M 179.223 189.557 C 177.591 190.700, 177 192.121, 177 194.902 C 177 199.183, 178.644 201.118, 184.287 203.475 C 186.329 204.329, 188 205.687, 188 206.494 C 188 208.477, 184.037 209.345, 180.729 208.087 C 178.475 207.230, 177.871 207.373, 177.018 208.966 C 175.549 211.712, 175.613 211.804, 179.904 213.089 C 188.184 215.570, 194.373 212.127, 193.816 205.348 C 193.529 201.850, 193.045 201.266, 188.500 198.934 C 181.993 195.593, 181.854 193.446, 188.158 193.654 C 192.016 193.782, 192.905 193.471, 193.329 191.849 C 193.701 190.427, 193.160 189.631, 191.356 188.945 C 187.594 187.515, 181.714 187.811, 179.223 189.557 M 217.223 189.557 C 215.577 190.709, 215 192.118, 215 194.984 C 215 199.172, 217.114 201.562, 222.959 203.983 C 224.632 204.676, 226 205.854, 226 206.602 C 226 208.491, 221.962 209.316, 218.729 208.087 C 216.475 207.230, 215.871 207.373, 215.018 208.966 C 213.575 211.662, 213.609 211.716, 217.608 213.036 C 224.969 215.465, 232.013 212.309, 231.985 206.595 C 231.964 202.375, 230.831 200.894, 225.802 198.511 C 219.882 195.706, 220.042 193.427, 226.138 193.731 C 230.068 193.927, 230.858 193.652, 231.309 191.926 C 231.704 190.415, 231.201 189.647, 229.356 188.945 C 225.594 187.515, 219.714 187.811, 217.223 189.557 M 165 201 L 165 213 168 213 L 171 213 171 201 L 171 189 168 189 L 165 189 165 201 M 86.494 195.006 C 84.199 197.542, 84.927 198.250, 89.973 198.389 C 92.993 198.472, 94.001 198.112, 94.001 196.950 C 93.999 192.988, 89.399 191.797, 86.494 195.006\",stroke:\"none\",fill:\"#f9b707\",fillRule:\"evenodd\"}),wp.element.createElement(\"path\",{d:\"M 78.655 27.829 C 77.323 29.301, 77 31.837, 77 40.829 C 77 50.929, 76.827 52, 75.196 52 C 74.203 52, 71.254 53.356, 68.641 55.013 C 63.756 58.111, 60.067 64.017, 60.022 68.816 C 59.993 71.829, 58.714 74.132, 55.013 77.833 C 52.697 80.149, 52 81.745, 52 84.727 C 52 89.414, 56.042 96.241, 61.463 100.709 C 65.159 103.755, 65.178 103.808, 62.855 104.525 C 61.560 104.925, 57.666 107.220, 54.202 109.626 C 45.125 115.930, 43.269 115.388, 46.603 107.408 C 48.258 103.447, 48.033 96.633, 46.185 94.785 C 45.555 94.155, 43.598 94.527, 40.625 95.842 C 38.012 96.997, 35.789 97.468, 35.460 96.935 C 34.455 95.310, 31.801 95.914, 28.335 98.558 C 24.455 101.517, 21.550 106.795, 20.667 112.485 C 20.076 116.297, 20.279 116.687, 25.349 121.485 C 28.263 124.243, 33.273 129.788, 36.482 133.807 C 40.333 138.629, 43.210 141.292, 44.945 141.639 C 47.330 142.116, 47.593 142.634, 47.794 147.253 C 48.231 157.298, 51.932 166.258, 58.917 174.178 C 61.186 176.751, 62.630 179.110, 62.126 179.422 C 61.622 179.734, 60.942 179.290, 60.614 178.436 C 60.176 177.296, 58.889 176.966, 55.759 177.192 C 52.073 177.459, 51.455 177.837, 51.165 180 C 50.981 181.375, 50.368 182.950, 49.803 183.500 C 49.103 184.181, 48.997 183.767, 49.470 182.202 C 50.441 178.991, 48.594 177.212, 43.493 176.447 C 35.745 175.285, 27.217 179.843, 24.364 186.671 C 22.615 190.858, 22.607 201.125, 24.351 205.298 C 27.681 213.267, 37.197 217.550, 45.500 214.816 C 49.827 213.391, 51.026 210.786, 49.053 207.100 C 48.126 205.367, 47.306 205.176, 42.975 205.689 C 37.541 206.333, 34.719 204.856, 33.662 200.815 C 33.159 198.894, 33.432 198.801, 37.812 199.406 C 40.390 199.762, 43.400 200.232, 44.500 200.450 C 45.600 200.668, 47.492 200.881, 48.704 200.923 C 50.721 200.993, 50.934 201.575, 51.204 207.750 L 51.500 214.500 56 214.500 L 60.500 214.500 60.798 208.250 C 61.095 202.008, 61.443 201.465, 64.418 202.607 C 66.594 203.442, 66.489 209.802, 64.259 212.267 C 62.594 214.106, 62.621 214.274, 64.858 216.086 C 71.800 221.707, 93.549 224.531, 129.250 224.447 C 156.544 224.382, 162.885 224.146, 171 222.897 C 174.575 222.347, 179.075 221.667, 181 221.388 C 192.401 219.732, 199.668 215.715, 197.025 212.530 C 196.336 211.701, 195.380 211.265, 194.898 211.563 C 194.417 211.860, 194.291 211.406, 194.618 210.552 C 195.514 208.219, 197.535 208.683, 198.827 211.520 C 199.459 212.906, 201.074 214.458, 202.417 214.969 C 204.969 215.939, 211.710 215.278, 212.513 213.978 C 212.769 213.565, 214.881 213.882, 217.207 214.684 C 222.682 216.570, 227.512 215.641, 231.150 212.003 C 236.269 206.885, 234.357 200.494, 226.628 196.889 L 222.500 194.964 226.769 195.497 C 230.661 195.983, 231.125 195.802, 232.019 193.450 C 233.514 189.519, 233.290 189.019, 229.286 187.346 C 223.320 184.853, 214 187.290, 214 191.344 C 214 191.912, 214.880 191.432, 215.956 190.278 C 218.580 187.461, 222.943 186.638, 228.016 188.004 C 231.627 188.977, 232.119 189.429, 231.613 191.315 C 231.291 192.517, 231.021 193.782, 231.014 194.127 C 231.006 194.472, 229.034 194.533, 226.630 194.262 C 220.580 193.580, 219.939 195.437, 225.392 197.849 C 233.014 201.221, 234.911 207.089, 229.975 212.025 C 228.557 213.443, 226.659 213.996, 223.250 213.986 C 215.502 213.964, 213.716 213.170, 214.345 210.025 C 214.739 208.055, 214.479 207.333, 213.375 207.333 C 212.268 207.333, 212.033 207.994, 212.491 209.822 C 213.274 212.942, 211.779 213.965, 206.405 213.985 C 200.247 214.008, 199 212.037, 199 202.282 C 199 195.220, 198.783 194.239, 197.250 194.361 C 195.970 194.463, 195.416 193.627, 195.187 191.250 C 194.925 188.532, 195.211 188, 196.937 188 C 198.444 188, 199 187.358, 199 185.617 C 199 183.990, 199.715 183.042, 201.250 182.630 C 205.275 181.552, 206 181.913, 206 185 C 206 187.641, 206.345 188, 208.891 188 C 211.463 188, 211.827 188.395, 212.200 191.587 C 212.613 195.126, 212.574 195.169, 209.309 194.788 L 206 194.402 206 200.732 C 206 207.885, 207.066 209.015, 211.660 206.735 C 214.035 205.556, 214.909 205.541, 217 206.648 C 220.294 208.391, 225 208.393, 225 206.652 C 225 205.910, 223.197 204.491, 220.994 203.497 C 215.727 201.122, 214.986 200.092, 213.638 193.262 C 212.609 188.052, 212.237 187.470, 209.750 187.184 C 207.854 186.965, 207 186.292, 207 185.015 C 207 180.915, 206.056 180.087, 202.673 181.224 C 198.518 182.620, 196.798 184.031, 197.385 185.562 C 198.075 187.359, 194.528 187.984, 190.285 186.813 C 188.268 186.256, 185.092 186.048, 183.227 186.351 L 179.837 186.901 183.834 182.565 C 186.032 180.180, 188.582 176.776, 189.500 175 L 191.169 171.772 194.464 174.965 L 197.758 178.158 199.409 173.329 C 201.946 165.903, 202.557 155.247, 200.699 150.813 C 198.579 145.753, 199.398 143.868, 204.836 141.283 C 210.845 138.426, 221.784 128.500, 229.181 119.191 C 234.185 112.895, 234.999 111.283, 234.994 107.684 C 234.988 102.735, 233.275 96, 232.023 96 C 231.538 96, 230.177 94.071, 229 91.714 C 225.799 85.305, 220.402 79.260, 219.054 80.574 C 218.448 81.164, 217.812 85.269, 217.639 89.696 C 217.374 96.517, 216.882 98.467, 214.413 102.476 C 210.830 108.295, 204.881 112.626, 199.258 113.508 C 195.361 114.119, 194.938 113.968, 194.055 111.645 C 193.527 110.255, 192.960 107.883, 192.797 106.375 C 192.532 103.930, 192.911 103.544, 196.282 102.825 C 202.658 101.464, 206 99.258, 206 96.410 C 206 94.840, 207.099 92.946, 208.881 91.447 C 214.517 86.704, 216.554 78.503, 213.533 72.712 C 212.480 70.693, 212.344 69.225, 213.039 67.396 C 217.062 56.816, 206.777 43.237, 192.250 39.947 C 188.435 39.083, 188.284 39.673, 191.099 44.443 C 193.478 48.476, 193.087 48.819, 189.210 46.100 C 185.483 43.487, 180.553 42.392, 181.302 44.344 C 181.745 45.499, 181.091 45.688, 178.058 45.281 C 174.032 44.741, 171.887 46.287, 173.887 48.287 C 175.481 49.881, 175.268 50.697, 173.113 51.260 C 171.710 51.627, 167.928 48.449, 158.377 38.877 L 145.529 26 112.919 26 C 82.598 26, 80.194 26.128, 78.655 27.829 M 79.571 28.571 C 78.176 29.967, 78 37.179, 78 92.871 C 78 140.327, 78.292 155.892, 79.200 156.800 C 80.929 158.529, 177.071 158.529, 178.800 156.800 C 179.699 155.901, 180 143.803, 180 108.513 L 180 61.425 162.685 44.213 L 145.370 27 113.257 27 C 85.396 27, 80.935 27.208, 79.571 28.571 M 85 92.750 L 85.001 151.500 129.422 151.538 L 173.844 151.575 173.672 108.753 L 173.500 65.930 156.670 66.052 C 147.413 66.118, 139.613 65.946, 139.335 65.668 C 139.057 65.390, 137.505 65.625, 135.885 66.190 C 130.975 67.902, 128.468 75.269, 131.113 80.212 C 132.123 82.098, 132.006 82.233, 129.863 81.660 C 128.564 81.312, 126.247 81.021, 124.715 81.014 C 122.272 81.002, 122.057 80.762, 122.965 79.066 C 124.688 75.846, 124.140 72.319, 121.411 69.077 C 118.100 65.142, 114.753 65.093, 110.923 68.923 C 107.197 72.650, 107.082 76.756, 110.589 80.923 L 113.178 84 110.558 84 C 106.962 84, 105.989 86.884, 108.897 88.925 C 113.813 92.375, 117.969 96.241, 117.528 96.955 C 117.271 97.370, 118.213 98.787, 119.620 100.105 L 122.179 102.500 129.059 95 C 132.843 90.875, 135.744 86.943, 135.505 86.262 C 135.267 85.581, 135.500 85.131, 136.023 85.262 C 136.546 85.393, 139.596 82.688, 142.802 79.250 C 149.624 71.934, 151.704 71.504, 156.600 76.400 C 160.675 80.475, 160.857 82.828, 157.412 86.923 C 155.988 88.615, 154.708 90.787, 154.566 91.749 C 154.425 92.712, 153.782 93.500, 153.136 93.500 C 152.491 93.500, 145.827 100.475, 138.327 109 C 130.826 117.525, 124.128 124.637, 123.441 124.805 C 122.337 125.074, 105.328 110.077, 100.750 104.797 C 98.234 101.895, 98.576 99.552, 102.104 95.535 C 105.871 91.244, 108.759 91.020, 113.817 94.626 L 117.500 97.252 114.234 94.126 C 110.887 90.922, 107.396 90.183, 104.057 91.970 C 102.309 92.905, 102.308 92.698, 104.021 86.174 C 104.998 82.453, 106.121 76.382, 106.517 72.683 C 107.108 67.171, 107.691 65.601, 109.754 63.979 C 111.137 62.891, 113.160 62, 114.249 62 C 116.722 62, 124 66.016, 124 67.380 C 124 67.937, 125.463 67.003, 127.250 65.304 C 130.045 62.648, 137.815 58, 139.460 58 C 139.757 58, 140 59.548, 140 61.441 C 140 63.334, 140.526 65.207, 141.170 65.605 C 141.968 66.098, 142.127 65.773, 141.670 64.582 C 141.301 63.622, 141 56.348, 141 48.418 L 141 34 113 34 L 85 34 85 92.750 M 86.202 41.182 C 86.479 46.935, 86.686 47.413, 89.178 48.076 C 91.325 48.647, 91.741 48.489, 91.277 47.279 C 90.314 44.770, 94.840 46.697, 101.037 51.434 L 106.573 55.667 108.287 53.395 C 110.423 50.563, 110.420 50.238, 108.250 49.767 C 105.845 49.245, 112.026 45.335, 117.750 43.757 C 120.088 43.112, 122 42.276, 122 41.898 C 122 41.521, 123.306 39.814, 124.901 38.106 L 127.802 35 106.853 35 L 85.905 35 86.202 41.182 M 132 38.378 C 132 42.105, 133.130 43.397, 137.250 44.381 C 139.972 45.031, 140 44.987, 140 40.019 L 140 35 136 35 C 132.123 35, 132 35.104, 132 38.378 M 148 49.268 L 148 60 158.500 60 C 164.275 60, 169 59.879, 169 59.732 C 169 59.585, 164.275 54.755, 158.500 49 L 148 38.536 148 49.268 M 149 45.229 C 149 48.295, 149.810 49.715, 153.887 53.792 C 158.563 58.467, 158.866 58.610, 160.900 57.075 C 163.002 55.490, 162.949 55.395, 156.013 48.513 L 149 41.554 149 45.229 M 205.833 52.500 C 209.374 57.258, 210.579 62.049, 208.986 65.027 C 208.354 66.206, 208.083 67.416, 208.382 67.715 C 208.681 68.014, 209.449 67.110, 210.088 65.706 C 211.826 61.893, 210.414 56.785, 206.410 52.396 L 202.855 48.500 205.833 52.500 M 150.500 56 C 151.495 57.100, 152.535 58, 152.810 58 C 153.085 58, 152.495 57.100, 151.500 56 C 150.505 54.900, 149.465 54, 149.190 54 C 148.915 54, 149.505 54.900, 150.500 56 M 69.421 59.419 C 66.126 60.854, 64.519 63.894, 63.493 70.635 C 63.146 72.909, 61.976 75.722, 60.893 76.885 L 58.922 79 61.737 79 C 64.392 79, 64.532 78.784, 64.192 75.215 C 63.939 72.568, 64.407 70.719, 65.747 69.064 C 69.414 64.536, 74.720 68.345, 74.251 75.169 C 74.188 76.087, 74.781 77.086, 75.568 77.388 C 76.748 77.840, 77 76.288, 77 68.576 C 77 60.962, 76.705 59.101, 75.418 58.607 C 73.334 57.807, 73.027 57.849, 69.421 59.419 M 86 62.965 C 86 65.809, 86.527 67.211, 87.863 67.927 C 90.440 69.306, 92.366 74.418, 91.653 77.986 C 91.186 80.322, 91.494 81.194, 93.090 82.048 C 96.509 83.878, 95.130 87.134, 90.250 88.755 L 86 90.167 86 108.648 L 86 127.129 92.413 121.464 C 98.414 116.162, 98.764 115.621, 97.858 113.022 C 96.466 109.029, 92.528 105.629, 90.048 106.277 C 86.155 107.295, 88.017 104.865, 92.778 102.713 C 96.271 101.134, 97.989 99.606, 99.168 97.027 C 101.129 92.738, 102.295 84.300, 101.035 83.522 C 100.532 83.211, 100.376 79.905, 100.688 76.177 C 101.120 71.029, 100.868 68.646, 99.643 66.277 C 97.514 62.159, 92.935 59, 89.096 59 C 86.155 59, 86 59.199, 86 62.965 M 134.174 69.314 C 131.582 72.074, 131.288 77.538, 133.557 80.777 C 135.252 83.198, 137.469 83.668, 138.595 81.845 C 139 81.190, 138.873 80.960, 138.302 81.313 C 137.748 81.656, 136.578 81.388, 135.702 80.718 C 131.058 77.166, 133.897 69.183, 139.320 70.544 C 141.330 71.049, 142.257 72.033, 142.671 74.105 C 143.297 77.235, 145 77.082, 145 73.896 C 145 71.384, 140.690 67, 138.220 67 C 137.191 67, 135.370 68.042, 134.174 69.314 M 144.416 68.250 C 144.737 68.938, 145.261 70.229, 145.582 71.120 C 146.010 72.308, 147.342 72.708, 150.576 72.620 C 154.692 72.508, 154.988 72.315, 154.994 69.750 C 155 67.078, 154.842 67, 149.417 67 C 145.306 67, 143.987 67.330, 144.416 68.250 M 67.278 69.250 C 65.586 71.436, 64.983 74.085, 65.609 76.579 C 66.498 80.119, 67.707 79.536, 67.328 75.750 C 67.150 73.963, 67.266 73.065, 67.587 73.756 C 67.908 74.447, 68.604 74.745, 69.134 74.417 C 69.727 74.051, 69.647 73.279, 68.926 72.411 C 68.002 71.298, 68.087 71, 69.328 71 C 70.193 71, 71.359 71.787, 71.920 72.750 C 72.791 74.246, 72.944 74.269, 72.970 72.905 C 72.998 71.437, 70.071 68, 68.794 68 C 68.492 68, 67.810 68.563, 67.278 69.250 M 111.557 70.223 C 109.257 73.505, 109.623 77.714, 112.455 80.545 C 115.510 83.601, 117.059 83.631, 119.826 80.686 C 124.076 76.161, 121.615 68, 116 68 C 114.151 68, 112.554 68.799, 111.557 70.223 M 184.500 68.826 C 182.453 69.295, 181.401 70.205, 181.189 71.693 C 180.900 73.716, 181.091 73.792, 183.838 72.755 C 186.256 71.842, 187.271 71.948, 189.383 73.332 C 194.594 76.746, 193.476 82.916, 186.924 86.900 L 183.957 88.704 188.972 88.602 C 192.356 88.533, 193.990 88.078, 193.994 87.205 C 193.997 86.492, 195.105 84.805, 196.455 83.455 C 202.316 77.593, 209.333 83.294, 204.598 90.072 C 203.994 90.936, 205.188 89.866, 207.250 87.693 C 213.519 81.089, 212.009 77, 203.300 77 C 198.128 77, 198 76.931, 198 74.122 C 198 72.540, 197.343 70.699, 196.539 70.033 C 194.732 68.533, 188.509 67.909, 184.500 68.826 M 86 69.880 C 86 70.535, 86.725 72.176, 87.610 73.528 C 88.639 75.099, 88.986 76.920, 88.570 78.577 C 88.148 80.258, 88.285 80.942, 88.960 80.525 C 90.828 79.370, 90.167 72.461, 88 70.500 C 86.801 69.415, 86 69.167, 86 69.880 M 115.500 73 C 116.208 75.229, 115.037 75.637, 113.200 73.800 C 112.267 72.867, 112 73.277, 112 75.645 C 112 77.407, 112.832 79.443, 113.974 80.477 C 115.823 82.150, 116.109 82.158, 118.474 80.609 C 121.374 78.709, 121.819 74.962, 119.429 72.571 C 117.123 70.265, 114.715 70.528, 115.500 73 M 135.500 73 C 134.684 74.320, 135.880 76.192, 137.069 75.457 C 138.284 74.707, 138.276 72, 137.059 72 C 136.541 72, 135.840 72.450, 135.500 73 M 182.637 75.276 C 180.900 76.546, 181.244 80, 183.107 80 C 184.445 80, 184.183 78.247, 182.750 77.615 C 182.063 77.312, 182.850 77.162, 184.500 77.282 C 186.802 77.449, 187.577 78.043, 187.830 79.832 C 188.012 81.115, 187.380 83.028, 186.425 84.082 C 185.471 85.137, 185.177 86, 185.772 86 C 187.334 86, 191 81.047, 191 78.937 C 191 74.877, 186.110 72.737, 182.637 75.276 M 170.044 77.277 C 168.992 79.379, 168.936 80.336, 169.810 81.210 C 171.483 82.883, 172.192 81.675, 171.788 77.846 L 171.434 74.500 170.044 77.277 M 69.750 79.668 C 68.787 79.931, 68 80.564, 68 81.073 C 68 81.583, 67.325 82, 66.500 82 C 65.675 82, 65 82.413, 65 82.918 C 65 83.423, 64.727 84.548, 64.393 85.418 C 63.949 86.576, 64.418 87.019, 66.143 87.070 C 68.352 87.136, 68.375 87.194, 66.500 88 C 64.564 88.832, 64.569 88.862, 66.668 88.930 C 69.439 89.020, 72.119 88.001, 71.547 87.076 C 71.304 86.683, 72.462 86.561, 74.121 86.804 C 77.073 87.238, 77.129 87.166, 76.818 83.373 C 76.545 80.044, 76.149 79.478, 74 79.345 C 72.625 79.260, 70.713 79.405, 69.750 79.668 M 137.398 94.908 C 130.304 103.144, 123.819 109.909, 122.986 109.942 C 122.153 109.974, 118.400 107.227, 114.645 103.838 C 109.290 99.005, 107.625 97.981, 106.924 99.088 C 106.433 99.865, 106.024 100.782, 106.015 101.127 C 106.007 101.472, 109.796 105.201, 114.436 109.413 L 122.873 117.072 137.966 99.874 C 146.267 90.416, 152.905 82.215, 152.717 81.651 C 151.804 78.911, 149.335 81.047, 137.398 94.908 M 137.963 95.930 C 130.096 104.890, 124.002 111, 122.930 111 C 121.939 111, 118.290 108.525, 114.821 105.500 C 107.774 99.354, 104.776 98.110, 110 103.500 C 111.866 105.425, 114.006 107, 114.755 107 C 115.505 107, 115.864 107.410, 115.554 107.912 C 115.028 108.764, 122.780 115.678, 123.357 114.871 C 123.793 114.259, 147.147 87.716, 149.849 84.760 C 151.227 83.254, 152.093 81.760, 151.774 81.440 C 151.454 81.121, 145.239 87.641, 137.963 95.930 M 56.015 84.800 C 55.985 91.250, 61.528 97.704, 71.984 103.395 L 77 106.125 77 101.736 C 77 97.447, 76.932 97.356, 74.059 97.778 C 71.861 98.101, 70.461 97.552, 68.514 95.605 C 67.081 94.172, 65.078 93, 64.062 93 C 63.045 93, 61.958 92.333, 61.645 91.517 C 61.281 90.569, 60.430 90.278, 59.288 90.711 C 57.585 91.356, 57.583 91.304, 59.250 89.623 C 60.212 88.652, 61 87.200, 61 86.397 C 61 85.594, 60.212 84.647, 59.250 84.292 C 58.288 83.936, 57.169 83.163, 56.765 82.573 C 56.361 81.983, 56.024 82.985, 56.015 84.800 M 68.500 83 C 68.160 83.550, 68.359 84, 68.941 84 C 69.523 84, 70 83.550, 70 83 C 70 82.450, 69.802 82, 69.559 82 C 69.316 82, 68.840 82.450, 68.500 83 M 86 83.480 C 86 84.566, 87.057 85, 89.700 85 C 93.881 85, 93.176 83.262, 88.663 82.444 C 86.735 82.094, 86 82.380, 86 83.480 M 119.721 83.605 C 118.882 84.452, 117.333 84.886, 112.250 85.699 C 108.675 86.271, 108.037 87.419, 110.750 88.399 C 111.713 88.747, 113.525 89.635, 114.778 90.372 C 117.021 91.692, 122.393 91.445, 121.538 90.062 C 121.297 89.671, 122.039 89.385, 123.188 89.426 C 124.481 89.472, 125.386 88.799, 125.563 87.658 C 125.721 86.645, 126.151 85.520, 126.520 85.158 C 126.889 84.796, 127.035 85.258, 126.845 86.184 C 126.655 87.111, 126.838 87.898, 127.250 87.934 C 130.379 88.208, 132.023 87.847, 131.500 87 C 131.160 86.450, 131.584 86, 132.441 86 C 134.287 86, 134.566 84.387, 132.750 84.214 C 132.063 84.149, 129.025 83.808, 126 83.457 C 122.975 83.106, 120.149 83.173, 119.721 83.605 M 197.571 84.571 C 196.707 85.436, 196 86.261, 196 86.405 C 196 87.151, 199.591 86.557, 200.138 85.721 C 201.275 83.981, 203.109 86.075, 202.428 88.334 C 201.984 89.809, 202.104 90.181, 202.804 89.500 C 203.845 88.488, 204.335 86.532, 204.118 84.250 C 203.950 82.477, 199.445 82.698, 197.571 84.571 M 119.733 87.124 C 120.412 87.808, 121.219 88.115, 121.526 87.807 C 121.834 87.499, 121.279 86.940, 120.293 86.564 C 118.868 86.022, 118.753 86.136, 119.733 87.124 M 171.252 92.500 C 171.263 94.700, 171.468 95.482, 171.707 94.238 C 171.946 92.994, 171.937 91.194, 171.687 90.238 C 171.437 89.282, 171.241 90.300, 171.252 92.500 M 221.158 92.500 C 220.905 98.190, 216.619 106.726, 211.706 111.326 C 209.393 113.491, 208.400 114.792, 209.500 114.217 C 215.614 111.022, 222.413 98.823, 221.730 92.275 L 221.336 88.500 221.158 92.500 M 181.177 91.666 C 181.551 93.593, 188.068 97.444, 192.624 98.428 C 195.403 99.029, 195.800 98.826, 196.229 96.585 C 197.062 92.224, 193.907 90, 186.888 90 C 181.794 90, 180.904 90.260, 181.177 91.666 M 65 95.902 C 65 96.940, 66.902 98.443, 67.466 97.850 C 67.649 97.658, 67.169 96.920, 66.399 96.211 C 65.630 95.501, 65 95.362, 65 95.902 M 149.415 98.250 C 143.859 104.315, 142.533 106.517, 146.855 102.500 C 150.604 99.015, 153.750 95, 152.731 95 C 152.545 95, 151.053 96.463, 149.415 98.250 M 199.733 97.124 C 200.412 97.808, 201.219 98.115, 201.526 97.807 C 201.834 97.499, 201.279 96.940, 200.293 96.564 C 198.868 96.022, 198.753 96.136, 199.733 97.124 M 181 106.291 C 181 108.146, 183.965 110.039, 186.684 109.921 C 188.081 109.860, 188.154 109.715, 187 109.294 C 186.175 108.992, 184.488 107.824, 183.250 106.698 C 181.331 104.953, 181 104.893, 181 106.291 M 72.241 108.986 C 70.683 111.480, 70.685 111.996, 72.250 112.015 C 72.938 112.024, 74.157 112.446, 74.960 112.954 C 76.231 113.758, 76.217 114.113, 74.852 115.689 C 73.595 117.140, 71 123.159, 71 124.624 C 71 124.802, 72.350 125.647, 74 126.500 L 77 128.051 77 117.526 C 77 108.146, 76.808 107, 75.241 107 C 74.273 107, 72.923 107.894, 72.241 108.986 M 73.872 109.750 C 72.629 111.336, 72.664 111.371, 74.250 110.128 C 75.916 108.821, 76.445 108, 75.622 108 C 75.415 108, 74.627 108.787, 73.872 109.750 M 59 110 C 58.099 110.582, 57.975 110.975, 58.691 110.985 C 59.346 110.993, 60.160 110.550, 60.500 110 C 61.267 108.758, 60.921 108.758, 59 110 M 132.183 117.538 C 128.158 122.192, 124.087 126, 123.136 126 C 122.184 126, 119.648 124.200, 117.500 122 C 115.352 119.800, 113.069 118, 112.427 118 C 111.208 118, 109.852 124.001, 108.021 137.500 C 107.425 141.900, 106.717 146.512, 106.449 147.750 L 105.962 150 128.481 150 C 140.866 150, 151 149.801, 151 149.558 C 151 148.426, 143.794 116.066, 142.803 112.750 C 142.187 110.688, 141.192 109.017, 140.592 109.038 C 139.991 109.059, 136.207 112.884, 132.183 117.538 M 169.975 113.565 C 168.680 116.971, 168.735 117.535, 170.500 119 C 171.797 120.076, 172 119.618, 172 115.622 C 172 110.412, 171.404 109.806, 169.975 113.565 M 184.750 112.662 C 185.438 112.940, 186.563 112.940, 187.250 112.662 C 187.938 112.385, 187.375 112.158, 186 112.158 C 184.625 112.158, 184.063 112.385, 184.750 112.662 M 181 120.719 C 181 127.823, 181.253 128.889, 183.426 130.930 C 185.791 133.152, 188.582 132.959, 187.691 130.636 C 187.446 129.998, 187.583 127.247, 187.996 124.525 C 188.817 119.119, 187.595 114, 185.484 114 C 184.758 114, 183.452 113.727, 182.582 113.393 C 181.221 112.871, 181 113.896, 181 120.719 M 64.547 151.209 C 65.898 161.958, 68.800 168.674, 71.626 167.590 C 72.576 167.226, 72.848 167.437, 72.388 168.181 C 71.905 168.962, 72.231 169.125, 73.388 168.681 C 74.334 168.318, 74.850 168.434, 74.537 168.939 C 73.803 170.128, 77.627 171.349, 79.146 170.410 C 79.828 169.988, 80.041 170.125, 79.660 170.741 C 78.944 171.899, 85.970 172.902, 89.780 172.184 C 92.582 171.657, 93.543 169.880, 94.342 163.750 L 94.962 159 87.552 159 C 80.495 159, 78.013 158.117, 76.839 155.189 C 76.653 154.724, 73.694 152.709, 70.264 150.711 L 64.028 147.079 64.547 151.209 M 95 149 C 94.099 149.582, 93.975 149.975, 94.691 149.985 C 95.346 149.993, 96.160 149.550, 96.500 149 C 97.267 147.758, 96.921 147.758, 95 149 M 104.607 160.582 C 103.021 164.716, 104.774 178.620, 107.574 184.108 C 108.789 186.490, 109.320 186.676, 113.713 186.254 C 117.497 185.890, 119.047 186.218, 120.990 187.791 C 123.146 189.536, 123.329 190.154, 122.429 192.645 C 121.554 195.068, 120.914 195.462, 118.199 195.246 C 116.440 195.107, 115 195.182, 115 195.415 C 115 196.110, 121.648 200, 122.835 200 C 123.552 200, 124.033 196.089, 124.217 188.750 L 124.500 177.500 129 177.500 L 133.500 177.500 134 185.500 L 134.500 193.500 136.276 190.250 C 137.877 187.319, 138.467 187.017, 142.276 187.168 L 146.500 187.336 142.715 187.731 C 139.366 188.079, 138.617 188.692, 136.215 193.048 C 134.722 195.756, 133.275 197.979, 133 197.988 C 132.725 197.997, 132.642 193.503, 132.816 188.002 L 133.133 178 129.066 178 L 125 178 125 196.060 L 125 214.121 128.750 213.810 L 132.500 213.500 133 208.161 L 133.500 202.822 136.500 208.408 C 139.421 213.846, 139.612 213.993, 143.770 213.997 L 148.040 214 145.069 208.798 C 143.435 205.937, 141.402 202.782, 140.550 201.788 C 139.131 200.130, 139.259 199.627, 142.097 195.740 C 143.799 193.408, 145.582 191.607, 146.060 191.738 C 146.537 191.869, 146.689 191.352, 146.396 190.589 C 146.103 189.826, 146.344 188.905, 146.932 188.542 C 147.519 188.179, 148.060 185.771, 148.134 183.191 L 148.267 178.500 149.046 183.500 C 149.474 186.250, 149.864 193.732, 149.912 200.128 C 150.013 213.446, 150.463 214.294, 157.176 213.811 C 161.244 213.518, 161.500 213.323, 161.500 210.501 C 161.500 208.170, 161.055 207.502, 159.500 207.501 C 157.589 207.500, 157.500 206.833, 157.500 192.497 L 157.500 177.493 153.500 177.695 C 151.300 177.807, 150.236 177.673, 151.135 177.398 C 153.608 176.641, 154.932 167.078, 153.370 161.250 L 152.767 159 128.990 159 C 108.232 159, 105.137 159.201, 104.607 160.582 M 68.031 161.500 C 68.031 162.050, 68.467 163.175, 69 164 C 69.533 164.825, 69.969 165.050, 69.969 164.500 C 69.969 163.950, 69.533 162.825, 69 162 C 68.467 161.175, 68.031 160.950, 68.031 161.500 M 171.488 163.527 C 171.769 164.887, 172.243 167.162, 172.539 168.581 L 173.079 171.163 175.589 167.464 C 176.969 165.430, 177.852 163.518, 177.549 163.216 C 177.247 162.914, 177 163.278, 177 164.024 C 177 165.065, 176.708 165.020, 175.750 163.831 C 175.063 162.978, 173.707 162.004, 172.738 161.666 C 171.291 161.163, 171.067 161.497, 171.488 163.527 M 169 171.333 C 169 174.450, 169.434 177, 169.965 177 C 170.862 177, 170.351 167.059, 169.404 166.083 C 169.182 165.854, 169 168.217, 169 171.333 M 36.479 177.945 C 28.619 179.789, 24 186.604, 24 196.356 C 24 207.654, 29.806 213.989, 40.168 213.996 C 47.068 214.001, 49 213.340, 49 210.975 C 49 207.022, 48.016 206.188, 43.993 206.728 C 37.810 207.557, 35.067 206.404, 33.328 202.243 C 31.407 197.644, 32.019 189.742, 34.509 186.990 C 35.908 185.445, 37.590 185, 42.038 185 C 47.490 185, 47.793 184.867, 48.306 182.250 C 48.603 180.738, 48.543 179.220, 48.173 178.878 C 47.003 177.796, 39.622 177.207, 36.479 177.945 M 163.917 177.451 C 163.871 177.478, 163.716 178.583, 163.572 179.906 C 163.323 182.203, 166.013 186, 167.889 186 C 168.378 186, 169.788 185.182, 171.022 184.183 C 174.170 181.634, 172.383 178.380, 167.525 177.813 C 165.586 177.587, 163.963 177.424, 163.917 177.451 M 52 196 L 52 214 56 214 L 60 214 60 205.122 C 60 195.774, 60.993 193.464, 64.702 194.183 C 66.289 194.491, 66.500 195.702, 66.500 204.520 L 66.500 214.508 70.369 214.138 C 72.497 213.934, 74.522 214.228, 74.869 214.790 C 75.581 215.944, 75.230 199.232, 74.439 194.293 C 73.902 190.942, 69.936 186.995, 67.115 187.006 C 66.227 187.010, 64.263 187.658, 62.750 188.447 L 60 189.881 60 183.941 L 60 178 56 178 L 52 178 52 196 M 67.500 185.062 L 65.500 185.983 67.500 185.712 C 68.600 185.563, 70.620 186.466, 71.990 187.720 C 74.836 190.326, 76.288 190.562, 77.855 188.675 C 78.711 187.643, 78.176 186.977, 75.444 185.675 C 71.533 183.810, 70.407 183.723, 67.500 185.062 M 36.001 187.499 C 34.931 188.788, 35.160 188.920, 37.628 188.436 C 39.208 188.127, 41.274 187.765, 42.221 187.632 C 45.809 187.130, 45.164 186, 41.289 186 C 38.915 186, 36.731 186.619, 36.001 187.499 M 47.655 188.829 C 46.745 189.835, 46.014 191.072, 46.030 191.579 C 46.047 192.085, 46.571 191.620, 47.195 190.544 C 48.321 188.603, 48.335 188.603, 49.099 190.544 C 49.727 192.140, 49.841 191.994, 49.716 189.750 C 49.632 188.238, 49.507 187, 49.437 187 C 49.367 187, 48.565 187.823, 47.655 188.829 M 85.500 188.369 C 79.795 190.951, 78.508 193.414, 78.419 201.926 C 78.375 206.170, 78.004 210.285, 77.595 211.071 C 77.172 211.884, 77.435 211.794, 78.205 210.861 C 79.443 209.362, 79.793 209.425, 82.335 211.611 C 84.445 213.426, 86.288 214, 90.009 214 C 94.594 214, 100.019 212.417, 99.985 211.089 C 99.920 208.592, 97.615 207.500, 92.410 207.500 C 87.460 207.500, 86.647 207.219, 85.909 205.250 C 85.080 203.042, 85.205 203, 92.513 203 C 99.624 203, 99.990 202.887, 100.589 200.500 C 102.710 192.050, 93.582 184.711, 85.500 188.369 M 110.158 188.020 C 108.869 188.561, 107.396 188.745, 106.883 188.428 C 106.370 188.111, 106.054 188.222, 106.181 188.676 C 106.308 189.129, 105.644 190.940, 104.706 192.700 C 101.338 199.019, 103.158 210.409, 107.945 212.970 C 110.641 214.414, 118.270 214.254, 120.381 212.711 C 121.660 211.775, 121.962 210.695, 121.480 208.774 C 120.882 206.393, 120.573 206.237, 118.408 207.224 C 112.862 209.751, 108.245 203.366, 111.219 197.286 C 112.409 194.853, 113.104 194.523, 116.700 194.680 C 120.358 194.840, 120.890 194.573, 121.464 192.286 C 121.820 190.869, 121.748 189.349, 121.305 188.907 C 119.954 187.557, 112.644 186.976, 110.158 188.020 M 168.250 187.732 L 164 188.115 164 200.990 C 164 213.449, 163.927 213.890, 161.750 214.631 C 160.512 215.052, 159.129 215.296, 158.676 215.173 C 158.222 215.051, 158.166 215.460, 158.551 216.082 C 159.353 217.380, 156.136 216.578, 151.899 214.425 C 150.423 213.674, 148.909 213.477, 148.399 213.969 C 146.957 215.360, 140.157 215.557, 138.847 214.247 C 137.915 213.315, 137.310 213.321, 136.132 214.274 C 134.907 215.265, 134.772 215.241, 135.429 214.150 C 136.087 213.057, 135.839 213.016, 134.121 213.935 C 132.954 214.560, 132 215.306, 132 215.594 C 132 215.882, 132.521 215.796, 133.158 215.403 C 133.958 214.908, 134.133 215.385, 133.723 216.953 C 133.148 219.148, 133.296 219.200, 138.505 218.632 C 141.785 218.274, 145.364 218.545, 147.690 219.325 C 152.093 220.804, 154.716 220.757, 157.726 219.147 C 158.904 218.516, 161.505 218, 163.506 218 C 165.536 218, 167.864 217.279, 168.775 216.368 C 170.284 214.859, 170.247 214.787, 168.288 215.409 C 167.122 215.779, 165.879 215.613, 165.525 215.041 C 165.126 214.395, 166.214 214, 168.387 214 L 171.892 214 172.196 200.498 C 172.363 193.072, 172.500 187.076, 172.500 187.173 C 172.500 187.270, 170.588 187.522, 168.250 187.732 M 182 187.706 C 179.076 188.775, 176 192.381, 176 194.740 C 176 198.642, 177.903 201.069, 182.586 203.141 C 185.139 204.271, 187 205.765, 187 206.686 C 187 208.007, 186.157 208.179, 182.051 207.692 C 177.620 207.168, 177.031 207.328, 176.428 209.227 C 175.340 212.653, 178.461 214.144, 186.083 213.839 C 189.733 213.693, 192.700 213.113, 192.741 212.538 C 192.782 211.967, 193.388 210.083, 194.089 208.351 C 195.189 205.631, 195.121 204.834, 193.597 202.507 C 192.626 201.025, 189.834 198.929, 187.392 197.849 C 182.464 195.670, 182.428 193.992, 187.309 194.015 C 191.455 194.035, 193.896 195.442, 191.428 196.389 C 189.951 196.956, 190.195 197.430, 193.038 199.528 C 197.322 202.689, 198.347 202.726, 194.699 199.589 C 193.159 198.264, 192.344 196.905, 192.888 196.569 C 193.432 196.233, 194.341 196.755, 194.908 197.729 C 195.475 198.703, 195.953 198.993, 195.970 198.372 C 195.986 197.752, 195.305 196.668, 194.456 195.963 C 193.410 195.096, 193.108 193.784, 193.521 191.906 C 194.079 189.364, 193.808 189.041, 190.315 188.071 C 186.203 186.929, 184.349 186.847, 182 187.706 M 86 196 C 85.001 197.866, 85.240 198, 89.572 198 C 93.394 198, 94.107 197.721, 93.607 196.418 C 93.273 195.548, 93 194.648, 93 194.418 C 93 194.188, 91.666 194, 90.035 194 C 88.094 194, 86.701 194.690, 86 196 M 173.604 195.750 C 171.902 201.367, 173.935 205.825, 178 205.391 C 178.825 205.303, 180.163 205.179, 180.973 205.115 C 181.943 205.039, 181.602 204.424, 179.973 203.310 C 178.613 202.381, 177.135 201.022, 176.689 200.289 C 176.242 199.557, 175.494 199.195, 175.025 199.485 C 174.556 199.774, 174.386 198.659, 174.646 197.006 C 175.160 193.749, 174.465 192.912, 173.604 195.750 M 87.504 195.993 C 87.148 196.569, 87.964 196.898, 89.445 196.775 C 90.850 196.659, 92 196.212, 92 195.782 C 92 194.645, 88.228 194.822, 87.504 195.993 M 187.510 196.016 C 187.856 196.575, 188.557 196.774, 189.069 196.457 C 190.456 195.600, 190.198 195, 188.441 195 C 187.584 195, 187.165 195.457, 187.510 196.016 M 217.590 204.855 C 216.918 205.942, 219.232 207, 222.282 207 C 223.562 207, 224.024 206.649, 223.450 206.112 C 221.876 204.642, 218.208 203.855, 217.590 204.855 M 99.617 208.443 C 99.921 209.237, 100.919 210.057, 101.835 210.265 C 103.405 210.623, 97.567 213.901, 92.556 215.475 C 90.810 216.024, 90.921 216.274, 93.651 217.934 C 97.510 220.281, 102.424 219.890, 105.511 216.990 L 107.900 214.746 104.700 210.891 C 101.541 207.085, 98.541 205.641, 99.617 208.443 M 79.569 213.682 C 78.606 216.191, 80.086 216.894, 84.500 216.021 C 86.700 215.586, 87.588 215.178, 86.473 215.115 C 85.357 215.052, 83.491 214.331, 82.324 213.514 C 80.425 212.184, 80.137 212.202, 79.569 213.682 M 174.500 214 C 174.840 214.550, 175.568 215, 176.118 215 C 176.668 215, 176.840 214.550, 176.500 214 C 176.160 213.450, 175.432 213, 174.882 213 C 174.332 213, 174.160 213.450, 174.500 214 M 101.583 215.866 C 101.152 216.563, 101.070 217.403, 101.400 217.733 C 102.295 218.628, 103.271 217.312, 102.787 215.862 C 102.449 214.846, 102.213 214.846, 101.583 215.866 M 171.325 215.658 C 171.687 216.020, 171.511 216.884, 170.934 217.579 C 170.131 218.547, 170.372 218.715, 171.964 218.299 C 173.599 217.871, 173.799 217.462, 172.899 216.378 C 172.270 215.620, 171.510 215, 171.211 215 C 170.912 215, 170.963 215.296, 171.325 215.658\",stroke:\"none\",fill:\"#625398\",fillRule:\"evenodd\"}))};/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CheckListIcon);\n\n//# sourceURL=webpack://publishpress-checklists/./modules/checklists/assets/js/CheckListIcon.jsx?");

/***/ }),

/***/ "./modules/checklists/assets/js/gutenberg-panel.jsx":
/*!**********************************************************!*\
  !*** ./modules/checklists/assets/js/gutenberg-panel.jsx ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/regenerator */ \"./node_modules/@babel/runtime/regenerator/index.js\");\n/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/typeof */ \"./node_modules/@babel/runtime/helpers/typeof.js\");\n/* harmony import */ var _babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/slicedToArray */ \"./node_modules/@babel/runtime/helpers/slicedToArray.js\");\n/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/classCallCheck */ \"./node_modules/@babel/runtime/helpers/classCallCheck.js\");\n/* harmony import */ var _babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @babel/runtime/helpers/createClass */ \"./node_modules/@babel/runtime/helpers/createClass.js\");\n/* harmony import */ var _babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _babel_runtime_helpers_possibleConstructorReturn__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @babel/runtime/helpers/possibleConstructorReturn */ \"./node_modules/@babel/runtime/helpers/possibleConstructorReturn.js\");\n/* harmony import */ var _babel_runtime_helpers_possibleConstructorReturn__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_possibleConstructorReturn__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @babel/runtime/helpers/getPrototypeOf */ \"./node_modules/@babel/runtime/helpers/getPrototypeOf.js\");\n/* harmony import */ var _babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _babel_runtime_helpers_assertThisInitialized__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @babel/runtime/helpers/assertThisInitialized */ \"./node_modules/@babel/runtime/helpers/assertThisInitialized.js\");\n/* harmony import */ var _babel_runtime_helpers_assertThisInitialized__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_assertThisInitialized__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @babel/runtime/helpers/inherits */ \"./node_modules/@babel/runtime/helpers/inherits.js\");\n/* harmony import */ var _babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"./node_modules/@babel/runtime/helpers/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _CheckListIcon_jsx__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./CheckListIcon.jsx */ \"./modules/checklists/assets/js/CheckListIcon.jsx\");\nvar registerPlugin=wp.plugins.registerPlugin,_wp$editPost=wp.editPost,PluginSidebarMoreMenuItem=_wp$editPost.PluginSidebarMoreMenuItem,PluginSidebar=_wp$editPost.PluginSidebar,_wp$element=wp.element,Fragment=_wp$element.Fragment,Component=_wp$element.Component,__=wp.i18n.__,_wp=wp,hooks=_wp.hooks;var PPChecklistsPanel=/*#__PURE__*/function(a){function b(a){var c;return _babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_3___default()(this,b),c=_babel_runtime_helpers_possibleConstructorReturn__WEBPACK_IMPORTED_MODULE_5___default()(this,_babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_6___default()(b).call(this,a)),_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_9___default()(_babel_runtime_helpers_assertThisInitialized__WEBPACK_IMPORTED_MODULE_7___default()(c),\"isMounted\",!1),_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_9___default()(_babel_runtime_helpers_assertThisInitialized__WEBPACK_IMPORTED_MODULE_7___default()(c),\"oldStatus\",\"\"),_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_9___default()(_babel_runtime_helpers_assertThisInitialized__WEBPACK_IMPORTED_MODULE_7___default()(c),\"currentStatus\",\"\"),_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_9___default()(_babel_runtime_helpers_assertThisInitialized__WEBPACK_IMPORTED_MODULE_7___default()(c),\"handleRequirementStatusChange\",function(){c.updateRequirements(c.state.requirements)}),_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_9___default()(_babel_runtime_helpers_assertThisInitialized__WEBPACK_IMPORTED_MODULE_7___default()(c),\"updateRequirements\",function(a){if(c.isMounted){var b=Object.values(a).some(function(a){return\"block\"===a.rule}),d=Object.entries(a).map(function(a){var b=_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2___default()(a,2),c=b[0],d=b[1],e=d.id||c,f=document.querySelector(\"#ppch_item_\".concat(e));return f&&(d.status=\"yes\"==f.value),d.id=e,d});c.setState({showRequiredLegend:b,requirements:d})}}),c.state={showRequiredLegend:!1,requirements:[],failedRequirements:[]},c}return _babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_8___default()(b,a),_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_4___default()(b,[{key:\"componentDidMount\",value:function componentDidMount(){var a=this;this.isMounted=!0,\"undefined\"!=typeof ppChecklists&&this.updateRequirements(ppChecklists.requirements),hooks.addAction(\"pp-checklists.update-failed-requirements\",\"publishpress/checklists\",this.updateFailedRequirements.bind(this),10),hooks.addAction(\"pp-checklists.requirements-updated\",\"publishpress/checklists\",this.handleRequirementStatusChange.bind(this),10);/**\n         * Our less problematic solution till gutenberg Add a way \n         * for third parties to perform additional save validation \n         * in this issue https://github.com/WordPress/gutenberg/issues/13413\n         * is this solution as it also solves third party conflict with\n         * locking post (Rankmath, Yoast SEO etc)\n         */var b=wp.data.dispatch(\"core/editor\"),c=wp.data.dispatch(\"core/notices\"),d=b.savePost,e=b.editPost;this.oldStatus&&\"\"!=this.oldStatus||(this.oldStatus=wp.data.select(\"core/editor\").getCurrentPost().status),wp.data.dispatch(\"core/editor\").editPost=function(b,c){return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_0___default().async(function(d){for(;;)switch(d.prev=d.next){case 0:if(c=c||{},1!==c.pp_checklists_edit_filtered&&1!==c.pp_checklists_post_status_edit){d.next=3;break}return d.abrupt(\"return\",e(b,c));case 3:return\"object\"===_babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_1___default()(b)&&b.status&&(a.currentStatus=b.status),c.pp_checklists_edit_filtered=1,d.abrupt(\"return\",e(b,c));case 6:case\"end\":return d.stop();}})},wp.data.dispatch(\"core/editor\").savePost=function(b){var e,f;return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_0___default().async(function(g){for(;;)switch(g.prev=g.next){case 0:if(b=b||{},e=!1,f={publish:!0,// already published post\nfuture:!0// scheduled post\n},b.isAutosave||b.isPreview?e=!1:\"\"===a.currentStatus?wp.data.select(\"core/edit-post\").isPublishSidebarOpened()||\"publish\"===wp.data.select(\"core/editor\").getEditedPostAttribute(\"status\")||\"publish\"===wp.data.select(\"core/editor\").getCurrentPost().status?wp.data.select(\"core/edit-post\").isPublishSidebarOpened()&&\"publish\"==wp.data.select(\"core/editor\").getEditedPostAttribute(\"status\")?e=!0:!wp.data.select(\"core/edit-post\").isPublishSidebarOpened()&&\"publish\"==wp.data.select(\"core/editor\").getEditedPostAttribute(\"status\")&&(e=!0):e=!1:e=f[a.currentStatus]??!1,e&&\"undefined\"!=typeof a.state.failedRequirements.block&&0!==a.state.failedRequirements.block.length){g.next=8;break}return g.abrupt(\"return\",d(b));case 8:return wp.data.dispatch(\"core/edit-post\").closePublishSidebar(),c.createErrorNotice(i18n.completeRequirementMessage,{id:\"publishpress-checklists-validation\",isDismissible:!0}),wp.data.dispatch(\"core/edit-post\").openGeneralSidebar(\"publishpress-checklists-panel/checklists-sidebar\"),\"\"!==a.oldStatus&&wp.data.dispatch(\"core/editor\").editPost({status:a.oldStatus,pp_checklists_post_status_edit:!0}),g.abrupt(\"return\");case 13:case\"end\":return g.stop();}})}}},{key:\"componentDidUpdate\",value:function componentDidUpdate(a,b){\"undefined\"!=typeof ppChecklists&&JSON.stringify(Object.values(ppChecklists.requirements))!==JSON.stringify(b.requirements)&&this.updateRequirements(ppChecklists.requirements)}},{key:\"componentWillUnmount\",value:function componentWillUnmount(){hooks.removeAction(\"pp-checklists.update-failed-requirements\",\"publishpress/checklists\"),hooks.removeAction(\"pp-checklists.requirements-updated\",\"publishpress/checklists\"),this.isMounted=!1}/**\n     * Hook to failed requirement to update block requirements.\n     * \n     * @param {Array} failedRequirements \n     */},{key:\"updateFailedRequirements\",value:function updateFailedRequirements(a){this.isMounted&&this.setState({failedRequirements:a})}/**\n     * Handle requirement status change\n     */ /**\n     * Update sidebar requirements\n     * \n     * @param {Array} Requirements \n     */},{key:\"render\",value:function render(){var a=this.state,b=a.showRequiredLegend,c=a.requirements;return 0<c.length?wp.element.createElement(Fragment,null,wp.element.createElement(PluginSidebarMoreMenuItem,{target:\"checklists-sidebar\",icon:wp.element.createElement(_CheckListIcon_jsx__WEBPACK_IMPORTED_MODULE_10__[\"default\"],null)},i18n.checklistLabel),wp.element.createElement(PluginSidebar,{name:\"checklists-sidebar\",title:__(\"Checklists\",\"publishpress-checklists\")},wp.element.createElement(\"div\",{id:\"pp-checklists-sidebar-content\",className:\"components-panel__body is-opened\"},\"1\"==i18n.isElementorEnabled?wp.element.createElement(\"p\",null,wp.element.createElement(\"em\",null,i18n.elementorNotice)):wp.element.createElement(Fragment,null,0===c.length?wp.element.createElement(\"p\",null,wp.element.createElement(\"em\",null,i18n.noTaskLabel)):wp.element.createElement(\"ul\",{id:\"pp-checklists-sidebar-req-box\"},c.map(function(a,b){return wp.element.createElement(\"li\",{key:\"pp-checklists-req-panel-\".concat(b),className:\"pp-checklists-req panel-req pp-checklists-\".concat(a.rule,\" status-\").concat(a.status?\"yes\":\"no\",\" \").concat(a.is_custom?\"pp-checklists-custom-item\":\"\"),\"data-id\":a.id,\"data-type\":a.type,\"data-extra\":a.extra||\"\",\"data-source\":a.source||\"\",onClick:function onClick(){if(a.is_custom){var b=document.querySelector(\"#pp-checklists-req-\".concat(a.id)+\" .status-label\");b&&b.click()}}},a.is_custom||a.require_button?wp.element.createElement(\"input\",{type:\"hidden\",name:\"_PPCH_custom_item[\".concat(a.id,\"]\"),value:a.status?\"yes\":\"no\"}):null,wp.element.createElement(\"div\",{className:\"status-icon dashicons \".concat(a.is_custom?a.status?\"dashicons-yes\":\"\":a.status?\"dashicons-yes\":\"dashicons-no\")}),wp.element.createElement(\"div\",{className:\"status-label\"},wp.element.createElement(\"span\",{className:\"req-label\",dangerouslySetInnerHTML:{__html:a.label}}),\"block\"===a.rule?wp.element.createElement(\"span\",{className:\"required\"},\"*\"):null,a.require_button?wp.element.createElement(\"div\",{className:\"requirement-button-task-wrap\"},wp.element.createElement(\"button\",{type:\"button\",className:\"button button-secondary pp-checklists-check-item\"},__(\"Check Now\",\"publishpress-checklists\"),wp.element.createElement(\"span\",{className:\"spinner\"})),wp.element.createElement(\"div\",{className:\"request-response\"})):null))}))),b?wp.element.createElement(\"em\",null,\"(*) \",i18n.required):null))):null}}]),b}(Component),ChecklistsTitle=function(){return wp.element.createElement(\"div\",{className:\"pp-checklists-toolbar-icon\"},\"Checklists \")};registerPlugin(\"publishpress-checklists-panel\",{render:PPChecklistsPanel,icon:wp.element.createElement(ChecklistsTitle,null)});\n\n//# sourceURL=webpack://publishpress-checklists/./modules/checklists/assets/js/gutenberg-panel.jsx?");

/***/ }),

/***/ "./node_modules/regenerator-runtime/runtime.js":
/*!*****************************************************!*\
  !*** ./node_modules/regenerator-runtime/runtime.js ***!
  \*****************************************************/
/***/ ((module) => {

eval("/**\n * Copyright (c) 2014-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nvar runtime = (function (exports) {\n  \"use strict\";\n\n  var Op = Object.prototype;\n  var hasOwn = Op.hasOwnProperty;\n  var undefined; // More compressible than void 0.\n  var $Symbol = typeof Symbol === \"function\" ? Symbol : {};\n  var iteratorSymbol = $Symbol.iterator || \"@@iterator\";\n  var asyncIteratorSymbol = $Symbol.asyncIterator || \"@@asyncIterator\";\n  var toStringTagSymbol = $Symbol.toStringTag || \"@@toStringTag\";\n\n  function wrap(innerFn, outerFn, self, tryLocsList) {\n    // If outerFn provided and outerFn.prototype is a Generator, then outerFn.prototype instanceof Generator.\n    var protoGenerator = outerFn && outerFn.prototype instanceof Generator ? outerFn : Generator;\n    var generator = Object.create(protoGenerator.prototype);\n    var context = new Context(tryLocsList || []);\n\n    // The ._invoke method unifies the implementations of the .next,\n    // .throw, and .return methods.\n    generator._invoke = makeInvokeMethod(innerFn, self, context);\n\n    return generator;\n  }\n  exports.wrap = wrap;\n\n  // Try/catch helper to minimize deoptimizations. Returns a completion\n  // record like context.tryEntries[i].completion. This interface could\n  // have been (and was previously) designed to take a closure to be\n  // invoked without arguments, but in all the cases we care about we\n  // already have an existing method we want to call, so there's no need\n  // to create a new function object. We can even get away with assuming\n  // the method takes exactly one argument, since that happens to be true\n  // in every case, so we don't have to touch the arguments object. The\n  // only additional allocation required is the completion record, which\n  // has a stable shape and so hopefully should be cheap to allocate.\n  function tryCatch(fn, obj, arg) {\n    try {\n      return { type: \"normal\", arg: fn.call(obj, arg) };\n    } catch (err) {\n      return { type: \"throw\", arg: err };\n    }\n  }\n\n  var GenStateSuspendedStart = \"suspendedStart\";\n  var GenStateSuspendedYield = \"suspendedYield\";\n  var GenStateExecuting = \"executing\";\n  var GenStateCompleted = \"completed\";\n\n  // Returning this object from the innerFn has the same effect as\n  // breaking out of the dispatch switch statement.\n  var ContinueSentinel = {};\n\n  // Dummy constructor functions that we use as the .constructor and\n  // .constructor.prototype properties for functions that return Generator\n  // objects. For full spec compliance, you may wish to configure your\n  // minifier not to mangle the names of these two functions.\n  function Generator() {}\n  function GeneratorFunction() {}\n  function GeneratorFunctionPrototype() {}\n\n  // This is a polyfill for %IteratorPrototype% for environments that\n  // don't natively support it.\n  var IteratorPrototype = {};\n  IteratorPrototype[iteratorSymbol] = function () {\n    return this;\n  };\n\n  var getProto = Object.getPrototypeOf;\n  var NativeIteratorPrototype = getProto && getProto(getProto(values([])));\n  if (NativeIteratorPrototype &&\n      NativeIteratorPrototype !== Op &&\n      hasOwn.call(NativeIteratorPrototype, iteratorSymbol)) {\n    // This environment has a native %IteratorPrototype%; use it instead\n    // of the polyfill.\n    IteratorPrototype = NativeIteratorPrototype;\n  }\n\n  var Gp = GeneratorFunctionPrototype.prototype =\n    Generator.prototype = Object.create(IteratorPrototype);\n  GeneratorFunction.prototype = Gp.constructor = GeneratorFunctionPrototype;\n  GeneratorFunctionPrototype.constructor = GeneratorFunction;\n  GeneratorFunctionPrototype[toStringTagSymbol] =\n    GeneratorFunction.displayName = \"GeneratorFunction\";\n\n  // Helper for defining the .next, .throw, and .return methods of the\n  // Iterator interface in terms of a single ._invoke method.\n  function defineIteratorMethods(prototype) {\n    [\"next\", \"throw\", \"return\"].forEach(function(method) {\n      prototype[method] = function(arg) {\n        return this._invoke(method, arg);\n      };\n    });\n  }\n\n  exports.isGeneratorFunction = function(genFun) {\n    var ctor = typeof genFun === \"function\" && genFun.constructor;\n    return ctor\n      ? ctor === GeneratorFunction ||\n        // For the native GeneratorFunction constructor, the best we can\n        // do is to check its .name property.\n        (ctor.displayName || ctor.name) === \"GeneratorFunction\"\n      : false;\n  };\n\n  exports.mark = function(genFun) {\n    if (Object.setPrototypeOf) {\n      Object.setPrototypeOf(genFun, GeneratorFunctionPrototype);\n    } else {\n      genFun.__proto__ = GeneratorFunctionPrototype;\n      if (!(toStringTagSymbol in genFun)) {\n        genFun[toStringTagSymbol] = \"GeneratorFunction\";\n      }\n    }\n    genFun.prototype = Object.create(Gp);\n    return genFun;\n  };\n\n  // Within the body of any async function, `await x` is transformed to\n  // `yield regeneratorRuntime.awrap(x)`, so that the runtime can test\n  // `hasOwn.call(value, \"__await\")` to determine if the yielded value is\n  // meant to be awaited.\n  exports.awrap = function(arg) {\n    return { __await: arg };\n  };\n\n  function AsyncIterator(generator) {\n    function invoke(method, arg, resolve, reject) {\n      var record = tryCatch(generator[method], generator, arg);\n      if (record.type === \"throw\") {\n        reject(record.arg);\n      } else {\n        var result = record.arg;\n        var value = result.value;\n        if (value &&\n            typeof value === \"object\" &&\n            hasOwn.call(value, \"__await\")) {\n          return Promise.resolve(value.__await).then(function(value) {\n            invoke(\"next\", value, resolve, reject);\n          }, function(err) {\n            invoke(\"throw\", err, resolve, reject);\n          });\n        }\n\n        return Promise.resolve(value).then(function(unwrapped) {\n          // When a yielded Promise is resolved, its final value becomes\n          // the .value of the Promise<{value,done}> result for the\n          // current iteration.\n          result.value = unwrapped;\n          resolve(result);\n        }, function(error) {\n          // If a rejected Promise was yielded, throw the rejection back\n          // into the async generator function so it can be handled there.\n          return invoke(\"throw\", error, resolve, reject);\n        });\n      }\n    }\n\n    var previousPromise;\n\n    function enqueue(method, arg) {\n      function callInvokeWithMethodAndArg() {\n        return new Promise(function(resolve, reject) {\n          invoke(method, arg, resolve, reject);\n        });\n      }\n\n      return previousPromise =\n        // If enqueue has been called before, then we want to wait until\n        // all previous Promises have been resolved before calling invoke,\n        // so that results are always delivered in the correct order. If\n        // enqueue has not been called before, then it is important to\n        // call invoke immediately, without waiting on a callback to fire,\n        // so that the async generator function has the opportunity to do\n        // any necessary setup in a predictable way. This predictability\n        // is why the Promise constructor synchronously invokes its\n        // executor callback, and why async functions synchronously\n        // execute code before the first await. Since we implement simple\n        // async functions in terms of async generators, it is especially\n        // important to get this right, even though it requires care.\n        previousPromise ? previousPromise.then(\n          callInvokeWithMethodAndArg,\n          // Avoid propagating failures to Promises returned by later\n          // invocations of the iterator.\n          callInvokeWithMethodAndArg\n        ) : callInvokeWithMethodAndArg();\n    }\n\n    // Define the unified helper method that is used to implement .next,\n    // .throw, and .return (see defineIteratorMethods).\n    this._invoke = enqueue;\n  }\n\n  defineIteratorMethods(AsyncIterator.prototype);\n  AsyncIterator.prototype[asyncIteratorSymbol] = function () {\n    return this;\n  };\n  exports.AsyncIterator = AsyncIterator;\n\n  // Note that simple async functions are implemented on top of\n  // AsyncIterator objects; they just return a Promise for the value of\n  // the final result produced by the iterator.\n  exports.async = function(innerFn, outerFn, self, tryLocsList) {\n    var iter = new AsyncIterator(\n      wrap(innerFn, outerFn, self, tryLocsList)\n    );\n\n    return exports.isGeneratorFunction(outerFn)\n      ? iter // If outerFn is a generator, return the full iterator.\n      : iter.next().then(function(result) {\n          return result.done ? result.value : iter.next();\n        });\n  };\n\n  function makeInvokeMethod(innerFn, self, context) {\n    var state = GenStateSuspendedStart;\n\n    return function invoke(method, arg) {\n      if (state === GenStateExecuting) {\n        throw new Error(\"Generator is already running\");\n      }\n\n      if (state === GenStateCompleted) {\n        if (method === \"throw\") {\n          throw arg;\n        }\n\n        // Be forgiving, per 25.3.3.3.3 of the spec:\n        // https://people.mozilla.org/~jorendorff/es6-draft.html#sec-generatorresume\n        return doneResult();\n      }\n\n      context.method = method;\n      context.arg = arg;\n\n      while (true) {\n        var delegate = context.delegate;\n        if (delegate) {\n          var delegateResult = maybeInvokeDelegate(delegate, context);\n          if (delegateResult) {\n            if (delegateResult === ContinueSentinel) continue;\n            return delegateResult;\n          }\n        }\n\n        if (context.method === \"next\") {\n          // Setting context._sent for legacy support of Babel's\n          // function.sent implementation.\n          context.sent = context._sent = context.arg;\n\n        } else if (context.method === \"throw\") {\n          if (state === GenStateSuspendedStart) {\n            state = GenStateCompleted;\n            throw context.arg;\n          }\n\n          context.dispatchException(context.arg);\n\n        } else if (context.method === \"return\") {\n          context.abrupt(\"return\", context.arg);\n        }\n\n        state = GenStateExecuting;\n\n        var record = tryCatch(innerFn, self, context);\n        if (record.type === \"normal\") {\n          // If an exception is thrown from innerFn, we leave state ===\n          // GenStateExecuting and loop back for another invocation.\n          state = context.done\n            ? GenStateCompleted\n            : GenStateSuspendedYield;\n\n          if (record.arg === ContinueSentinel) {\n            continue;\n          }\n\n          return {\n            value: record.arg,\n            done: context.done\n          };\n\n        } else if (record.type === \"throw\") {\n          state = GenStateCompleted;\n          // Dispatch the exception by looping back around to the\n          // context.dispatchException(context.arg) call above.\n          context.method = \"throw\";\n          context.arg = record.arg;\n        }\n      }\n    };\n  }\n\n  // Call delegate.iterator[context.method](context.arg) and handle the\n  // result, either by returning a { value, done } result from the\n  // delegate iterator, or by modifying context.method and context.arg,\n  // setting context.delegate to null, and returning the ContinueSentinel.\n  function maybeInvokeDelegate(delegate, context) {\n    var method = delegate.iterator[context.method];\n    if (method === undefined) {\n      // A .throw or .return when the delegate iterator has no .throw\n      // method always terminates the yield* loop.\n      context.delegate = null;\n\n      if (context.method === \"throw\") {\n        // Note: [\"return\"] must be used for ES3 parsing compatibility.\n        if (delegate.iterator[\"return\"]) {\n          // If the delegate iterator has a return method, give it a\n          // chance to clean up.\n          context.method = \"return\";\n          context.arg = undefined;\n          maybeInvokeDelegate(delegate, context);\n\n          if (context.method === \"throw\") {\n            // If maybeInvokeDelegate(context) changed context.method from\n            // \"return\" to \"throw\", let that override the TypeError below.\n            return ContinueSentinel;\n          }\n        }\n\n        context.method = \"throw\";\n        context.arg = new TypeError(\n          \"The iterator does not provide a 'throw' method\");\n      }\n\n      return ContinueSentinel;\n    }\n\n    var record = tryCatch(method, delegate.iterator, context.arg);\n\n    if (record.type === \"throw\") {\n      context.method = \"throw\";\n      context.arg = record.arg;\n      context.delegate = null;\n      return ContinueSentinel;\n    }\n\n    var info = record.arg;\n\n    if (! info) {\n      context.method = \"throw\";\n      context.arg = new TypeError(\"iterator result is not an object\");\n      context.delegate = null;\n      return ContinueSentinel;\n    }\n\n    if (info.done) {\n      // Assign the result of the finished delegate to the temporary\n      // variable specified by delegate.resultName (see delegateYield).\n      context[delegate.resultName] = info.value;\n\n      // Resume execution at the desired location (see delegateYield).\n      context.next = delegate.nextLoc;\n\n      // If context.method was \"throw\" but the delegate handled the\n      // exception, let the outer generator proceed normally. If\n      // context.method was \"next\", forget context.arg since it has been\n      // \"consumed\" by the delegate iterator. If context.method was\n      // \"return\", allow the original .return call to continue in the\n      // outer generator.\n      if (context.method !== \"return\") {\n        context.method = \"next\";\n        context.arg = undefined;\n      }\n\n    } else {\n      // Re-yield the result returned by the delegate method.\n      return info;\n    }\n\n    // The delegate iterator is finished, so forget it and continue with\n    // the outer generator.\n    context.delegate = null;\n    return ContinueSentinel;\n  }\n\n  // Define Generator.prototype.{next,throw,return} in terms of the\n  // unified ._invoke helper method.\n  defineIteratorMethods(Gp);\n\n  Gp[toStringTagSymbol] = \"Generator\";\n\n  // A Generator should always return itself as the iterator object when the\n  // @@iterator function is called on it. Some browsers' implementations of the\n  // iterator prototype chain incorrectly implement this, causing the Generator\n  // object to not be returned from this call. This ensures that doesn't happen.\n  // See https://github.com/facebook/regenerator/issues/274 for more details.\n  Gp[iteratorSymbol] = function() {\n    return this;\n  };\n\n  Gp.toString = function() {\n    return \"[object Generator]\";\n  };\n\n  function pushTryEntry(locs) {\n    var entry = { tryLoc: locs[0] };\n\n    if (1 in locs) {\n      entry.catchLoc = locs[1];\n    }\n\n    if (2 in locs) {\n      entry.finallyLoc = locs[2];\n      entry.afterLoc = locs[3];\n    }\n\n    this.tryEntries.push(entry);\n  }\n\n  function resetTryEntry(entry) {\n    var record = entry.completion || {};\n    record.type = \"normal\";\n    delete record.arg;\n    entry.completion = record;\n  }\n\n  function Context(tryLocsList) {\n    // The root entry object (effectively a try statement without a catch\n    // or a finally block) gives us a place to store values thrown from\n    // locations where there is no enclosing try statement.\n    this.tryEntries = [{ tryLoc: \"root\" }];\n    tryLocsList.forEach(pushTryEntry, this);\n    this.reset(true);\n  }\n\n  exports.keys = function(object) {\n    var keys = [];\n    for (var key in object) {\n      keys.push(key);\n    }\n    keys.reverse();\n\n    // Rather than returning an object with a next method, we keep\n    // things simple and return the next function itself.\n    return function next() {\n      while (keys.length) {\n        var key = keys.pop();\n        if (key in object) {\n          next.value = key;\n          next.done = false;\n          return next;\n        }\n      }\n\n      // To avoid creating an additional object, we just hang the .value\n      // and .done properties off the next function object itself. This\n      // also ensures that the minifier will not anonymize the function.\n      next.done = true;\n      return next;\n    };\n  };\n\n  function values(iterable) {\n    if (iterable) {\n      var iteratorMethod = iterable[iteratorSymbol];\n      if (iteratorMethod) {\n        return iteratorMethod.call(iterable);\n      }\n\n      if (typeof iterable.next === \"function\") {\n        return iterable;\n      }\n\n      if (!isNaN(iterable.length)) {\n        var i = -1, next = function next() {\n          while (++i < iterable.length) {\n            if (hasOwn.call(iterable, i)) {\n              next.value = iterable[i];\n              next.done = false;\n              return next;\n            }\n          }\n\n          next.value = undefined;\n          next.done = true;\n\n          return next;\n        };\n\n        return next.next = next;\n      }\n    }\n\n    // Return an iterator with no values.\n    return { next: doneResult };\n  }\n  exports.values = values;\n\n  function doneResult() {\n    return { value: undefined, done: true };\n  }\n\n  Context.prototype = {\n    constructor: Context,\n\n    reset: function(skipTempReset) {\n      this.prev = 0;\n      this.next = 0;\n      // Resetting context._sent for legacy support of Babel's\n      // function.sent implementation.\n      this.sent = this._sent = undefined;\n      this.done = false;\n      this.delegate = null;\n\n      this.method = \"next\";\n      this.arg = undefined;\n\n      this.tryEntries.forEach(resetTryEntry);\n\n      if (!skipTempReset) {\n        for (var name in this) {\n          // Not sure about the optimal order of these conditions:\n          if (name.charAt(0) === \"t\" &&\n              hasOwn.call(this, name) &&\n              !isNaN(+name.slice(1))) {\n            this[name] = undefined;\n          }\n        }\n      }\n    },\n\n    stop: function() {\n      this.done = true;\n\n      var rootEntry = this.tryEntries[0];\n      var rootRecord = rootEntry.completion;\n      if (rootRecord.type === \"throw\") {\n        throw rootRecord.arg;\n      }\n\n      return this.rval;\n    },\n\n    dispatchException: function(exception) {\n      if (this.done) {\n        throw exception;\n      }\n\n      var context = this;\n      function handle(loc, caught) {\n        record.type = \"throw\";\n        record.arg = exception;\n        context.next = loc;\n\n        if (caught) {\n          // If the dispatched exception was caught by a catch block,\n          // then let that catch block handle the exception normally.\n          context.method = \"next\";\n          context.arg = undefined;\n        }\n\n        return !! caught;\n      }\n\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        var record = entry.completion;\n\n        if (entry.tryLoc === \"root\") {\n          // Exception thrown outside of any try block that could handle\n          // it, so set the completion value of the entire function to\n          // throw the exception.\n          return handle(\"end\");\n        }\n\n        if (entry.tryLoc <= this.prev) {\n          var hasCatch = hasOwn.call(entry, \"catchLoc\");\n          var hasFinally = hasOwn.call(entry, \"finallyLoc\");\n\n          if (hasCatch && hasFinally) {\n            if (this.prev < entry.catchLoc) {\n              return handle(entry.catchLoc, true);\n            } else if (this.prev < entry.finallyLoc) {\n              return handle(entry.finallyLoc);\n            }\n\n          } else if (hasCatch) {\n            if (this.prev < entry.catchLoc) {\n              return handle(entry.catchLoc, true);\n            }\n\n          } else if (hasFinally) {\n            if (this.prev < entry.finallyLoc) {\n              return handle(entry.finallyLoc);\n            }\n\n          } else {\n            throw new Error(\"try statement without catch or finally\");\n          }\n        }\n      }\n    },\n\n    abrupt: function(type, arg) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.tryLoc <= this.prev &&\n            hasOwn.call(entry, \"finallyLoc\") &&\n            this.prev < entry.finallyLoc) {\n          var finallyEntry = entry;\n          break;\n        }\n      }\n\n      if (finallyEntry &&\n          (type === \"break\" ||\n           type === \"continue\") &&\n          finallyEntry.tryLoc <= arg &&\n          arg <= finallyEntry.finallyLoc) {\n        // Ignore the finally entry if control is not jumping to a\n        // location outside the try/catch block.\n        finallyEntry = null;\n      }\n\n      var record = finallyEntry ? finallyEntry.completion : {};\n      record.type = type;\n      record.arg = arg;\n\n      if (finallyEntry) {\n        this.method = \"next\";\n        this.next = finallyEntry.finallyLoc;\n        return ContinueSentinel;\n      }\n\n      return this.complete(record);\n    },\n\n    complete: function(record, afterLoc) {\n      if (record.type === \"throw\") {\n        throw record.arg;\n      }\n\n      if (record.type === \"break\" ||\n          record.type === \"continue\") {\n        this.next = record.arg;\n      } else if (record.type === \"return\") {\n        this.rval = this.arg = record.arg;\n        this.method = \"return\";\n        this.next = \"end\";\n      } else if (record.type === \"normal\" && afterLoc) {\n        this.next = afterLoc;\n      }\n\n      return ContinueSentinel;\n    },\n\n    finish: function(finallyLoc) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.finallyLoc === finallyLoc) {\n          this.complete(entry.completion, entry.afterLoc);\n          resetTryEntry(entry);\n          return ContinueSentinel;\n        }\n      }\n    },\n\n    \"catch\": function(tryLoc) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.tryLoc === tryLoc) {\n          var record = entry.completion;\n          if (record.type === \"throw\") {\n            var thrown = record.arg;\n            resetTryEntry(entry);\n          }\n          return thrown;\n        }\n      }\n\n      // The context.catch method must only be called with a location\n      // argument that corresponds to a known catch block.\n      throw new Error(\"illegal catch attempt\");\n    },\n\n    delegateYield: function(iterable, resultName, nextLoc) {\n      this.delegate = {\n        iterator: values(iterable),\n        resultName: resultName,\n        nextLoc: nextLoc\n      };\n\n      if (this.method === \"next\") {\n        // Deliberately forget the last sent value so that we don't\n        // accidentally pass it on to the delegate.\n        this.arg = undefined;\n      }\n\n      return ContinueSentinel;\n    }\n  };\n\n  // Regardless of whether this script is executing as a CommonJS module\n  // or not, return the runtime object so that we can declare the variable\n  // regeneratorRuntime in the outer scope, which allows this module to be\n  // injected easily by `bin/regenerator --include-runtime script.js`.\n  return exports;\n\n}(\n  // If this script is executing as a CommonJS module, use module.exports\n  // as the regeneratorRuntime namespace. Otherwise create a new empty\n  // object. Either way, the resulting object will be used to initialize\n  // the regeneratorRuntime variable at the top of this file.\n   true ? module.exports : 0\n));\n\ntry {\n  regeneratorRuntime = runtime;\n} catch (accidentalStrictMode) {\n  // This module should not be running in strict mode, so the above\n  // assignment should always work unless something is misconfigured. Just\n  // in case runtime.js accidentally runs in strict mode, we can escape\n  // strict mode using a global Function call. This could conceivably fail\n  // if a Content Security Policy forbids using Function, but in that case\n  // the proper solution is to fix the accidental strict mode problem. If\n  // you've misconfigured your bundler to force strict mode and applied a\n  // CSP to forbid Function, and you're not willing to fix either of those\n  // problems, please detail your unique predicament in a GitHub issue.\n  Function(\"r\", \"regeneratorRuntime = r\")(runtime);\n}\n\n\n//# sourceURL=webpack://publishpress-checklists/./node_modules/regenerator-runtime/runtime.js?");

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval devtool is used.
/******/ 	var __webpack_exports__ = __webpack_require__("./modules/checklists/assets/js/gutenberg-panel.jsx");
/******/ 	
/******/ })()
;