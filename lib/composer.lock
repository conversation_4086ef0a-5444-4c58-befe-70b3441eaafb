{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "46b3419986a417f08600f04ad7f55202", "packages": [{"name": "alledia/edd-sl-plugin-updater", "version": "v1.6.23", "source": {"type": "git", "url": "https://github.com/publishpress/EDD-SL-Plugin-Updater.git", "reference": "38253d04ac8875a88f62a3ebdca8034473f77418"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/publishpress/EDD-SL-Plugin-Updater/zipball/38253d04ac8875a88f62a3ebdca8034473f77418", "reference": "38253d04ac8875a88f62a3ebdca8034473f77418", "shasum": ""}, "require": {"ext-json": "*", "php": ">=5.6.20"}, "type": "library", "autoload": {"files": ["EDD_SL_Plugin_Updater.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "EasyDigitalDownloads", "email": "<EMAIL>"}, {"name": "PublishPress", "email": "<EMAIL>"}, {"name": "Alledia", "email": "<EMAIL>"}], "description": "EDD Updates <PERSON><PERSON> for WordPress Plugins", "keywords": ["edd", "updates", "wordpress"], "support": {"issues": "https://github.com/publishpress/EDD-SL-Plugin-Updater/issues", "source": "https://github.com/publishpress/EDD-SL-Plugin-Updater/tree/v1.6.23"}, "time": "2023-08-30T19:32:16+00:00"}, {"name": "publishpress/instance-protection", "version": "v1.0.3", "source": {"type": "git", "url": "https://github.com/publishpress/library-instance-protection.git", "reference": "6a4e7038c95fac43264b1d61a5cdae2b1e3cc4ee"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/publishpress/library-instance-protection/zipball/6a4e7038c95fac43264b1d61a5cdae2b1e3cc4ee", "reference": "6a4e7038c95fac43264b1d61a5cdae2b1e3cc4ee", "shasum": ""}, "require": {"php": ">=5.6.20"}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["GPL-3.0-or-later"], "authors": [{"name": "PublishPress", "email": "<EMAIL>"}], "description": "Library for protecting WordPress plugins to run twice.", "homepage": "http://publishpress.com/", "keywords": ["wordpress plugin"], "support": {"issues": "https://github.com/publishpress/library-instance-protection/issues", "source": "https://github.com/publishpress/library-instance-protection/tree/v1.0.3"}, "time": "2022-10-28T16:14:03+00:00"}, {"name": "publishpress/pimple-pimple", "version": "********", "source": {"type": "git", "url": "https://github.com/publishpress/library-pimple-pimple.git", "reference": "f2784f4b1ccaf195b373a46a74cf3742e7c9f826"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/publishpress/library-pimple-pimple/zipball/f2784f4b1ccaf195b373a46a74cf3742e7c9f826", "reference": "f2784f4b1ccaf195b373a46a74cf3742e7c9f826", "shasum": ""}, "require": {"php": ">=7.2.5", "publishpress/psr-container": "^********"}, "require-dev": {"brianhenryie/strauss": "^0.14.0", "codeception/module-asserts": "^1.0", "codeception/module-cli": "^1.0", "codeception/module-db": "^1.0", "codeception/module-filesystem": "^1.0", "codeception/module-phpbrowser": "^1.0", "codeception/module-webdriver": "^1.0", "codeception/util-universalframework": "^1.0", "lucatume/wp-browser": "^3.1", "pimple/pimple": "3.5.0", "publishpress/codeception-extension-extended-copier": "^1.0", "publishpress/version-loader-generator": "^1.0"}, "type": "library", "extra": {"strauss": {"packages": ["pimple/pimple", "psr/container"], "include_author": "true", "classmap_output": "true", "classmap_prefix": "PublishPress_", "constant_prefix": "PUBLISHPRESS_", "namespace_prefix": "PublishPress\\", "target_directory": "lib", "exclude_from_copy": {"packages": [], "namespaces": [], "file_patterns": []}, "override_autoload": [], "delete_vendor_files": true, "exclude_from_prefix": {"packages": [], "namespaces": [], "file_patterns": []}, "delete_vendor_packages": true, "namespace_replacement_patterns": []}, "generator": {"lib-class-test": "class_exists('PublishPress\\Pimple\\Container')", "action-register-priority": "-200", "action-initialize-priority": "-185"}}, "autoload": {"files": ["lib/include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "PublishPress", "email": "<EMAIL>"}], "description": "<PERSON><PERSON>, a simple Dependency Injection Container, prefixed for PublishPress", "homepage": "https://github.com/silexphp/Pimple/", "keywords": ["container", "dependency injection", "publishpress"], "support": {"issues": "https://github.com/publishpress/library-pimple-pimple/issues", "source": "https://github.com/publishpress/library-pimple-pimple/tree/********"}, "time": "2023-05-30T18:41:10+00:00"}, {"name": "publishpress/psr-container", "version": "********", "source": {"type": "git", "url": "https://github.com/publishpress/library-psr-container.git", "reference": "4ccd2cb058e7b93e96186791ca25870a02e35c5d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/publishpress/library-psr-container/zipball/4ccd2cb058e7b93e96186791ca25870a02e35c5d", "reference": "4ccd2cb058e7b93e96186791ca25870a02e35c5d", "shasum": ""}, "require": {"php": ">=7.2.5"}, "require-dev": {"brianhenryie/strauss": "^0.14.0", "codeception/module-asserts": "^1.0", "codeception/module-cli": "^1.0", "codeception/module-db": "^1.0", "codeception/module-filesystem": "^1.0", "codeception/module-phpbrowser": "^1.0", "codeception/module-webdriver": "^1.0", "codeception/util-universalframework": "^1.0", "lucatume/wp-browser": "^3.1", "psr/container": "2.0.1", "publishpress/codeception-extension-extended-copier": "^1.0", "publishpress/version-loader-generator": "^1.0"}, "type": "library", "extra": {"strauss": {"packages": ["psr/container"], "include_author": "true", "classmap_output": "true", "classmap_prefix": "PublishPress_", "constant_prefix": "PUBLISHPRESS_", "namespace_prefix": "PublishPress\\", "target_directory": "lib", "exclude_from_copy": {"packages": [], "namespaces": [], "file_patterns": []}, "override_autoload": [], "delete_vendor_files": true, "exclude_from_prefix": {"packages": [], "namespaces": [], "file_patterns": []}, "delete_vendor_packages": true, "namespace_replacement_patterns": []}, "generator": {"lib-class-test": "interface_exists('PublishPress\\Psr\\Container\\ContainerInterface')", "action-register-priority": "-200", "action-initialize-priority": "-190"}}, "autoload": {"files": ["lib/include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}, {"name": "PublishPress", "email": "<EMAIL>"}], "description": "Common Container Interface (PHP FIG PSR-11), prefixed for PublishPress plugins", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr", "publishpress"], "support": {"issues": "https://github.com/publishpress/library-psr-container/issues", "source": "https://github.com/publishpress/library-psr-container/tree/********"}, "time": "2023-05-30T18:17:45+00:00"}, {"name": "publishpress/publishpress-checklists", "version": "dev-development", "source": {"type": "git", "url": "https://github.com/publishpress/PublishPress-Checklists.git", "reference": "c005d5b8b690d0aa910acc2ec23d80aef4ef55f8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/publishpress/PublishPress-Checklists/zipball/c005d5b8b690d0aa910acc2ec23d80aef4ef55f8", "reference": "c005d5b8b690d0aa910acc2ec23d80aef4ef55f8", "shasum": ""}, "require": {"php": ">=7.2.5"}, "require-dev": {"codeception/module-asserts": "^1.2", "codeception/module-db": "^1.0", "codeception/module-phpbrowser": "^1.0", "codeception/util-universalframework": "^1.0", "overtrue/phplint": "^2.4"}, "default-branch": true, "type": "wordpress-plugin", "extra": {"plugin-slug": "publishpress-checklists", "plugin-name": "publishpress-checklists", "plugin-folder": "publishpress-checklists"}, "scripts": {"build": ["ppbuild build"], "build:dir": ["ppbuild build-dir"], "build:clean": ["ppbuild clean"], "get-version": ["ppbuild version"], "check:longpath": ["longpath ."], "pre-autoload-dump": ["composer dumpautoload --working-dir=./lib"], "pre-update-cmd": ["composer update --working-dir=./lib"], "pre-install-cmd": ["composer install --working-dir=./lib"]}, "license": ["GPL-2"], "authors": [{"name": "PublishPress", "email": "<EMAIL>"}], "support": {"source": "https://github.com/publishpress/PublishPress-Checklists/tree/development", "issues": "https://github.com/publishpress/PublishPress-Checklists/issues"}, "time": "2025-06-04T16:09:49+00:00"}, {"name": "publishpress/wordpress-banners", "version": "v1.3.1", "source": {"type": "git", "url": "https://github.com/publishpress/library-wordpress-banners.git", "reference": "bcb5f9f00973d53e2db027f9a05c059f18743fae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/publishpress/library-wordpress-banners/zipball/bcb5f9f00973d53e2db027f9a05c059f18743fae", "reference": "bcb5f9f00973d53e2db027f9a05c059f18743fae", "shasum": ""}, "require": {"php": ">=5.6.20"}, "type": "library", "autoload": {"files": ["BannersMain.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-3.0-or-later"], "authors": [{"name": "PublishPress", "email": "<EMAIL>"}], "description": "Display banners in admin pages of PublishPress Plugins.", "homepage": "http://publishpress.com/", "support": {"issues": "https://github.com/publishpress/library-wordpress-banners/issues", "source": "https://github.com/publishpress/library-wordpress-banners/tree/v1.3.1"}, "time": "2022-08-15T18:07:23+00:00"}, {"name": "publishpress/wordpress-edd-license", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/publishpress/library-wordpress-edd-license.git", "reference": "ea53f02c0537bf4054bf3fcb183f93a08c0b2ed3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/publishpress/library-wordpress-edd-license/zipball/ea53f02c0537bf4054bf3fcb183f93a08c0b2ed3", "reference": "ea53f02c0537bf4054bf3fcb183f93a08c0b2ed3", "shasum": ""}, "require": {"alledia/edd-sl-plugin-updater": "^1.6", "ext-json": "*", "php": ">=5.6.20", "publishpress/pimple-pimple": "~3.2"}, "require-dev": {"codeception/domain-assert": "^1.0", "codeception/module-asserts": "^1.2", "codeception/module-cli": "^1.0", "codeception/module-db": "^1.0", "codeception/module-filesystem": "^1.0", "codeception/module-phpbrowser": "^1.0", "codeception/module-webdriver": "^1.0", "codeception/util-universalframework": "^1.0", "lucatume/wp-browser": "^2.4", "publishpress/version-loader-generator": "^1.0"}, "type": "library", "extra": {"generator": {"src-dir": "src", "lib-class-test": "class_exists('PublishPress\\WordPressEDDLicense\\License')", "action-register-priority": "-190", "action-initialize-priority": "-150"}}, "autoload": {"files": ["src/include.php"], "psr-4": {"PublishPress\\WordPressEDDLicense\\": "src/classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "PublishPress", "email": "<EMAIL>"}, {"name": "Alledia", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "and<PERSON>@grudtner.me", "homepage": "http://anderson.grudtner.me", "role": "Lead Developer"}], "description": "EDD license integration library for WordPress plugins", "keywords": ["edd", "license", "wordpress"], "support": {"source": "https://github.com/publishpress/library-wordpress-edd-license/tree/3.0.1"}, "time": "2023-05-30T19:13:23+00:00"}, {"name": "publishpress/wordpress-reviews", "version": "v1.1.20", "source": {"type": "git", "url": "https://github.com/publishpress/library-wordpress-reviews.git", "reference": "6d0b687a66439721b0432ef1320fd818cd56309f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/publishpress/library-wordpress-reviews/zipball/6d0b687a66439721b0432ef1320fd818cd56309f", "reference": "6d0b687a66439721b0432ef1320fd818cd56309f", "shasum": ""}, "require": {"php": ">=5.6.20"}, "require-dev": {"codeception/module-asserts": "^1.0", "codeception/module-cli": "^1.0", "codeception/module-db": "^1.0", "codeception/module-filesystem": "^1.0", "codeception/module-phpbrowser": "^1.0", "codeception/module-rest": "^1.3", "codeception/module-webdriver": "^1.0", "codeception/util-universalframework": "^1.0", "lucatume/wp-browser": "^3", "overtrue/phplint": "^2.1", "phpmd/phpmd": "^2.8", "publishpress/publishpress-plugin-builder": "^1.2", "squizlabs/php_codesniffer": "^3.5", "wp-cli/i18n-command": "^2.2", "wp-cli/wp-cli": "^2.5"}, "type": "library", "autoload": {"files": ["ReviewsController.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-3.0-or-later"], "authors": [{"name": "PublishPress", "email": "<EMAIL>"}, {"name": "<PERSON>", "homepage": "https://github.com/danieliser/WP-Product-In-Dash-Review-Requests"}], "description": "Library for showing a five-star review banner.", "homepage": "http://publishpress.com/", "keywords": ["review", "reviews", "wordpress plugin"], "support": {"issues": "https://github.com/publishpress/library-wordpress-reviews/issues", "source": "https://github.com/publishpress/library-wordpress-reviews/tree/v1.1.20"}, "time": "2023-10-06T17:22:31+00:00"}, {"name": "publishpress/wordpress-version-notices", "version": "2.1.3", "source": {"type": "git", "url": "https://github.com/publishpress/library-wordpress-version-notices.git", "reference": "8e54558d2427a0f93174ccbc1d02c1ba7e2abc8d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/publishpress/library-wordpress-version-notices/zipball/8e54558d2427a0f93174ccbc1d02c1ba7e2abc8d", "reference": "8e54558d2427a0f93174ccbc1d02c1ba7e2abc8d", "shasum": ""}, "require": {"php": ">=7.2.5", "publishpress/pimple-pimple": "^*******", "publishpress/psr-container": "^*******"}, "require-dev": {"codeception/module-asserts": "^1.0", "codeception/module-cli": "^1.0", "codeception/module-db": "^1.0", "codeception/module-filesystem": "^1.0", "codeception/module-phpbrowser": "^1.0", "codeception/module-rest": "^1.3", "codeception/module-webdriver": "^1.0", "codeception/util-universalframework": "^1.0", "composer/composer": "2.2.12", "lucatume/wp-browser": "^3", "overtrue/phplint": "^2.1", "publishpress/version-loader-generator": "^1.0"}, "type": "library", "extra": {"generator": {"src-dir": "src", "lib-class-test": "class_exists('PublishPress\\WordpressVersionNotices\\ServicesProvider')", "action-register-priority": "-190", "action-initialize-priority": "-150"}}, "autoload": {"files": ["src/include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-3.0-or-later"], "authors": [{"name": "PublishPress", "email": "<EMAIL>", "homepage": "https://publishpress.com", "role": "Developer"}], "description": "Library for displaying version notices for Pro plugins in WordPress.", "support": {"issues": "https://github.com/publishpress/library-wordpress-version-notices/issues", "source": "https://github.com/publishpress/library-wordpress-version-notices/tree/2.1.3"}, "time": "2023-10-11T15:04:44+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": {"publishpress/publishpress-checklists": 20}, "prefer-stable": true, "prefer-lowest": false, "platform": {"php": ">=7.2.5"}, "platform-dev": [], "plugin-api-version": "2.3.0"}