<?php
/**
 * @package     WPPF
 * <AUTHOR> <<EMAIL>>
 * @copyright   copyright (C) 2019 PublishPress. All rights reserved.
 * @license     GPLv2 or later
 * @since       1.0.0
 */

namespace WPPF\WP\Filesystem\Storage;

class Local implements StorageInterface
{
    /**
     * @inheritDoc
     */
    public function exists($path)
    {
        global $wp_filesystem;

        return $wp_filesystem->exists($path);
    }

    /**
     * @deprecated 2.10.0 Use exists() method instead.
     */
    public function has($path)
    {
        return $this->exists($path);
    }
}
