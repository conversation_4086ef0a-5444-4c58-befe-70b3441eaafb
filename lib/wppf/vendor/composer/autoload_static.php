<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

use Closure;

class ComposerStaticInit96782fba721f8943dc136f22f30a7600
{
    public static $files = array(
        '72526e082f63211293b0aebb9afaada1' => __DIR__ . '/../..' . '/defines.php',
    );

    public static $prefixLengthsPsr4 = array(
        'W' =>
            array(
                'WPPF\\' => 5,
            ),
    );

    public static $prefixDirsPsr4 = array(
        'WPPF\\' =>
            array(
                0 => __DIR__ . '/../..' . '/src',
            ),
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return Closure::bind(
            function () use ($loader) {
                $loader->prefixLengthsPsr4 = ComposerStaticInit96782fba721f8943dc136f22f30a7600::$prefixLengthsPsr4;
                $loader->prefixDirsPsr4    = ComposerStaticInit96782fba721f8943dc136f22f30a7600::$prefixDirsPsr4;
            },
            null,
            ClassLoader::class
        );
    }
}
