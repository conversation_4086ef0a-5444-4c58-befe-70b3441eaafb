#!/usr/bin/env php
<?php
/**
 * Display the IP address of the host machine.
 *
 * Usage: getip [-h]
 * Options:
 *  -h, --help    Display this help text.
 *
 * @version 1.1.1
 */

/**
 * Display usage information
 */
function print_usage()
{
    echo "Usage: php script.php [-h]\n";
    echo "Get the IP address on Linux or macOS.\n";
    echo "\n";
    echo "Options:\n";
    echo "  -h, --help    Display this help text.\n";
}

/**
 * Function to check if we are online.
 */
function is_online(): bool {
    return (bool) @fsockopen("www.google.com", 80);
}

/**
 * Return the IP address based on the operating system.
 *
 * @return string|void
 */
function get_docker_host_ip()
{
    if (! is_online()) {
        return '127.0.0.1';
    }

    if (php_uname('s') === 'Linux') {
        $output = shell_exec("ip addr show | grep 'docker' | grep 'inet ' | grep -v '127.0.0.1' | awk '{print \$2}' | cut -f1 -d'/'");
    } elseif (php_uname('s') === 'Darwin') {
        $output = shell_exec("ifconfig | grep 'inet ' | grep -v '127.0.0.1' | awk '{print \$2}'");
    } else {
        echo "Error: Unsupported operating system\n";
        exit(1);
    }

    return trim($output);
}

// Check if the user requests help
if (in_array('-h', $argv) || in_array('--help', $argv)) {
    print_usage();
    exit(0);
}

$docker_host_ip = get_docker_host_ip();

// Check if the IP was not detected
if (empty($docker_host_ip)) {
    echo "Error: Unable to determine the host IP\n";
    exit(1);
}

echo $docker_host_ip;
