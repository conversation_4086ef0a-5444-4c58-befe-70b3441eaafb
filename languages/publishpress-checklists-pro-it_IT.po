msgid ""
msgstr ""
"Project-Id-Version: PublishPress Checklists Pro\n"
"POT-Creation-Date: 2024-10-15 22:29+0700\n"
"PO-Revision-Date: 2024-10-17 21:57+0000\n"
"Last-Translator: \n"
"Language-Team: Italian\n"
"Language: it_IT\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Loco https://localise.biz/\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-Flags-xgettext: --add-comments=translators:\n"
"X-Poedit-WPHeader: publishpress-checklists-pro.php\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2;getText;displayText;getTextN;"
"getTextAndEscHtml\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.min.js\n"
"X-Poedit-SearchPathExcluded-1: vendor\n"
"X-Poedit-SearchPathExcluded-2: lib\n"
"Report-Msgid-Bugs-To: \n"
"X-Loco-Version: 2.6.11; wp-6.6.2"

#: src/modules/advanced-custom-fields/lib/Requirement/Base_counter.php:52
#, php-format
msgid "%%s character in %s field"
msgstr "Carattere %%s nel campo %s"

#: src/modules/advanced-custom-fields/lib/Requirement/Base_counter.php:53
#, php-format
msgid "%%s characters in %s field"
msgstr "%%s caratteri nel campo %s"

#: src/modules/image-count/lib/Requirement/ImageCount.php:53
msgid "%d image in content"
msgstr "%d immagine nel contenuto"

#: src/modules/image-count/lib/Requirement/ImageCount.php:54
msgid "%d images in content"
msgstr "%d immagini nel contenuto"

#: src/modules/advanced-custom-fields/lib/Requirement/Base_counter.php:46
#, php-format
msgid "%s"
msgstr "%s"

#: src/modules/woocommerce/lib/Requirement/Crosssell.php:86
#: src/modules/woocommerce/lib/Requirement/Crosssell.php:87
#, php-format
msgid "%s products for \"Cross-sells\""
msgstr "%s prodotti per \"Cross-sells\""

#: src/modules/woocommerce/lib/Requirement/Upsell.php:101
#: src/modules/woocommerce/lib/Requirement/Upsell.php:102
#, php-format
msgid "%s products for \"Upsells\""
msgstr "%s prodotti per \"Upsells\""

#: src/modules/woocommerce/lib/Requirement/Crosssell.php:88
#, php-format
msgid "%s to %s products for \"Cross-sells\""
msgstr "%s a %s prodotti per \"Cross-sells\""

#: src/modules/woocommerce/lib/Requirement/Upsell.php:103
#, php-format
msgid "%s to %s products for \"Upsells\""
msgstr "%s a %s prodotti per \"Upsells\""

#: src/modules/woocommerce/lib/Requirement/Discount.php:78
#: src/modules/woocommerce/lib/Requirement/Discount.php:79
#, php-format
msgid "%s%% discount for the \"Sale price\""
msgstr "%s%% sconto per il \"Prezzo in offerta\""

#: src/modules/woocommerce/lib/Requirement/Discount.php:80
#, php-format
msgid "%s%% to %s%% discount for the \"Sale price\""
msgstr "%s%% a %s%% sconto per il \"Prezzo in offerta\""

#: src/modules/advanced-custom-fields/advanced-custom-fields.php:320
msgid "ACF"
msgstr "ACF"

#: src/modules/advanced-custom-fields/advanced-custom-fields.php:108
msgid "Advanced Custom Fields Support"
msgstr "Supporto avanzato per campi personalizzati"

#: src/modules/woocommerce/lib/Requirement/Backorder.php:168
msgid "Allow"
msgstr "Pemetti"

#: src/modules/woocommerce/lib/Requirement/Backorder.php:66
msgid "Allow backorders"
msgstr "Permetti ordini per prodotti in riordinazione"

#: src/modules/woocommerce/lib/Requirement/Backorder.php:67
msgid "Allow backorders, but notify"
msgstr "Permetti ordini per prodotti in riordinazione, ma avvisa"

#: src/modules/woocommerce/lib/Requirement/Backorder.php:175
msgid "Allow, but notify customer"
msgstr "Permetti, ma avvisa l'utente"

#: src/modules/advanced-custom-fields/lib/Requirement/Base_counter.php:54
#, php-format
msgid "Between %%s and %%s characters in %s field"
msgstr "Tra %%s e %%s caratteri nel campo %s"

#: src/modules/image-count/lib/Requirement/ImageCount.php:55
msgid "Between %d and %d images in content"
msgstr "Tra %d e %d immagini nel contenuto"

#: src/modules/woocommerce/lib/Requirement/Backorder.php:65
msgid "Check the \"Allow backorders?\" box"
msgstr ""
"Seleziona la casella \"Permetti ordini per prodotti in riordinazione?\""

#: src/modules/woocommerce/lib/Requirement/Downloadable.php:43
#: src/modules/woocommerce/lib/Requirement/Downloadable.php:44
msgid "Check the \"Downloadable\" box"
msgstr "Seleziona la casella \"Scaricabile\""

#: src/modules/woocommerce/lib/Requirement/ManageStock.php:43
#: src/modules/woocommerce/lib/Requirement/ManageStock.php:44
msgid "Check the \"Manage stock?\" box"
msgstr "Seleziona la casella \"Gestire il magazzino?\""

#: src/modules/woocommerce/lib/Requirement/SoldIndividually.php:43
#: src/modules/woocommerce/lib/Requirement/SoldIndividually.php:44
msgid "Check the \"Sold individually\" box"
msgstr "Seleziona la casella \"Venduto singolarmente\""

#: src/modules/woocommerce/lib/Requirement/VirtualCheckbox.php:42
#: src/modules/woocommerce/lib/Requirement/VirtualCheckbox.php:43
msgid "Check the \"Virtual\" box"
msgstr "Seleziona la casella \"Virtuale\""

#: src/PluginServiceProvider.php:61
msgid "Checklists Pro"
msgstr "Checklists Pro"

#: src/modules/advanced-custom-fields/advanced-custom-fields.php:112
#: src/modules/advanced-custom-fields/advanced-custom-fields.php:116
msgid "Define tasks that must be complete before a product is published"
msgstr ""
"Definire le attività che devono essere completate prima che un prodotto "
"venga pubblicato"

#: src/modules/woocommerce/woocommerce.php:116
#: src/modules/woocommerce/woocommerce.php:120
msgid ""
"Define tasks that must be complete before a WooCommerce product is published"
msgstr ""
"Definisci le azioni che devono essere completate prima che un prodotto "
"WooCommerce venga pubblicato"

#: src/modules/featured-image-size/featured-image-size.php:83
#: src/modules/featured-image-size/featured-image-size.php:87
msgid "Define tasks that verify the featured image size"
msgstr ""
"Definire le operazioni che verificano le dimensioni dell'immagine in evidenza"

#: src/modules/image-count/image-count.php:82
#: src/modules/image-count/image-count.php:86
msgid "Define tasks that verify total images in content"
msgstr "Definire le attività che verificano le immagini totali nel contenuto"

#: src/modules/woocommerce/lib/Requirement/Discount.php:72
msgid "Discount for the \"Sale price\""
msgstr "Sconto per il \"Prezzo in offerta\""

#: src/modules/prosettings/prosettings.php:207
msgid "Display PublishPress branding in the admin:"
msgstr "Mostra il marchio PublishPress nell'amministrazione:"

#: src/modules/woocommerce/lib/Requirement/Backorder.php:161
msgid "Do not allow"
msgstr "Non permettere"

#: src/modules/woocommerce/lib/Requirement/Backorder.php:68
msgid "Do not allow backorders"
msgstr "Non permettere ordini per prodotti in riordinazione"

#: src/modules/woocommerce/lib/Requirement/RegularPrice.php:42
#: src/modules/woocommerce/lib/Requirement/RegularPrice.php:43
msgid "Enter a \"Regular price\""
msgstr "Immetti un \"Prezzo di listino\""

#: src/modules/woocommerce/lib/Requirement/SalePrice.php:43
#: src/modules/woocommerce/lib/Requirement/SalePrice.php:44
msgid "Enter a \"Sale price\""
msgstr "Immetti un \"Prezzo in offerta\""

#: src/modules/woocommerce/lib/Requirement/Sku.php:43
#: src/modules/woocommerce/lib/Requirement/Sku.php:44
msgid "Enter a \"SKU\""
msgstr "Immetti un \"COD\""

#: src/modules/featured-image-size/lib/Requirement/FeaturedImageHeight.php:51
msgid "Featured image height"
msgstr "Altezza dell'immagine in evidenza"

#: src/modules/featured-image-size/lib/Requirement/FeaturedImageHeight.php:70
#, php-format
msgid "Featured image height between %spx and %spx"
msgstr "Altezza dell'immagine in evidenza tra %spx e %spx"

#: src/modules/featured-image-size/lib/Requirement/FeaturedImageHeight.php:68
#: src/modules/featured-image-size/lib/Requirement/FeaturedImageHeight.php:69
#, php-format
msgid "Featured image height of %spx"
msgstr "Altezza dell'immagine in evidenza di %spx"

#: src/modules/featured-image-size/featured-image-size.php:79
msgid "Featured Image Size Support"
msgstr "Compatibilità con la dimensione dell'immagine in evidenza"

#: src/modules/featured-image-size/lib/Requirement/FeaturedImageWidth.php:51
msgid "Featured image width"
msgstr "Larghezza dell'immagine in evidenza"

#: src/modules/featured-image-size/lib/Requirement/FeaturedImageWidth.php:70
#, php-format
msgid "Featured image width between %spx and %spx"
msgstr "Larghezza dell'immagine in evidenza tra %spx e %spx"

#: src/modules/featured-image-size/lib/Requirement/FeaturedImageWidth.php:68
#: src/modules/featured-image-size/lib/Requirement/FeaturedImageWidth.php:69
#, php-format
msgid "Featured image width of %spx"
msgstr "Larghezza dell'immagine in evidenza di %spx"

#: src/modules/advanced-custom-fields/lib/Requirement/Base_counter.php:310
msgid "Got value from ACF Configuration."
msgstr "Ho ottenuto valore dalla configurazione ACF."

#. Author URI of the plugin
msgid "https://publishpress.com"
msgstr "https://publishpress.com"

#. URI of the plugin
msgid "https://publishpress.com/"
msgstr "https://publishpress.com/"

#: src/modules/image-count/image-count.php:78
msgid "Image Count"
msgstr "Conteggio delle immagini"

#: src/modules/prosettings/prosettings.php:199
msgid "License key:"
msgstr "Chiave di licenza:"

#: src/modules/woocommerce/lib/Requirement/Discount.php:76
#: src/modules/woocommerce/lib/Requirement/Discount.php:77
#, php-format
msgid "Max %s%% discount for the \"Sale price\""
msgstr "Max %s%% sconto per il \"Prezzo in offerta\""

#: src/modules/featured-image-size/lib/Requirement/FeaturedImageHeight.php:60
#: src/modules/featured-image-size/lib/Requirement/FeaturedImageHeight.php:64
#, php-format
msgid "Maximum %spx for the featured image height"
msgstr "Massima %spx per l'altezza dell'immagine in evidenza"

#: src/modules/featured-image-size/lib/Requirement/FeaturedImageWidth.php:60
#: src/modules/featured-image-size/lib/Requirement/FeaturedImageWidth.php:64
#, php-format
msgid "Maximum %spx for the featured image width"
msgstr "Massima %spx per la larghezza dell'immagine in evidenza"

#: src/modules/advanced-custom-fields/lib/Requirement/Base_counter.php:50
#, php-format
msgid "Maximum of %%s character in %s field"
msgstr "Massimo di %%s caratteri nel campo %s"

#: src/modules/advanced-custom-fields/lib/Requirement/Base_counter.php:51
#, php-format
msgid "Maximum of %%s characters in %s field"
msgstr "Massimo di %%s caratteri nel campo %s"

#: src/modules/image-count/lib/Requirement/ImageCount.php:51
msgid "Maximum of %d image in content"
msgstr "Massimo di %d immagine nel contenuto"

#: src/modules/image-count/lib/Requirement/ImageCount.php:52
msgid "Maximum of %d images in content"
msgstr "Massimo di %d immagini nel contenuto"

#: src/modules/woocommerce/lib/Requirement/Crosssell.php:78
#: src/modules/woocommerce/lib/Requirement/Crosssell.php:82
#, php-format
msgid "Maximum of %s products for \"Cross-sells\""
msgstr "Massimo di %s prodotti per \"Cross-sells\""

#: src/modules/woocommerce/lib/Requirement/Upsell.php:93
#: src/modules/woocommerce/lib/Requirement/Upsell.php:97
#, php-format
msgid "Maximum of %s products for \"Upsells\""
msgstr "Massimo di %s prodotti per \"Upsells\""

#: src/modules/woocommerce/lib/Requirement/Crosssell.php:69
#: src/modules/woocommerce/lib/Requirement/Upsell.php:84
#, php-format
msgid "Min %s Max %s"
msgstr "Min %s Max %s"

#: src/modules/woocommerce/lib/Requirement/Discount.php:74
#: src/modules/woocommerce/lib/Requirement/Discount.php:75
#, php-format
msgid "Min %s%% discount for the \"Sale price\""
msgstr "Min %s%% sconto per il \"Prezzo in offerta\""

#: src/modules/woocommerce/lib/Requirement/Discount.php:73
#, php-format
msgid "Min %s%% Max %s%%"
msgstr "Min %s%% Max %s%%"

#: src/modules/featured-image-size/lib/Requirement/FeaturedImageHeight.php:52
#: src/modules/featured-image-size/lib/Requirement/FeaturedImageHeight.php:56
#, php-format
msgid "Minimum %spx for the featured image height"
msgstr "Minima %spx per l'altezza dell'immagine in evidenza"

#: src/modules/featured-image-size/lib/Requirement/FeaturedImageWidth.php:52
#: src/modules/featured-image-size/lib/Requirement/FeaturedImageWidth.php:56
#, php-format
msgid "Minimum %spx for the featured image width"
msgstr "Minima %spx per la larghezza dell'immagine in evidenza"

#: src/modules/advanced-custom-fields/lib/Requirement/Base_counter.php:48
#, php-format
msgid "Minimum of %%s character in %s field"
msgstr "Minimo di %%s caratteri nel campo %s"

#: src/modules/advanced-custom-fields/lib/Requirement/Base_counter.php:49
#, php-format
msgid "Minimum of %%s characters in %s field"
msgstr "Minimo di %%s caratteri nel campo %s"

#: src/modules/image-count/lib/Requirement/ImageCount.php:49
msgid "Minimum of %d image in content"
msgstr "Minimo di %d immagine nel contenuto"

#: src/modules/image-count/lib/Requirement/ImageCount.php:50
msgid "Minimum of %d images in content"
msgstr "Minimo di %d immagini nel contenuto"

#: src/modules/woocommerce/lib/Requirement/Crosssell.php:70
#: src/modules/woocommerce/lib/Requirement/Crosssell.php:74
#, php-format
msgid "Minimum of %s products for \"Cross-sells\""
msgstr "Minimo di %s prodotti per \"Cross-sells\""

#: src/modules/woocommerce/lib/Requirement/Upsell.php:85
#: src/modules/woocommerce/lib/Requirement/Upsell.php:89
#, php-format
msgid "Minimum of %s products for \"Upsells\""
msgstr "Minimo di %s prodotti per \"Upsells\""

#: src/modules/advanced-custom-fields/lib/Requirement/Base_counter.php:47
#, php-format
msgid "Number of characters in %s field"
msgstr "Numero di caratteri nel campo %s"

#: src/modules/image-count/lib/Requirement/ImageCount.php:47
#: src/modules/image-count/lib/Requirement/ImageCount.php:48
msgid "Number of images in content"
msgstr "Numero di immagini nel contenuto"

#: src/modules/prosettings/prosettings.php:118
msgid "Pro Settings"
msgstr "Impostazioni Pro"

#: src/modules/woocommerce/lib/Requirement/Image.php:43
#: src/modules/woocommerce/lib/Requirement/Image.php:44
msgid "Product image"
msgstr "Immagine del prodotto"

#. Author of the plugin
msgid "PublishPress"
msgstr "PublishPress"

#. Name of the plugin
msgid "PublishPress Checklists Pro"
msgstr "PublishPress Checklists Pro"

#: src/modules/woocommerce/lib/Requirement/SalePriceScheduled.php:43
#: src/modules/woocommerce/lib/Requirement/SalePriceScheduled.php:44
msgid "Schedule the \"Sale price\""
msgstr "Programma il \"Prezzo in offerta\""

#: src/modules/woocommerce/lib/Requirement/Crosssell.php:65
msgid "Select some products for \"Cross-sells\""
msgstr "Seleziona alcuni prodotti per \"Cross-sells\""

#: src/modules/woocommerce/lib/Requirement/Upsell.php:80
msgid "Select some products for \"Upsells\""
msgstr "Seleziona alcuni prodotti per \"Up-sells\""

#. Description of the plugin
msgid ""
"With PublishPress Checklists, you can choose publishing requirements for "
"your content."
msgstr ""
"Con le checklist di PublishPress puoi scegliere i requisiti di pubblicazione "
"per i tuoi contenuti."

#: src/modules/woocommerce/woocommerce.php:292
msgid "WooCommerce"
msgstr "Commercio elettronico"

#: src/modules/woocommerce/woocommerce.php:112
msgid "WooCommerce Support"
msgstr "Compatibilità con WooCommerce"
