msgid ""
msgstr ""
"Project-Id-Version: PublishPress Checklists Pro\n"
"POT-Creation-Date: 2024-10-24 23:22-0300\n"
"PO-Revision-Date: 2024-11-14 14:33+0000\n"
"Last-Translator: \n"
"Language-Team: Portuguese (Brazil)\n"
"Language: pt_BR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Loco https://localise.biz/\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-Flags-xgettext: --add-comments=translators:\n"
"X-Poedit-WPHeader: publishpress-checklists-pro.php\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.min.js\n"
"Plural-Forms: nplurals=2; plural=n != 1;"

#: src/modules/advanced-custom-fields/lib/Requirement/Base_counter.php:52
#, php-format
msgid "%%s character in %s field"
msgstr "%%s caractere no campo %s"

#: src/modules/advanced-custom-fields/lib/Requirement/Base_counter.php:53
#, php-format
msgid "%%s characters in %s field"
msgstr "%%s caracteres no campo %s"

#: src/modules/image-count/lib/Requirement/ImageCount.php:53
msgid "%d image in content"
msgstr "%d imagem no conteúdo"

#: src/modules/image-count/lib/Requirement/ImageCount.php:54
msgid "%d images in content"
msgstr "%d imagens no conteúdo"

#: src/modules/advanced-custom-fields/lib/Requirement/Base_counter.php:46
#, php-format
msgid "%s"
msgstr "%s"

#: src/modules/woocommerce/lib/Requirement/Crosssell.php:86
#: src/modules/woocommerce/lib/Requirement/Crosssell.php:87
#, php-format
msgid "%s products for \"Cross-sells\""
msgstr "%s produtos para \"Vendas cruzadas (cross-sells)\""

#: src/modules/woocommerce/lib/Requirement/Upsell.php:101
#: src/modules/woocommerce/lib/Requirement/Upsell.php:102
#, php-format
msgid "%s products for \"Upsells\""
msgstr "%s produtos para \"Vendas adicionais (upsells)\""

#: src/modules/woocommerce/lib/Requirement/Crosssell.php:88
#, php-format
msgid "%s to %s products for \"Cross-sells\""
msgstr "%s a %s produtos para \"Vendas cruzadas (cross-sells)\""

#: src/modules/woocommerce/lib/Requirement/Upsell.php:103
#, php-format
msgid "%s to %s products for \"Upsells\""
msgstr "%s a %s produtos para \"Vendas adicionais (upsells)\""

#: src/modules/woocommerce/lib/Requirement/Discount.php:78
#: src/modules/woocommerce/lib/Requirement/Discount.php:79
#, php-format
msgid "%s%% discount for the \"Sale price\""
msgstr "%s%% de desconto para o \"Preço de venda\""

#: src/modules/woocommerce/lib/Requirement/Discount.php:80
#, php-format
msgid "%s%% to %s%% discount for the \"Sale price\""
msgstr "%s%% a %s%% de desconto para o \"Preço de venda\""

#: src/modules/advanced-custom-fields/advanced-custom-fields.php:320
msgid "ACF"
msgstr "ACF"

#: src/modules/advanced-custom-fields/advanced-custom-fields.php:108
msgid "Advanced Custom Fields Support"
msgstr "Suporte para campos personalizados avançados (ACF)"

#: src/modules/woocommerce/lib/Requirement/Backorder.php:168
msgid "Allow"
msgstr "Permitir"

#: src/modules/woocommerce/lib/Requirement/Backorder.php:66
msgid "Allow backorders"
msgstr "Permitir pedidos em atraso"

#: src/modules/woocommerce/lib/Requirement/Backorder.php:67
msgid "Allow backorders, but notify"
msgstr "Permitir pedidos em atraso, mas notificar"

#: src/modules/woocommerce/lib/Requirement/Backorder.php:175
msgid "Allow, but notify customer"
msgstr "Permitir, mas notificar o cliente"

#: src/modules/advanced-custom-fields/lib/Requirement/Base_counter.php:54
#, php-format
msgid "Between %%s and %%s characters in %s field"
msgstr "Entre %%s e %%s caracteres no campo %s"

#: src/modules/image-count/lib/Requirement/ImageCount.php:55
msgid "Between %d and %d images in content"
msgstr "Entre %d e %d imagens no conteúdo"

#: src/modules/woocommerce/lib/Requirement/Backorder.php:65
msgid "Check the \"Allow backorders?\" box"
msgstr "Marcar a caixa \"Permitir pedidos em atraso?\""

#: src/modules/woocommerce/lib/Requirement/Downloadable.php:43
#: src/modules/woocommerce/lib/Requirement/Downloadable.php:44
msgid "Check the \"Downloadable\" box"
msgstr "Marcar a caixa \"Baixável\""

#: src/modules/woocommerce/lib/Requirement/ManageStock.php:43
#: src/modules/woocommerce/lib/Requirement/ManageStock.php:44
msgid "Check the \"Manage stock?\" box"
msgstr "Marcar a caixa \"Gerenciar estoque?\""

#: src/modules/woocommerce/lib/Requirement/SoldIndividually.php:43
#: src/modules/woocommerce/lib/Requirement/SoldIndividually.php:44
msgid "Check the \"Sold individually\" box"
msgstr "Marcar a caixa \"Vendido individualmente\""

#: src/modules/woocommerce/lib/Requirement/VirtualCheckbox.php:42
#: src/modules/woocommerce/lib/Requirement/VirtualCheckbox.php:43
msgid "Check the \"Virtual\" box"
msgstr "Marcar a caixa “Virtual”"

#: src/PluginServiceProvider.php:61
msgid "Checklists Pro"
msgstr "Checklists Pro"

#: src/modules/advanced-custom-fields/advanced-custom-fields.php:112
#: src/modules/advanced-custom-fields/advanced-custom-fields.php:116
msgid "Define tasks that must be complete before a product is published"
msgstr ""
"Defina as tarefas que precisam ser concluídas antes da publicação de um "
"produto"

#: src/modules/woocommerce/woocommerce.php:116
#: src/modules/woocommerce/woocommerce.php:120
msgid ""
"Define tasks that must be complete before a WooCommerce product is published"
msgstr ""
"Defina as tarefas que precisam ser concluídas antes que um produto do "
"WooCommerce seja publicado"

#: src/modules/featured-image-size/featured-image-size.php:83
#: src/modules/featured-image-size/featured-image-size.php:87
msgid "Define tasks that verify the featured image size"
msgstr "Defina tarefas que verifiquem o tamanho da imagem destacada"

#: src/modules/image-count/image-count.php:82
#: src/modules/image-count/image-count.php:86
msgid "Define tasks that verify total images in content"
msgstr "Defina tarefas que verifiquem o total de imagens no conteúdo"

#: src/modules/woocommerce/lib/Requirement/Discount.php:72
msgid "Discount for the \"Sale price\""
msgstr "Desconto para o \"Preço de venda\""

#: src/modules/prosettings/prosettings.php:207
msgid "Display PublishPress branding in the admin:"
msgstr "Exibir a marca do PublishPress no painel administrativo:"

#: src/modules/woocommerce/lib/Requirement/Backorder.php:161
msgid "Do not allow"
msgstr "Não permitir"

#: src/modules/woocommerce/lib/Requirement/Backorder.php:68
msgid "Do not allow backorders"
msgstr "Não permitir pedidos em atraso"

#: src/modules/woocommerce/lib/Requirement/RegularPrice.php:42
#: src/modules/woocommerce/lib/Requirement/RegularPrice.php:43
msgid "Enter a \"Regular price\""
msgstr "Digitar um \"Preço normal\""

#: src/modules/woocommerce/lib/Requirement/SalePrice.php:43
#: src/modules/woocommerce/lib/Requirement/SalePrice.php:44
msgid "Enter a \"Sale price\""
msgstr "Digitar um \"Preço de oferta\"."

#: src/modules/woocommerce/lib/Requirement/Sku.php:43
#: src/modules/woocommerce/lib/Requirement/Sku.php:44
msgid "Enter a \"SKU\""
msgstr "Digitar uma \"SKU\""

#: src/modules/featured-image-size/lib/Requirement/FeaturedImageHeight.php:51
msgid "Featured image height"
msgstr "Altura da imagem destacada"

#: src/modules/featured-image-size/lib/Requirement/FeaturedImageHeight.php:70
#, php-format
msgid "Featured image height between %spx and %spx"
msgstr "Altura da imagem destacada, entre %spx e %spx"

#: src/modules/featured-image-size/lib/Requirement/FeaturedImageHeight.php:68
#: src/modules/featured-image-size/lib/Requirement/FeaturedImageHeight.php:69
#, php-format
msgid "Featured image height of %spx"
msgstr "Altura da imagem destacada de %spx"

#: src/modules/featured-image-size/featured-image-size.php:79
msgid "Featured Image Size Support"
msgstr "Suporte ao tamanho da imagem destacada"

#: src/modules/featured-image-size/lib/Requirement/FeaturedImageWidth.php:51
msgid "Featured image width"
msgstr "Largura da imagem destacada"

#: src/modules/featured-image-size/lib/Requirement/FeaturedImageWidth.php:70
#, php-format
msgid "Featured image width between %spx and %spx"
msgstr "Largura da imagem destacada, entre %spx e %spx"

#: src/modules/featured-image-size/lib/Requirement/FeaturedImageWidth.php:68
#: src/modules/featured-image-size/lib/Requirement/FeaturedImageWidth.php:69
#, php-format
msgid "Featured image width of %spx"
msgstr "Largura da imagem destacada de %spx"

#: src/modules/advanced-custom-fields/lib/Requirement/Base_counter.php:310
msgid "Got value from ACF Configuration."
msgstr "Recebeu o valor da configuração do ACF."

#. Author URI of the plugin
msgid "https://publishpress.com"
msgstr "https://publishpress.com"

#. URI of the plugin
msgid "https://publishpress.com/"
msgstr "https://publishpress.com/"

#: src/modules/image-count/image-count.php:78
msgid "Image Count"
msgstr "Contagem de imagens"

#: src/modules/prosettings/prosettings.php:199
msgid "License key:"
msgstr "Chave de licença:"

#: src/modules/woocommerce/lib/Requirement/Discount.php:76
#: src/modules/woocommerce/lib/Requirement/Discount.php:77
#, php-format
msgid "Max %s%% discount for the \"Sale price\""
msgstr "Desconto máximo de %s%% para o \"Preço de venda\""

#: src/modules/featured-image-size/lib/Requirement/FeaturedImageHeight.php:60
#: src/modules/featured-image-size/lib/Requirement/FeaturedImageHeight.php:64
#, php-format
msgid "Maximum %spx for the featured image height"
msgstr "Máximo de %spx para a altura da imagem destacada"

#: src/modules/featured-image-size/lib/Requirement/FeaturedImageWidth.php:60
#: src/modules/featured-image-size/lib/Requirement/FeaturedImageWidth.php:64
#, php-format
msgid "Maximum %spx for the featured image width"
msgstr "Máximo de %spx para a largura da imagem destacada"

#: src/modules/advanced-custom-fields/lib/Requirement/Base_counter.php:50
#, php-format
msgid "Maximum of %%s character in %s field"
msgstr "Máximo de %%s caractere no campo %s"

#: src/modules/advanced-custom-fields/lib/Requirement/Base_counter.php:51
#, php-format
msgid "Maximum of %%s characters in %s field"
msgstr "Máximo de %%s caracteres no campo %s"

#: src/modules/image-count/lib/Requirement/ImageCount.php:51
msgid "Maximum of %d image in content"
msgstr "Máximo de %d imagem no conteúdo"

#: src/modules/image-count/lib/Requirement/ImageCount.php:52
msgid "Maximum of %d images in content"
msgstr "Máximo de %d imagens no conteúdo"

#: src/modules/woocommerce/lib/Requirement/Crosssell.php:78
#: src/modules/woocommerce/lib/Requirement/Crosssell.php:82
#, php-format
msgid "Maximum of %s products for \"Cross-sells\""
msgstr "Máximo de %s produtos para \"Vendas cruzadas (cross-sells)\""

#: src/modules/woocommerce/lib/Requirement/Upsell.php:93
#: src/modules/woocommerce/lib/Requirement/Upsell.php:97
#, php-format
msgid "Maximum of %s products for \"Upsells\""
msgstr "Máximo de %s produtos para \"Vendas adicionais (upsells)\""

#: src/modules/woocommerce/lib/Requirement/Crosssell.php:69
#: src/modules/woocommerce/lib/Requirement/Upsell.php:84
#, php-format
msgid "Min %s Max %s"
msgstr "Mínimo %s Máximo %s"

#: src/modules/woocommerce/lib/Requirement/Discount.php:74
#: src/modules/woocommerce/lib/Requirement/Discount.php:75
#, php-format
msgid "Min %s%% discount for the \"Sale price\""
msgstr "Desconto mínimo de %s%% para o \"Preço de venda\""

#: src/modules/woocommerce/lib/Requirement/Discount.php:73
#, php-format
msgid "Min %s%% Max %s%%"
msgstr "Mínimo %s%% Máximo %s%%"

#: src/modules/featured-image-size/lib/Requirement/FeaturedImageHeight.php:52
#: src/modules/featured-image-size/lib/Requirement/FeaturedImageHeight.php:56
#, php-format
msgid "Minimum %spx for the featured image height"
msgstr "Mínimo de %spx para a altura da imagem destacada"

#: src/modules/featured-image-size/lib/Requirement/FeaturedImageWidth.php:52
#: src/modules/featured-image-size/lib/Requirement/FeaturedImageWidth.php:56
#, php-format
msgid "Minimum %spx for the featured image width"
msgstr "Mínimo de %spx para a largura da imagem destacada"

#: src/modules/advanced-custom-fields/lib/Requirement/Base_counter.php:48
#, php-format
msgid "Minimum of %%s character in %s field"
msgstr "Mínimo de %%s caractere no campo %s"

#: src/modules/advanced-custom-fields/lib/Requirement/Base_counter.php:49
#, php-format
msgid "Minimum of %%s characters in %s field"
msgstr "Mínimo de %%s caracteres no campo %s"

#: src/modules/image-count/lib/Requirement/ImageCount.php:49
msgid "Minimum of %d image in content"
msgstr "Mínimo de %d imagem no conteúdo"

#: src/modules/image-count/lib/Requirement/ImageCount.php:50
msgid "Minimum of %d images in content"
msgstr "Mínimo de %d imagens no conteúdo"

#: src/modules/woocommerce/lib/Requirement/Crosssell.php:70
#: src/modules/woocommerce/lib/Requirement/Crosssell.php:74
#, php-format
msgid "Minimum of %s products for \"Cross-sells\""
msgstr "Mínimo de %s produtos para \"Vendas cruzadas (cross-sells)\""

#: src/modules/woocommerce/lib/Requirement/Upsell.php:85
#: src/modules/woocommerce/lib/Requirement/Upsell.php:89
#, php-format
msgid "Minimum of %s products for \"Upsells\""
msgstr "Mínimo de %s produtos para \"Vendas adicionais (upsells)\""

#: src/modules/advanced-custom-fields/lib/Requirement/Base_counter.php:47
#, php-format
msgid "Number of characters in %s field"
msgstr "Número de caracteres no campo %s"

#: src/modules/image-count/lib/Requirement/ImageCount.php:47
#: src/modules/image-count/lib/Requirement/ImageCount.php:48
msgid "Number of images in content"
msgstr "Número de imagens no conteúdo"

#: src/modules/prosettings/prosettings.php:118
msgid "Pro Settings"
msgstr "Configurações do Pro"

#: src/modules/woocommerce/lib/Requirement/Image.php:43
#: src/modules/woocommerce/lib/Requirement/Image.php:44
msgid "Product image"
msgstr "Imagem do produto"

#. Author of the plugin
msgid "PublishPress"
msgstr "PublishPress"

#. Name of the plugin
msgid "PublishPress Checklists Pro"
msgstr "PublishPress Checklists Pro"

#: src/modules/woocommerce/lib/Requirement/SalePriceScheduled.php:43
#: src/modules/woocommerce/lib/Requirement/SalePriceScheduled.php:44
msgid "Schedule the \"Sale price\""
msgstr "Agendar o \"Preço de oferta\""

#: src/modules/woocommerce/lib/Requirement/Crosssell.php:65
msgid "Select some products for \"Cross-sells\""
msgstr "Selecione alguns produtos para “Vendas cruzadas (cross-sells)”"

#: src/modules/woocommerce/lib/Requirement/Upsell.php:80
msgid "Select some products for \"Upsells\""
msgstr "Selecione alguns produtos para \"Vendas adicionais (upsells)\""

#. Description of the plugin
msgid ""
"With PublishPress Checklists, you can choose publishing requirements for "
"your content."
msgstr ""
"Com o PublishPress Checklists, você pode escolher os requisitos de "
"publicação para o seu conteúdo."

#: src/modules/woocommerce/woocommerce.php:292
msgid "WooCommerce"
msgstr "WooCommerce"

#: src/modules/woocommerce/woocommerce.php:112
msgid "WooCommerce Support"
msgstr "Suporte ao WooCommerce"
