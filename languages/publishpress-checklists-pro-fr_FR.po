msgid ""
msgstr ""
"Project-Id-Version: PublishPress Checklists Pro\n"
"POT-Creation-Date: 2024-10-17 20:27+0700\n"
"PO-Revision-Date: 2024-10-17 21:58+0000\n"
"Last-Translator: \n"
"Language-Team: French (France)\n"
"Language: fr_FR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"
"X-Generator: Loco https://localise.biz/\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-Flags-xgettext: --add-comments=translators:\n"
"X-Poedit-WPHeader: publishpress-checklists-pro.php\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2;getText;displayText;getTextN;"
"getTextAndEscHtml\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.min.js\n"
"X-Poedit-SearchPathExcluded-1: vendor\n"
"X-Poedit-SearchPathExcluded-2: lib\n"

#: src/modules/advanced-custom-fields/lib/Requirement/Base_counter.php:52
#, php-format
msgid "%%s character in %s field"
msgstr "%%s caractère dans le champ %s"

#: src/modules/advanced-custom-fields/lib/Requirement/Base_counter.php:53
#, php-format
msgid "%%s characters in %s field"
msgstr "%%s caractères dans le champ %s"

#: src/modules/image-count/lib/Requirement/ImageCount.php:53
msgid "%d image in content"
msgstr "%d image dans le contenu"

#: src/modules/image-count/lib/Requirement/ImageCount.php:54
msgid "%d images in content"
msgstr "%d images dans le contenu"

#: src/modules/advanced-custom-fields/lib/Requirement/Base_counter.php:46
#, php-format
msgid "%s"
msgstr "%s"

#: src/modules/woocommerce/lib/Requirement/Crosssell.php:86
#: src/modules/woocommerce/lib/Requirement/Crosssell.php:87
#, php-format
msgid "%s products for \"Cross-sells\""
msgstr "%s produits pour les « Ventes croisées »"

#: src/modules/woocommerce/lib/Requirement/Upsell.php:101
#: src/modules/woocommerce/lib/Requirement/Upsell.php:102
#, php-format
msgid "%s products for \"Upsells\""
msgstr "%s produits pour les « Produits suggerés »"

#: src/modules/woocommerce/lib/Requirement/Crosssell.php:88
#, php-format
msgid "%s to %s products for \"Cross-sells\""
msgstr "%s et %s produits pour les « Ventes croisées »"

#: src/modules/woocommerce/lib/Requirement/Upsell.php:103
#, php-format
msgid "%s to %s products for \"Upsells\""
msgstr "%s et %s produits pour les « Produits suggerés »"

#: src/modules/woocommerce/lib/Requirement/Discount.php:78
#: src/modules/woocommerce/lib/Requirement/Discount.php:79
#, php-format
msgid "%s%% discount for the \"Sale price\""
msgstr "%s%% remise pour le « Tarif promo »"

#: src/modules/woocommerce/lib/Requirement/Discount.php:80
#, php-format
msgid "%s%% to %s%% discount for the \"Sale price\""
msgstr "%s%% et %s%% remise pour le « Tarif promo »"

#: src/modules/advanced-custom-fields/advanced-custom-fields.php:320
msgid "ACF"
msgstr "ACF"

#: src/modules/advanced-custom-fields/advanced-custom-fields.php:108
msgid "Advanced Custom Fields Support"
msgstr "Prise en charge avancée des champs personnalisés"

#: src/modules/woocommerce/lib/Requirement/Backorder.php:168
msgid "Allow"
msgstr "Autoriser"

#: src/modules/woocommerce/lib/Requirement/Backorder.php:66
msgid "Allow backorders"
msgstr "Autoriser les commandes sur un produit non disponible"

#: src/modules/woocommerce/lib/Requirement/Backorder.php:67
msgid "Allow backorders, but notify"
msgstr ""
"Autoriser les commandes sur un produit non disponible, mais il faut avertir"

#: src/modules/woocommerce/lib/Requirement/Backorder.php:175
msgid "Allow, but notify customer"
msgstr "Autoriser, mais avertir le client"

#: src/modules/advanced-custom-fields/lib/Requirement/Base_counter.php:54
#, php-format
msgid "Between %%s and %%s characters in %s field"
msgstr "Entre %%s et %%s caractères dans le champ %s"

#: src/modules/image-count/lib/Requirement/ImageCount.php:55
msgid "Between %d and %d images in content"
msgstr "Entre %d et %d images dans le contenu"

#: src/modules/woocommerce/lib/Requirement/Backorder.php:65
msgid "Check the \"Allow backorders?\" box"
msgstr ""
"Cochez la case « Autoriser les commandes sur un produit non disponible ? »"

#: src/modules/woocommerce/lib/Requirement/Downloadable.php:43
#: src/modules/woocommerce/lib/Requirement/Downloadable.php:44
msgid "Check the \"Downloadable\" box"
msgstr "Cochez la case « Téléchargeable »"

#: src/modules/woocommerce/lib/Requirement/ManageStock.php:43
#: src/modules/woocommerce/lib/Requirement/ManageStock.php:44
msgid "Check the \"Manage stock?\" box"
msgstr "Cochez la case « Gérer le stock ? »"

#: src/modules/woocommerce/lib/Requirement/SoldIndividually.php:43
#: src/modules/woocommerce/lib/Requirement/SoldIndividually.php:44
msgid "Check the \"Sold individually\" box"
msgstr "Cochez la case « Vendre individuellement »"

#: src/modules/woocommerce/lib/Requirement/VirtualCheckbox.php:42
#: src/modules/woocommerce/lib/Requirement/VirtualCheckbox.php:43
msgid "Check the \"Virtual\" box"
msgstr "Cochez la case « Virtuel »"

#: src/PluginServiceProvider.php:61
msgid "Checklists Pro"
msgstr "Checklists Pro"

#: src/modules/advanced-custom-fields/advanced-custom-fields.php:112
#: src/modules/advanced-custom-fields/advanced-custom-fields.php:116
msgid "Define tasks that must be complete before a product is published"
msgstr ""
"Définir les tâches qui doivent être terminées avant la publication d'un "
"produit"

#: src/modules/woocommerce/woocommerce.php:116
#: src/modules/woocommerce/woocommerce.php:120
msgid ""
"Define tasks that must be complete before a WooCommerce product is published"
msgstr ""
"Définissez les tâches à accomplir avant la publication d’un produit "
"WooCommerce"

#: src/modules/featured-image-size/featured-image-size.php:83
#: src/modules/featured-image-size/featured-image-size.php:87
msgid "Define tasks that verify the featured image size"
msgstr "Définir les tâches qui vérifient la taille de l’image mise en avant"

#: src/modules/image-count/image-count.php:82
#: src/modules/image-count/image-count.php:86
msgid "Define tasks that verify total images in content"
msgstr ""
"Définir des tâches qui vérifient le nombre total d'images dans le contenu"

#: src/modules/woocommerce/lib/Requirement/Discount.php:72
msgid "Discount for the \"Sale price\""
msgstr "Remise pour le « Tarif promo »"

#: src/modules/prosettings/prosettings.php:207
msgid "Display PublishPress branding in the admin:"
msgstr "Afficher la marque PublishPress dans l’administration :"

#: src/modules/woocommerce/lib/Requirement/Backorder.php:161
msgid "Do not allow"
msgstr "Ne pas autoriser"

#: src/modules/woocommerce/lib/Requirement/Backorder.php:68
msgid "Do not allow backorders"
msgstr "Ne pas autoriser les commandes sur un produit non disponible"

#: src/modules/woocommerce/lib/Requirement/RegularPrice.php:42
#: src/modules/woocommerce/lib/Requirement/RegularPrice.php:43
msgid "Enter a \"Regular price\""
msgstr "Saisissez le « Tarif régulier »"

#: src/modules/woocommerce/lib/Requirement/SalePrice.php:43
#: src/modules/woocommerce/lib/Requirement/SalePrice.php:44
msgid "Enter a \"Sale price\""
msgstr "Saisissez le « Tarif promo »"

#: src/modules/woocommerce/lib/Requirement/Sku.php:43
#: src/modules/woocommerce/lib/Requirement/Sku.php:44
msgid "Enter a \"SKU\""
msgstr "Saisissez le « UGS »"

#: src/modules/featured-image-size/lib/Requirement/FeaturedImageHeight.php:51
msgid "Featured image height"
msgstr "Hauteur de l’image mise en avant"

#: src/modules/featured-image-size/lib/Requirement/FeaturedImageHeight.php:70
#, php-format
msgid "Featured image height between %spx and %spx"
msgstr "Hauteur de l’image mise en avant entre %spx et %spx"

#: src/modules/featured-image-size/lib/Requirement/FeaturedImageHeight.php:68
#: src/modules/featured-image-size/lib/Requirement/FeaturedImageHeight.php:69
#, php-format
msgid "Featured image height of %spx"
msgstr "Hauteur de l’image mise en avant de %spx"

#: src/modules/featured-image-size/featured-image-size.php:79
msgid "Featured Image Size Support"
msgstr "Prise en charge de la taille de l’image mise en avant"

#: src/modules/featured-image-size/lib/Requirement/FeaturedImageWidth.php:51
msgid "Featured image width"
msgstr "Largeur de l’image mise en avant"

#: src/modules/featured-image-size/lib/Requirement/FeaturedImageWidth.php:70
#, php-format
msgid "Featured image width between %spx and %spx"
msgstr "Largeur de l’image mise en avant entre %spx et %spx"

#: src/modules/featured-image-size/lib/Requirement/FeaturedImageWidth.php:68
#: src/modules/featured-image-size/lib/Requirement/FeaturedImageWidth.php:69
#, php-format
msgid "Featured image width of %spx"
msgstr "Largeur de l’image mise en avant de %spx"

#: src/modules/advanced-custom-fields/lib/Requirement/Base_counter.php:310
msgid "Got value from ACF Configuration."
msgstr "J'ai obtenu de la valeur grâce à la configuration ACF."

#. Author URI of the plugin
msgid "https://publishpress.com"
msgstr "https://publishpress.com"

#. URI of the plugin
msgid "https://publishpress.com/"
msgstr "https://publishpress.com/"

#: src/modules/image-count/image-count.php:78
msgid "Image Count"
msgstr "Nombre d'images"

#: src/modules/prosettings/prosettings.php:199
msgid "License key:"
msgstr "Clé de licence :"

#: src/modules/woocommerce/lib/Requirement/Discount.php:76
#: src/modules/woocommerce/lib/Requirement/Discount.php:77
#, php-format
msgid "Max %s%% discount for the \"Sale price\""
msgstr "Max %s%% remise pour le « Tarif promo »"

#: src/modules/featured-image-size/lib/Requirement/FeaturedImageHeight.php:60
#: src/modules/featured-image-size/lib/Requirement/FeaturedImageHeight.php:64
#, php-format
msgid "Maximum %spx for the featured image height"
msgstr "%spx maximum pour la hauteur de l’image mise en avant."

#: src/modules/featured-image-size/lib/Requirement/FeaturedImageWidth.php:60
#: src/modules/featured-image-size/lib/Requirement/FeaturedImageWidth.php:64
#, php-format
msgid "Maximum %spx for the featured image width"
msgstr "%spx maximum pour la largeur de l’image mise en avant"

#: src/modules/advanced-custom-fields/lib/Requirement/Base_counter.php:50
#, php-format
msgid "Maximum of %%s character in %s field"
msgstr "Maximum de %%s caractères dans le champ %s"

#: src/modules/advanced-custom-fields/lib/Requirement/Base_counter.php:51
#, php-format
msgid "Maximum of %%s characters in %s field"
msgstr "Maximum de %%s caractères dans le champ %s"

#: src/modules/image-count/lib/Requirement/ImageCount.php:51
msgid "Maximum of %d image in content"
msgstr "Maximum de %d image dans le contenu"

#: src/modules/image-count/lib/Requirement/ImageCount.php:52
msgid "Maximum of %d images in content"
msgstr "Maximum de %d images dans le contenu"

#: src/modules/woocommerce/lib/Requirement/Crosssell.php:78
#: src/modules/woocommerce/lib/Requirement/Crosssell.php:82
#, php-format
msgid "Maximum of %s products for \"Cross-sells\""
msgstr "Maximum de %s produits pour les « Ventes croisées »"

#: src/modules/woocommerce/lib/Requirement/Upsell.php:93
#: src/modules/woocommerce/lib/Requirement/Upsell.php:97
#, php-format
msgid "Maximum of %s products for \"Upsells\""
msgstr "Maximum de %s produits pour les « Produits suggerés »"

#: src/modules/woocommerce/lib/Requirement/Crosssell.php:69
#: src/modules/woocommerce/lib/Requirement/Upsell.php:84
#, php-format
msgid "Min %s Max %s"
msgstr "Min %s Max %s"

#: src/modules/woocommerce/lib/Requirement/Discount.php:74
#: src/modules/woocommerce/lib/Requirement/Discount.php:75
#, php-format
msgid "Min %s%% discount for the \"Sale price\""
msgstr "Min %s%% remise pour le « Tarif promo »"

#: src/modules/woocommerce/lib/Requirement/Discount.php:73
#, php-format
msgid "Min %s%% Max %s%%"
msgstr "Min %s%% Max %s%%"

#: src/modules/featured-image-size/lib/Requirement/FeaturedImageHeight.php:52
#: src/modules/featured-image-size/lib/Requirement/FeaturedImageHeight.php:56
#, php-format
msgid "Minimum %spx for the featured image height"
msgstr "%spx minimum pour la hauteur de l’image mise en avant"

#: src/modules/featured-image-size/lib/Requirement/FeaturedImageWidth.php:52
#: src/modules/featured-image-size/lib/Requirement/FeaturedImageWidth.php:56
#, php-format
msgid "Minimum %spx for the featured image width"
msgstr "%spx minimum pour la largeur de l’image mise en avant"

#: src/modules/advanced-custom-fields/lib/Requirement/Base_counter.php:48
#, php-format
msgid "Minimum of %%s character in %s field"
msgstr "Minimum de %%s caractères dans le champ %s"

#: src/modules/advanced-custom-fields/lib/Requirement/Base_counter.php:49
#, php-format
msgid "Minimum of %%s characters in %s field"
msgstr "Minimum de %%s caractères dans le champ %s"

#: src/modules/image-count/lib/Requirement/ImageCount.php:49
msgid "Minimum of %d image in content"
msgstr "Minimum de %d image dans le contenu"

#: src/modules/image-count/lib/Requirement/ImageCount.php:50
msgid "Minimum of %d images in content"
msgstr "Minimum de %d images dans le contenu"

#: src/modules/woocommerce/lib/Requirement/Crosssell.php:70
#: src/modules/woocommerce/lib/Requirement/Crosssell.php:74
#, php-format
msgid "Minimum of %s products for \"Cross-sells\""
msgstr "Minimum de %s produits pour les « Ventes croisées »"

#: src/modules/woocommerce/lib/Requirement/Upsell.php:85
#: src/modules/woocommerce/lib/Requirement/Upsell.php:89
#, php-format
msgid "Minimum of %s products for \"Upsells\""
msgstr "Minimum de %s produits pour les « Produits suggerés »"

#: src/modules/advanced-custom-fields/lib/Requirement/Base_counter.php:47
#, php-format
msgid "Number of characters in %s field"
msgstr "Nombre de caractères dans le champ %s"

#: src/modules/image-count/lib/Requirement/ImageCount.php:47
#: src/modules/image-count/lib/Requirement/ImageCount.php:48
msgid "Number of images in content"
msgstr "Nombre d'images dans le contenu"

#: src/modules/prosettings/prosettings.php:118
msgid "Pro Settings"
msgstr "Réglages Pro"

#: src/modules/woocommerce/lib/Requirement/Image.php:43
#: src/modules/woocommerce/lib/Requirement/Image.php:44
msgid "Product image"
msgstr "Image produit"

#. Author of the plugin
msgid "PublishPress"
msgstr "PublishPress"

#. Name of the plugin
msgid "PublishPress Checklists Pro"
msgstr "PublishPress Checklists Pro"

#: src/modules/woocommerce/lib/Requirement/SalePriceScheduled.php:43
#: src/modules/woocommerce/lib/Requirement/SalePriceScheduled.php:44
msgid "Schedule the \"Sale price\""
msgstr "Planifier le « Tarif promo »"

#: src/modules/woocommerce/lib/Requirement/Crosssell.php:65
msgid "Select some products for \"Cross-sells\""
msgstr "Sélectionnez quelques produits pour les « Ventes croisées »"

#: src/modules/woocommerce/lib/Requirement/Upsell.php:80
msgid "Select some products for \"Upsells\""
msgstr "Sélectionnez quelques produits pour les « Produits suggerés »"

#. Description of the plugin
msgid ""
"With PublishPress Checklists, you can choose publishing requirements for "
"your content."
msgstr ""
"Avec les listes de contrôle PublishPress, vous pouvez choisir les exigences "
"de publication pour votre contenu."

#: src/modules/woocommerce/woocommerce.php:292
msgid "WooCommerce"
msgstr "WooCommerce"

#: src/modules/woocommerce/woocommerce.php:112
msgid "WooCommerce Support"
msgstr "Prise en charge WooCommerce"
