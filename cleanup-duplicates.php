<?php
/**
 * Temporary script to clean up corrupted duplicate entries
 * Run this once to fix the database corruption
 */

// WordPress bootstrap
require_once __DIR__ . '/wp-config.php';

// Load the duplicate handler
require_once __DIR__ . '/wp-content/plugins/publishpress-checklists-pro/src/modules/duplicate-checklist/lib/DuplicateHandler.php';

echo "Starting cleanup of corrupted duplicate entries...\n";

$handler = new \PublishPress\ChecklistsPro\DuplicateChecklist\DuplicateHandler();
$result = $handler->cleanupCorruptedDuplicates();

if ($result['success']) {
    echo "✅ SUCCESS: " . $result['message'] . "\n";
    if (!empty($result['cleaned_keys'])) {
        echo "Cleaned keys:\n";
        foreach ($result['cleaned_keys'] as $key) {
            echo "  - $key\n";
        }
    }
} else {
    echo "❌ ERROR: " . $result['message'] . "\n";
}

echo "\nCleanup complete!\n";
echo "You can now try duplicating Categories Count again.\n";
