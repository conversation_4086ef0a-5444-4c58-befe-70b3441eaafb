{"name": "publishpress/publishpress-checklists-pro", "type": "wordpress-plugin", "description": "", "license": "GPL-2.0-or-later", "authors": [{"name": "PublishPress", "email": "<EMAIL>"}], "repositories": [], "require": {"php": ">=7.2.5"}, "config": {"preferred-install": {"*": "dist"}}, "minimum-stability": "stable", "require-dev": {"overtrue/phplint": "^2.1"}, "scripts": {"build": "ppbuild build", "build:dir": "ppbuild build-dir", "build:clean": "ppbuild clean", "get-version": "ppbuild version", "check:deps": "checkdep ./lib publishpress/publishpress-checklists", "fix:deps": "mergedep ./lib publishpress/publishpress-checklists", "test": ["vendor/bin/codecept run unit --bootstrap ./tests/unit/_bootstrap.php"], "check:longpath": "longpath .", "pre-autoload-dump": "composer dumpautoload --working-dir=./lib", "pre-update-cmd": "composer update --working-dir=./lib", "pre-install-cmd": "composer install --working-dir=./lib"}, "extra": {"plugin-slug": "publishpress-checklists-pro", "plugin-name": "publishpress-checklists-pro", "plugin-folder": "publishpress-checklists-pro"}}