<?php

namespace src;

use Codeception\Stub;
use PublishPress\ChecklistsPro\PluginServiceProvider;
use UnitTester;

class PluginModelCest
{
    public function _before(UnitTester $I)
    {
    }

    public function tryToGetNameAndCheckIfIsValidStringForNames(UnitTester $I)
    {
        $pluginDefinition = new PluginServiceProvider();

        $name = $pluginDefinition->getName();


        $I->assertNotEmpty($name);
        $I->assertIsString($name);

        $nameMatchesRegex = preg_match('/[a-z0-9\-]+/', $name, $matchedName);

        $I->assertEquals(1, $nameMatchesRegex, 'Invalid chars in the name');
        $I->assertEquals($name, $matchedName[0], 'Invalid chars in the name');
    }

    public function tryToGetTitleAndCheckIfIsValidStringForTitles(UnitTester $I)
    {
        $pluginDefinition = new PluginServiceProvider();

        $title = $pluginDefinition->getTitle();

        $I->assertNotEmpty($title);
        $I->assertIsString($title);
    }

    public function tryToGetVersionAndCheckIfVersionIsEqualsToTheConstant(UnitTester $I)
    {
        define('PPCHPRO_VERSION', '32.4.2');

        $pluginDefinition = new PluginServiceProvider();

        $version = $pluginDefinition->getVersion();

        $I->assertEquals(PPCHPRO_VERSION, $version);
    }

    public function tryToGetPluginFileAndCheckIFFormatIsCorrect(UnitTester $I)
    {
        $name = 'my-plugin';

        $model = Stub::make(
            'PublishPress\\ChecklistsPro\\PluginServiceProvider',
            [
                'getName' => $name,
            ]
        );

        $expected = $name . DIRECTORY_SEPARATOR . $name . '.php';
        $I->assertEquals($expected, $model->getPluginFile());
    }

    public function tryToGetBasePathAndCheckIfPathIsEqualsToTheConstant(UnitTester $I)
    {
        define('PPCHPRO_BASE_PATH', '/var/www/html/wp-content/plugins/plublishpress-checklists-pro');

        $pluginDefinition = new PluginServiceProvider();

        $path = $pluginDefinition->getBasePath();

        $I->assertEquals(PPCHPRO_BASE_PATH, $path);
    }

    public function tryToGetTextDomainLoaderAndCheckIfImplementsCorrectInterface(UnitTester $I)
    {
        $model = Stub::make(
            'PublishPress\\ChecklistsPro\\PluginServiceProvider',
            [
                'getBasePath' => '/var/www/',
            ]
        );

        $textDomainLoader = $model->getTextDomainLoader();

        $I->assertIsObject($textDomainLoader);
    }
}
