(function ($, document, PP_Checklists) {
  'use strict';

  $(function () {
    var lastCount = 0;

    if ($('#pp-checklists-req-rank_math_score').length > 0) {
      if (PP_Checklists.is_gutenberg_active()) {
        /**
         * For Gutenberg
         */
        wp.data.subscribe(function () {

          var score = $('.rank-math-toolbar-score .score-text').text().trim().split(' / ')[0];

          var min = parseInt(objectL10n_checklist_requirements.requirements.rank_math_score.value[0]),
            max = parseInt(objectL10n_checklist_requirements.requirements.rank_math_score.value[1]);

          $('#pp-checklists-req-rank_math_score').trigger(
            PP_Checklists.EVENT_UPDATE_REQUIREMENT_STATE,
            PP_Checklists.check_valid_quantity(score, min, max),
          );

          lastCount = score;
        });
      } else {
        /**
         * For the Classic Editor
         */
        var $content = $('#content');
        var lastCount = 0;
        var editor;

        /**
         * Check for Rank Math score and update the requirement
         */
        $(document).on(PP_Checklists.EVENT_TIC, function (event) {
          var scoreText = $('.rank-math-seo-score .score-text').text().trim();
          
          // Extract the numeric score from the text
          // The format is typically "SEO: XX / 100" where XX is the score
          var scoreMatch = scoreText.match(/(\d+)\s*\/\s*100/);
          var score = scoreMatch ? scoreMatch[1] : 0;
          
          // Convert to number
          score = parseInt(score) || 0;
          
          // Only update if the score has changed
          if (score !== lastCount) {
            
            var min = parseInt(objectL10n_checklist_requirements.requirements.rank_math_score.value[0]),
              max = parseInt(objectL10n_checklist_requirements.requirements.rank_math_score.value[1]);

            $('#pp-checklists-req-rank_math_score').trigger(
              PP_Checklists.EVENT_UPDATE_REQUIREMENT_STATE,
              PP_Checklists.check_valid_quantity(score, min, max),
            );

            lastCount = score;
          }
        });

        // For the editor.
        $(document).on(PP_Checklists.EVENT_TINYMCE_LOADED, function (event, tinymce) {
          editor = tinymce.editors['content'];

          if (typeof editor !== 'undefined') {
            editor.onInit.add(function () {
              /**
               * Bind the words count update triggers.
               *
               * When a node change in the main TinyMCE editor has been triggered.
               * When a key has been released in the plain text content editor.
               */

              if (editor.id !== 'content') {
                return;
              }

              editor.on('nodechange keyup', _.debounce(update, 200));
            });
          }
        });

        $content.on('input keyup', _.debounce(update, 200));
        update();
      }
    }
  });
})(jQuery, document, PP_Checklists);
