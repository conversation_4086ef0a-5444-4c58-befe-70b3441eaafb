/* Duplicate Checklist Styles */

/* Duplicate button styling */
.ppc-duplicate-btn {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    padding: 4px 8px;
    height: auto;
    line-height: 1.2;
}

.ppc-duplicate-btn .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
}

.ppc-duplicate-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Duplicate management actions */
.ppc-duplicate-actions {
    display: flex;
    gap: 4px;
    justify-content: center;
    align-items: center;
}

.ppc-rename-duplicate-btn,
.ppc-delete-duplicate-btn {
    padding: 2px 4px;
    min-width: 24px;
    height: 24px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.ppc-rename-duplicate-btn .dashicons,
.ppc-delete-duplicate-btn .dashicons {
    font-size: 12px;
    width: 12px;
    height: 12px;
}

.ppc-rename-duplicate-btn:hover {
    background-color: #0073aa;
    border-color: #0073aa;
    color: white;
}

.ppc-delete-duplicate-btn:hover {
    background-color: #d63638;
    border-color: #d63638;
    color: white;
}

.ppc-rename-duplicate-btn:disabled,
.ppc-delete-duplicate-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Duplicate requirement row styling */
.ppc-duplicate-requirement {
    background-color: #f8f9fa;
    border-left: 3px solid #007cba;
}

.ppc-duplicate-requirement:hover {
    background-color: #f0f6fc;
}

/* Duplicate badge */
.ppc-duplicate-badge {
    display: inline-block;
    background-color: #007cba;
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 3px;
    margin-left: 8px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Duplicate column header */
.pp-checklists-requirements-settings th:last-child {
    width: 100px;
    text-align: center;
}

/* Duplicate column cell */
.pp-checklists-requirements-settings td:last-child {
    text-align: center;
    vertical-align: middle;
}

/* Loading state for duplicate button */
.ppc-duplicate-btn .dashicons-update-alt {
    animation: ppc-spin 1s linear infinite;
}

@keyframes ppc-spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Notice styling improvements */
.notice.ppc-duplicate-notice {
    margin: 15px 0;
    padding: 12px;
}

.notice.ppc-duplicate-notice p {
    margin: 0;
    font-weight: 500;
}

/* Responsive adjustments */
@media screen and (max-width: 782px) {
    .ppc-duplicate-btn {
        font-size: 11px;
        padding: 3px 6px;
    }
    
    .ppc-duplicate-btn .dashicons {
        font-size: 12px;
        width: 12px;
        height: 12px;
    }
    
    .ppc-duplicate-badge {
        font-size: 9px;
        padding: 1px 4px;
    }
}

/* Table layout improvements */
.pp-checklists-requirements-settings {
    table-layout: fixed;
}

.pp-checklists-requirements-settings th:nth-child(1) {
    width: 30%;
}

.pp-checklists-requirements-settings th:nth-child(2) {
    width: 20%;
}

.pp-checklists-requirements-settings th:nth-child(3) {
    width: 15%;
}

.pp-checklists-requirements-settings th:nth-child(4) {
    width: 25%;
}

.pp-checklists-requirements-settings th:nth-child(5) {
    width: 10%;
}

/* Duplicate requirement title styling */
.ppc-duplicate-requirement .pp-checklists-custom-item-title {
    border-left: 3px solid #007cba;
}

/* Hover effects */
.ppc-duplicate-btn:hover:not(:disabled) {
    background-color: #0073aa;
    border-color: #0073aa;
    color: white;
}

/* Focus states for accessibility */
.ppc-duplicate-btn:focus {
    box-shadow: 0 0 0 2px #007cba;
    outline: none;
}

/* Success state */
.ppc-duplicate-btn.success {
    background-color: #00a32a;
    border-color: #00a32a;
    color: white;
}

/* Error state */
.ppc-duplicate-btn.error {
    background-color: #d63638;
    border-color: #d63638;
    color: white;
}

/* Tooltip styling for duplicate button */
.ppc-duplicate-btn[title]:hover::after {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: #333;
    color: white;
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 11px;
    white-space: nowrap;
    z-index: 1000;
    margin-bottom: 5px;
}

.ppc-duplicate-btn[title]:hover::before {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: #333;
    z-index: 1000;
    margin-bottom: 1px;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .ppc-duplicate-requirement {
        background-color: #1e1e1e;
        border-left-color: #00a0d2;
    }
    
    .ppc-duplicate-requirement:hover {
        background-color: #2a2a2a;
    }
    
    .ppc-duplicate-badge {
        background-color: #00a0d2;
    }
}
