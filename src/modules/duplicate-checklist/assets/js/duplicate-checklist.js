(function ($, document) {
  'use strict';

  $(function () {
    // Handle duplicate button clicks
    $(document).on('click', '.ppc-duplicate-btn', function (e) {
      e.preventDefault();
      
      var $button = $(this);
      var requirementName = $button.data('requirement');
      var postType = $button.data('post-type');
      
      // Confirm duplication
      if (!confirm(ppcDuplicateChecklist.strings.confirm_duplicate)) {
        return;
      }
      
      // Ask for custom name
      var customName = prompt(ppcDuplicateChecklist.strings.enter_name, requirementName + ' Copy');
      if (customName === null) {
        return; // User cancelled
      }
      
      // Disable button and show loading state
      $button.prop('disabled', true);
      var originalText = $button.html();
      $button.html('<span class="dashicons dashicons-update-alt"></span> ' + ppcDuplicateChecklist.strings.duplicating);
      
      // Make AJAX request
      $.ajax({
        url: ppcDuplicateChecklist.ajaxurl,
        type: 'POST',
        data: {
          action: 'ppc_duplicate_requirement',
          requirement: requirementName,
          post_type: postType,
          custom_name: customName,
          _wpnonce: ppcDuplicateChecklist.nonce
        },
        success: function (response) {
          if (response.success) {
            // Show success message
            showNotice(ppcDuplicateChecklist.strings.success, 'success');
            
            // Reload the page to show the new duplicate
            setTimeout(function () {
              window.location.reload();
            }, 1500);
          } else {
            showNotice(response.data || ppcDuplicateChecklist.strings.error, 'error');
            resetButton($button, originalText);
          }
        },
        error: function () {
          showNotice(ppcDuplicateChecklist.strings.error, 'error');
          resetButton($button, originalText);
        }
      });
    });

    // Handle delete duplicate button clicks
    $(document).on('click', '.ppc-delete-duplicate-btn', function (e) {
      e.preventDefault();

      var $button = $(this);
      var requirementName = $button.data('requirement');

      // Confirm deletion
      if (!confirm('Are you sure you want to delete this duplicate requirement? This action cannot be undone.')) {
        return;
      }

      // Disable button and show loading state
      $button.prop('disabled', true);
      var originalHtml = $button.html();
      $button.html('<span class="dashicons dashicons-update-alt"></span>');

      // Make AJAX request
      $.ajax({
        url: ppcDuplicateChecklist.ajaxurl,
        type: 'POST',
        data: {
          action: 'ppc_delete_duplicate_requirement',
          requirement: requirementName,
          _wpnonce: ppcDuplicateChecklist.nonce
        },
        success: function (response) {
          if (response.success) {
            // Remove the row from the table
            $button.closest('tr').fadeOut(function() {
              $(this).remove();
            });
            showNotice('Duplicate requirement deleted successfully.', 'success');
          } else {
            showNotice(response.data || 'Error deleting duplicate requirement.', 'error');
            resetButton($button, originalHtml);
          }
        },
        error: function () {
          showNotice('Error deleting duplicate requirement.', 'error');
          resetButton($button, originalHtml);
        }
      });
    });

    // Handle rename duplicate button clicks
    $(document).on('click', '.ppc-rename-duplicate-btn', function (e) {
      e.preventDefault();

      var $button = $(this);
      var requirementName = $button.data('requirement');
      var $titleCell = $button.closest('tr').find('td:first');
      var currentTitle = $titleCell.text().trim().replace(' (Duplicate)', '');

      // Ask for new name
      var newName = prompt('Enter a new name for this duplicate requirement:', currentTitle);
      if (newName === null || newName.trim() === '') {
        return; // User cancelled or entered empty name
      }

      // Disable button and show loading state
      $button.prop('disabled', true);
      var originalHtml = $button.html();
      $button.html('<span class="dashicons dashicons-update-alt"></span>');

      // Make AJAX request
      $.ajax({
        url: ppcDuplicateChecklist.ajaxurl,
        type: 'POST',
        data: {
          action: 'ppc_rename_duplicate_requirement',
          requirement: requirementName,
          new_name: newName.trim(),
          _wpnonce: ppcDuplicateChecklist.nonce
        },
        success: function (response) {
          if (response.success) {
            // Update the title in the table
            $titleCell.text(response.data.new_name + ' (Duplicate)');
            showNotice('Duplicate requirement renamed successfully.', 'success');
            resetButton($button, originalHtml);
          } else {
            showNotice(response.data || 'Error renaming duplicate requirement.', 'error');
            resetButton($button, originalHtml);
          }
        },
        error: function () {
          showNotice('Error renaming duplicate requirement.', 'error');
          resetButton($button, originalHtml);
        }
      });
    });

    /**
     * Reset button to original state
     */
    function resetButton($button, originalText) {
      $button.prop('disabled', false);
      $button.html(originalText);
    }
    
    /**
     * Show admin notice
     */
    function showNotice(message, type) {
      var noticeClass = type === 'success' ? 'notice-success' : 'notice-error';
      var $notice = $('<div class="notice ' + noticeClass + ' is-dismissible"><p>' + message + '</p></div>');
      
      // Insert notice after the page title
      if ($('.wrap h1').length) {
        $('.wrap h1').after($notice);
      } else {
        $('.wrap').prepend($notice);
      }
      
      // Auto-dismiss after 5 seconds
      setTimeout(function () {
        $notice.fadeOut();
      }, 5000);
      
      // Handle dismiss button
      $notice.on('click', '.notice-dismiss', function () {
        $notice.fadeOut();
      });
    }
    
    /**
     * Add visual indicators for duplicate requirements
     */
    function addDuplicateIndicators() {
      $('.pp-checklists-requirement-row').each(function () {
        var $row = $(this);
        var requirementName = $row.find('input[name*="_title"]').attr('name');
        
        if (requirementName && requirementName.indexOf('_duplicate_') !== -1) {
          // Add duplicate indicator
          $row.addClass('ppc-duplicate-requirement');
          
          // Add badge to the title cell
          var $titleCell = $row.find('td:first');
          if (!$titleCell.find('.ppc-duplicate-badge').length) {
            $titleCell.append('<span class="ppc-duplicate-badge">Duplicate</span>');
          }
        }
      });
    }
    
    // Add duplicate indicators on page load
    addDuplicateIndicators();
    
    // Re-add indicators when new rows are added (for dynamic content)
    $(document).on('DOMNodeInserted', function () {
      setTimeout(addDuplicateIndicators, 100);
    });
  });

})(jQuery, document);
