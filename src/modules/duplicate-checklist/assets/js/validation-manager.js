(function ($, document, PP_Checklists) {
  'use strict';

  /**
   * Duplicate Validation Manager
   * Handles JavaScript validation for duplicated requirements
   */
  var DuplicateValidationManager = {
    
    registeredValidations: {},
    
    /**
     * Initialize the validation manager
     */
    init: function () {
      // Auto-register all duplicate requirements on page load
      if (typeof duplicateRequirements !== 'undefined') {
        for (var duplicateName in duplicateRequirements) {
          var duplicate = duplicateRequirements[duplicateName];
          this.registerDuplicate(
            duplicate.originalType,
            duplicateName,
            duplicate.config
          );
        }
      }
    },
    
    /**
     * Register a duplicate requirement validation
     */
    registerDuplicate: function (originalType, duplicateName, config) {
      var validationFunction = this.getValidationFunction(originalType);
      
      if (validationFunction && $('#pp-checklists-req-' + duplicateName).length > 0) {
        var self = this;
        $(document).on(PP_Checklists.EVENT_TIC, function (event) {
          validationFunction.call(self, duplicateName, config);
        });
        
        // Store registration info
        this.registeredValidations[duplicateName] = {
          originalType: originalType,
          config: config
        };
      }
    },
    
    /**
     * Get validation function by requirement type
     */
    getValidationFunction: function (type) {
      switch (type) {
        case 'title_count':
          return this.validateTitleCount;
        case 'words_count':
          return this.validateWordsCount;
        case 'image_count':
          return this.validateImageCount;
        case 'internal_links':
          return this.validateInternalLinks;
        case 'external_links':
          return this.validateExternalLinks;
        case 'image_alt_count':
          return this.validateImageAltCount;
        case 'validate_links':
          return this.validateLinks;
        // Add more validation functions as needed
        default:
          return null;
      }
    },
    
    /**
     * Title count validation (reusable)
     */
    validateTitleCount: function (requirementName, config) {
      var count = 0, obj = null;
      var minValue = parseInt(config.rule ? config.rule.value[0] : config.min || 0);
      var maxValue = parseInt(config.rule ? config.rule.value[1] : config.max || 0);
      
      if (PP_Checklists.is_gutenberg_active()) {
        obj = wp.htmlEntities.decodeEntities(PP_Checklists.getEditor().getEditedPostAttribute('title'));
      } else {
        if ($('#title').length === 0) return;
        obj = $('#title').val();
      }
      
      if (typeof obj !== 'undefined') {
        count = obj.length;
        $('#pp-checklists-req-' + requirementName).trigger(
          PP_Checklists.EVENT_UPDATE_REQUIREMENT_STATE,
          PP_Checklists.check_valid_quantity(count, minValue, maxValue)
        );
      }
    },
    
    /**
     * Words count validation
     */
    validateWordsCount: function (requirementName, config) {
      var count = 0;
      var minValue = parseInt(config.rule ? config.rule.value[0] : config.min || 0);
      var maxValue = parseInt(config.rule ? config.rule.value[1] : config.max || 0);
      
      if (PP_Checklists.is_gutenberg_active()) {
        var content = PP_Checklists.getEditor().getEditedPostAttribute('content');
        if (typeof content !== 'undefined') {
          count = wp.utils.WordCounter.prototype.count(content);
        }
      } else {
        var $content = $('#content');
        var text;
        
        if (typeof tinymce !== 'undefined' && tinymce.editors['content'] && !tinymce.editors['content'].isHidden()) {
          text = tinymce.editors['content'].getContent({ format: 'raw' });
        } else {
          text = $content.val();
        }
        
        if (typeof wp !== 'undefined' && wp.utils && wp.utils.WordCounter) {
          count = wp.utils.WordCounter.prototype.count(text);
        }
      }
      
      $('#pp-checklists-req-' + requirementName).trigger(
        PP_Checklists.EVENT_UPDATE_REQUIREMENT_STATE,
        PP_Checklists.check_valid_quantity(count, minValue, maxValue)
      );
    },
    
    /**
     * Image count validation
     */
    validateImageCount: function (requirementName, config) {
      var count = 0;
      var minValue = parseInt(config.rule ? config.rule.value[0] : config.min || 0);
      var maxValue = parseInt(config.rule ? config.rule.value[1] : config.max || 0);
      
      if (PP_Checklists.is_gutenberg_active()) {
        var content = PP_Checklists.getEditor().getEditedPostAttribute('content');
        if (typeof content !== 'undefined') {
          var imgRegex = /<img[^>]+src\s*=\s*['"]([^'"]+)['"][^>]*>/gi;
          var matches = content.match(imgRegex);
          count = matches ? matches.length : 0;
        }
      } else {
        var $content = $('#content');
        var text;
        
        if (typeof tinymce !== 'undefined' && tinymce.editors['content'] && !tinymce.editors['content'].isHidden()) {
          text = tinymce.editors['content'].getContent({ format: 'raw' });
        } else {
          text = $content.val();
        }
        
        var imgRegex = /<img[^>]+src\s*=\s*['"]([^'"]+)['"][^>]*>/gi;
        var matches = text.match(imgRegex);
        count = matches ? matches.length : 0;
      }
      
      $('#pp-checklists-req-' + requirementName).trigger(
        PP_Checklists.EVENT_UPDATE_REQUIREMENT_STATE,
        PP_Checklists.check_valid_quantity(count, minValue, maxValue)
      );
    },
    
    /**
     * Internal links validation
     */
    validateInternalLinks: function (requirementName, config) {
      var count = 0;
      var minValue = parseInt(config.rule ? config.rule.value[0] : config.min || 0);
      var maxValue = parseInt(config.rule ? config.rule.value[1] : config.max || 0);
      
      var content = '';
      if (PP_Checklists.is_gutenberg_active()) {
        content = PP_Checklists.getEditor().getEditedPostAttribute('content') || '';
      } else {
        var $content = $('#content');
        if (typeof tinymce !== 'undefined' && tinymce.editors['content'] && !tinymce.editors['content'].isHidden()) {
          content = tinymce.editors['content'].getContent({ format: 'raw' });
        } else {
          content = $content.val();
        }
      }
      
      if (typeof PP_Checklists.get_internal_links_count === 'function') {
        count = PP_Checklists.get_internal_links_count(content);
      }
      
      $('#pp-checklists-req-' + requirementName).trigger(
        PP_Checklists.EVENT_UPDATE_REQUIREMENT_STATE,
        PP_Checklists.check_valid_quantity(count, minValue, maxValue)
      );
    },
    
    /**
     * External links validation
     */
    validateExternalLinks: function (requirementName, config) {
      var count = 0;
      var minValue = parseInt(config.rule ? config.rule.value[0] : config.min || 0);
      var maxValue = parseInt(config.rule ? config.rule.value[1] : config.max || 0);
      
      var content = '';
      if (PP_Checklists.is_gutenberg_active()) {
        content = PP_Checklists.getEditor().getEditedPostAttribute('content') || '';
      } else {
        var $content = $('#content');
        if (typeof tinymce !== 'undefined' && tinymce.editors['content'] && !tinymce.editors['content'].isHidden()) {
          content = tinymce.editors['content'].getContent({ format: 'raw' });
        } else {
          content = $content.val();
        }
      }
      
      if (typeof PP_Checklists.get_external_links_count === 'function') {
        count = PP_Checklists.get_external_links_count(content);
      }
      
      $('#pp-checklists-req-' + requirementName).trigger(
        PP_Checklists.EVENT_UPDATE_REQUIREMENT_STATE,
        PP_Checklists.check_valid_quantity(count, minValue, maxValue)
      );
    },
    
    /**
     * Image alt count validation
     */
    validateImageAltCount: function (requirementName, config) {
      var minValue = parseInt(config.rule ? config.rule.value[0] : config.min || 0);
      var maxValue = parseInt(config.rule ? config.rule.value[1] : config.max || 0);
      
      var content = '';
      if (PP_Checklists.is_gutenberg_active()) {
        content = PP_Checklists.getEditor().getEditedPostAttribute('content') || '';
      } else {
        var $content = $('#content');
        if (typeof tinymce !== 'undefined' && tinymce.editors['content'] && !tinymce.editors['content'].isHidden()) {
          content = tinymce.editors['content'].getContent({ format: 'raw' });
        } else {
          content = $content.val();
        }
      }
      
      var isValid = true;
      if (typeof PP_Checklists.get_image_alt_lengths === 'function') {
        var altLengths = PP_Checklists.get_image_alt_lengths(content);
        isValid = altLengths.every(function (length) {
          return PP_Checklists.check_valid_quantity(length, minValue, maxValue);
        });
      }
      
      $('#pp-checklists-req-' + requirementName).trigger(
        PP_Checklists.EVENT_UPDATE_REQUIREMENT_STATE,
        isValid
      );
    },
    
    /**
     * Validate links format
     */
    validateLinks: function (requirementName, config) {
      var content = '';
      if (PP_Checklists.is_gutenberg_active()) {
        content = PP_Checklists.getEditor().getEditedPostAttribute('content') || '';
      } else {
        var $content = $('#content');
        if (typeof tinymce !== 'undefined' && tinymce.editors['content'] && !tinymce.editors['content'].isHidden()) {
          content = tinymce.editors['content'].getContent({ format: 'raw' });
        } else {
          content = $content.val();
        }
      }
      
      var isValid = true;
      if (typeof PP_Checklists.validate_links_format === 'function') {
        var invalidLinks = PP_Checklists.validate_links_format(content);
        isValid = invalidLinks.length === 0;
      }
      
      $('#pp-checklists-req-' + requirementName).trigger(
        PP_Checklists.EVENT_UPDATE_REQUIREMENT_STATE,
        isValid
      );
    }
  };

  // Initialize when document is ready
  $(document).ready(function () {
    DuplicateValidationManager.init();
  });

  // Make it globally available
  window.DuplicateValidationManager = DuplicateValidationManager;

})(jQuery, document, PP_Checklists);
