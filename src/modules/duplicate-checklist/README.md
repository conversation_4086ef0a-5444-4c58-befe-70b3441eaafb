# Duplicate Checklist Module

This module provides functionality to duplicate checklist requirements with different configurations, enabling role-based requirements and flexible checklist management.

## Features

- **Duplicate Requirements**: Create copies of existing requirements with unique configurations
- **Role-Based Configurations**: Different settings for different user roles (e.g., author vs editor requirements)
- **JavaScript Validation**: Automatic JS validation for duplicated requirements
- **Management Interface**: Rename and delete duplicate requirements
- **Visual Indicators**: Clear identification of duplicate requirements in the admin interface

## Use Cases

### Role-Based Requirements
- **Authors**: Minimum 10 characters in title
- **Editors**: Minimum 20 characters in title
- **Different word counts**: Authors need 300+ words, editors need 500+ words

### Content Type Variations
- **Blog Posts**: Different requirements than pages
- **Product Pages**: Specific requirements for e-commerce content
- **Landing Pages**: Specialized requirements for conversion optimization

## How It Works

### 1. Database Storage
Duplicated requirements are stored using the same pattern as original requirements but with unique names:
- Original: `title_count_rule`
- Duplicate: `title_count_duplicate_1234567890_rule`

### 2. Dynamic Class Generation
The system creates dynamic PHP classes that extend the original requirement classes, maintaining all functionality while allowing different configurations.

### 3. JavaScript Validation
A validation manager automatically registers JS validation for duplicated requirements, reusing the original validation logic with new configuration values.

## Usage

### Duplicating a Requirement

1. Go to **Checklists** → **Checklists** in your WordPress admin
2. Find the requirement you want to duplicate
3. Click the **Duplicate** button in the "Duplicate" column
4. Enter a custom name for the duplicate (optional)
5. Configure the duplicate with different settings

### Managing Duplicates

- **Rename**: Click the edit icon next to a duplicate requirement
- **Delete**: Click the trash icon next to a duplicate requirement
- **Configure**: Edit settings just like any other requirement

### Testing

If `WP_DEBUG` is enabled, you can access testing tools at:
**Tools** → **Test Duplicates**

## Technical Details

### File Structure
```
src/modules/duplicate-checklist/
├── duplicate-checklist.php          # Main module file
├── lib/
│   └── DuplicateHandler.php         # Core duplication logic
├── assets/
│   ├── js/
│   │   ├── duplicate-checklist.js   # Frontend functionality
│   │   └── validation-manager.js    # JS validation system
│   └── css/
│       └── duplicate-checklist.css  # Styling
├── test-duplicate.php               # Testing utilities
└── README.md                        # This file
```

### Hooks Used

- `publishpress_checklists_tasks_list_th` - Adds duplicate column header
- `publishpress_checklists_tasks_list_td` - Adds duplicate buttons
- `publishpress_checklists_post_type_requirements` - Registers duplicated requirements

### AJAX Endpoints

- `ppc_duplicate_requirement` - Creates a duplicate
- `ppc_delete_duplicate_requirement` - Deletes a duplicate
- `ppc_rename_duplicate_requirement` - Renames a duplicate

### Database Options

- `publishpress_checklists_checklists_options` - Main requirements configuration
- `ppc_duplicate_class_mappings` - Dynamic class mappings for duplicates

## Supported Requirement Types

The validation manager supports these requirement types:
- `title_count` - Character count in title
- `words_count` - Word count in content
- `image_count` - Number of images
- `internal_links` - Internal link count
- `external_links` - External link count
- `image_alt_count` - Image alt text validation
- `validate_links` - Link format validation

Additional requirement types can be added by extending the validation manager.

## Security

- All AJAX requests are nonce-protected
- User capability checks (`manage_options`)
- Input sanitization and validation
- SQL injection prevention through WordPress APIs

## Compatibility

- WordPress 5.5+
- PHP 7.2.5+
- PublishPress Checklists (free version)
- All supported post types
- Gutenberg and Classic Editor

## Troubleshooting

### Common Issues

1. **Duplicate button not appearing**
   - Check if the module is enabled
   - Verify user has `manage_options` capability
   - Check browser console for JavaScript errors

2. **JavaScript validation not working**
   - Ensure `pp-checklists-requirements` script is loaded
   - Check if duplicate requirements data is localized
   - Verify requirement type is supported in validation manager

3. **Duplicates not appearing in post editor**
   - Check if requirements are enabled for the post type
   - Verify dynamic class generation is working
   - Check `ppc_duplicate_class_mappings` option

### Debug Mode

Enable `WP_DEBUG` to access testing tools and additional logging.

## Development

### Adding New Validation Types

To add support for a new requirement type:

1. Add the type to `getValidationFunction()` in `validation-manager.js`
2. Implement the validation function following the existing pattern
3. Update the class mapping in `DuplicateHandler.php` if needed

### Extending Functionality

The module is designed to be extensible. Key extension points:
- Custom requirement types
- Additional AJAX endpoints
- Enhanced UI components
- Integration with other plugins

## License

This module is part of PublishPress Checklists Pro and follows the same licensing terms.
