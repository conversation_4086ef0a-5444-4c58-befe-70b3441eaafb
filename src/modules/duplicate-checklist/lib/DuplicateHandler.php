<?php

/**
 * @package     PublishPress\ChecklistsPro
 * <AUTHOR> <<EMAIL>>
 * @copyright   copyright (C) 2019 PublishPress. All rights reserved.
 * @license     GPLv2 or later
 * @since       1.0.0
 */

namespace PublishPress\ChecklistsPro\DuplicateChecklist;

/**
 * Class DuplicateHandler
 * 
 * Handles the core logic for duplicating checklist requirements
 */
class DuplicateHandler
{
    /**
     * Duplicate a requirement with all its settings
     *
     * @param string $original_name The original requirement name
     * @param string $post_type The post type
     * @param string $custom_name Optional custom name for the duplicate
     * @return array Result array with success status and data
     */
    public function duplicateRequirement($original_name, $post_type, $custom_name = '')
    {
        try {
            // Generate unique name for the duplicate
            $duplicate_name = $this->generateDuplicateName($original_name, $custom_name);
            
            // Get current options
            $options = get_option('publishpress_checklists_checklists_options', new \stdClass());
            if (!is_object($options)) {
                $options = new \stdClass();
            }

            // Copy all settings from original to duplicate
            $this->copyRequirementSettings($options, $original_name, $duplicate_name, $post_type);

            // Store metadata about the duplicate
            $this->storeDuplicateMetadata($options, $duplicate_name, $original_name, $custom_name);

            // Save the updated options
            update_option('publishpress_checklists_checklists_options', $options);

            // Create the dynamic requirement class
            $this->createDynamicRequirementClass($original_name, $duplicate_name);

            return [
                'success' => true,
                'duplicate_name' => $duplicate_name,
                'original_name' => $original_name,
                'message' => 'Requirement duplicated successfully'
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Error duplicating requirement: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Generate a unique name for the duplicate requirement
     *
     * @param string $original_name
     * @param string $custom_name
     * @return string
     */
    private function generateDuplicateName($original_name, $custom_name = '')
    {
        if (!empty($custom_name)) {
            // Sanitize custom name
            $custom_name = sanitize_title($custom_name);
            $duplicate_name = $original_name . '_duplicate_' . $custom_name;
        } else {
            // Generate with timestamp
            $duplicate_name = $original_name . '_duplicate_' . time();
        }

        // Ensure uniqueness
        $options = get_option('publishpress_checklists_checklists_options', new \stdClass());
        $counter = 1;
        $base_name = $duplicate_name;
        
        while (isset($options->{$duplicate_name . '_rule'})) {
            $duplicate_name = $base_name . '_' . $counter;
            $counter++;
        }

        return $duplicate_name;
    }

    /**
     * Copy all settings from original requirement to duplicate
     *
     * @param object $options
     * @param string $original_name
     * @param string $duplicate_name
     * @param string $post_type
     */
    private function copyRequirementSettings($options, $original_name, $duplicate_name, $post_type)
    {
        // Define setting suffixes to copy
        $setting_suffixes = [
            '_rule',
            '_min',
            '_max',
            '_multiple',
            '_can_ignore',
            '_editable_by',
            '_title',
            '_users',
            '_roles',
            '_field_key',
            '_taxonomy',
            '_terms'
        ];

        foreach ($setting_suffixes as $suffix) {
            $original_key = $original_name . $suffix;
            $duplicate_key = $duplicate_name . $suffix;

            if (isset($options->{$original_key})) {
                // Copy the entire setting structure
                $options->{$duplicate_key} = $this->deepCopy($options->{$original_key});
            }
        }

        // Also copy any post-type specific settings
        foreach ($options as $key => $value) {
            if (strpos($key, $original_name . '_') === 0) {
                $suffix = str_replace($original_name . '_', '', $key);
                $duplicate_key = $duplicate_name . '_' . $suffix;
                
                if (!isset($options->{$duplicate_key})) {
                    $options->{$duplicate_key} = $this->deepCopy($value);
                }
            }
        }
    }

    /**
     * Store metadata about the duplicate relationship
     *
     * @param object $options
     * @param string $duplicate_name
     * @param string $original_name
     * @param string $custom_name
     */
    private function storeDuplicateMetadata($options, $duplicate_name, $original_name, $custom_name)
    {
        $metadata_key = $duplicate_name . '_duplicate_meta';
        $options->{$metadata_key} = [
            'original_requirement' => $original_name,
            'custom_name' => $custom_name,
            'created_at' => current_time('mysql'),
            'created_by' => get_current_user_id()
        ];
    }

    /**
     * Create a dynamic requirement class for the duplicate
     *
     * @param string $original_name
     * @param string $duplicate_name
     */
    private function createDynamicRequirementClass($original_name, $duplicate_name)
    {
        // Get the original requirement class
        $original_class = $this->getOriginalRequirementClass($original_name);
        
        if (!$original_class) {
            return;
        }

        // Create dynamic class name
        $class_name = 'DuplicateRequirement_' . str_replace(['_', '-'], '', ucwords($duplicate_name, '_-'));
        
        // Store the class mapping for later use
        $this->storeDynamicClassMapping($duplicate_name, $class_name, $original_class);
    }

    /**
     * Get the original requirement class name
     *
     * @param string $requirement_name
     * @return string|false
     */
    private function getOriginalRequirementClass($requirement_name)
    {
        // Map of requirement names to their classes
        $class_mapping = [
            'title_count' => '\\PublishPress\\Checklists\\Core\\Requirement\\Title_count',
            'words_count' => '\\PublishPress\\Checklists\\Core\\Requirement\\Words_count',
            'internal_links' => '\\PublishPress\\Checklists\\Core\\Requirement\\Internal_links',
            'external_links' => '\\PublishPress\\Checklists\\Core\\Requirement\\External_links',
            'image_alt' => '\\PublishPress\\Checklists\\Core\\Requirement\\Image_alt',
            'image_alt_count' => '\\PublishPress\\Checklists\\Core\\Requirement\\Image_alt_count',
            'validate_links' => '\\PublishPress\\Checklists\\Core\\Requirement\\Validate_links',
            'required_categories' => '\\PublishPress\\Checklists\\Core\\Requirement\\Required_categories',
            'prohibited_categories' => '\\PublishPress\\Checklists\\Core\\Requirement\\Prohibited_categories',
            'required_tags' => '\\PublishPress\\Checklists\\Core\\Requirement\\Required_tags',
            'prohibited_tags' => '\\PublishPress\\Checklists\\Core\\Requirement\\Prohibited_tags',
            'featured_image' => '\\PublishPress\\Checklists\\Core\\Requirement\\Featured_image',
            'excerpt' => '\\PublishPress\\Checklists\\Core\\Requirement\\Excerpt',
            'approved_by' => '\\PublishPress\\Checklists\\Core\\Requirement\\Approved_by',
            // Add Pro requirements
            'image_count' => '\\PublishPress\\ChecklistsPro\\ImageCount\\Requirement\\ImageCount',
            'approved_by_user' => '\\PublishPress\\ChecklistsPro\\ApprovedByUser\\Requirement\\ApprovedByUser',
            // Add more as needed
        ];

        return isset($class_mapping[$requirement_name]) ? $class_mapping[$requirement_name] : false;
    }

    /**
     * Store dynamic class mapping for requirement registration
     *
     * @param string $duplicate_name
     * @param string $class_name
     * @param string $original_class
     */
    private function storeDynamicClassMapping($duplicate_name, $class_name, $original_class)
    {
        $mappings = get_option('ppc_duplicate_class_mappings', []);
        $mappings[$duplicate_name] = [
            'class_name' => $class_name,
            'original_class' => $original_class,
            'created_at' => current_time('mysql')
        ];
        update_option('ppc_duplicate_class_mappings', $mappings);
    }

    /**
     * Get duplicated requirements for a specific post type
     *
     * @param string $post_type
     * @return array Array of requirement class names
     */
    public function getDuplicatedRequirements($post_type)
    {
        $duplicated_requirements = [];
        $options = get_option('publishpress_checklists_checklists_options', new \stdClass());
        $class_mappings = get_option('ppc_duplicate_class_mappings', []);

        if (!is_object($options)) {
            return $duplicated_requirements;
        }

        // Find all duplicate requirements for this post type
        foreach ($options as $key => $value) {
            if (strpos($key, '_duplicate_') !== false && strpos($key, '_rule') !== false) {
                $requirement_name = str_replace('_rule', '', $key);
                
                // Check if this requirement is enabled for the post type
                if (isset($value[$post_type]) && $value[$post_type] !== 'disabled') {
                    // Create dynamic class for this duplicate
                    $class_name = $this->createDynamicClass($requirement_name, $class_mappings);
                    if ($class_name) {
                        $duplicated_requirements[] = $class_name;
                    }
                }
            }
        }

        return $duplicated_requirements;
    }

    /**
     * Create a dynamic class for a duplicate requirement
     *
     * @param string $duplicate_name
     * @param array $class_mappings
     * @return string|false
     */
    private function createDynamicClass($duplicate_name, $class_mappings)
    {
        if (!isset($class_mappings[$duplicate_name])) {
            return false;
        }

        $mapping = $class_mappings[$duplicate_name];
        $original_class = $mapping['original_class'];
        $dynamic_class_name = $mapping['class_name'];

        // Check if class already exists
        if (class_exists($dynamic_class_name)) {
            return $dynamic_class_name;
        }

        // Create the dynamic class
        $class_code = $this->generateDynamicClassCode($dynamic_class_name, $original_class, $duplicate_name);
        
        try {
            eval($class_code);
            return $dynamic_class_name;
        } catch (\Exception $e) {
            error_log('Error creating dynamic class: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Generate PHP code for a dynamic requirement class
     *
     * @param string $class_name
     * @param string $original_class
     * @param string $duplicate_name
     * @return string
     */
    private function generateDynamicClassCode($class_name, $original_class, $duplicate_name)
    {
        $original_name = $this->getOriginalNameFromDuplicate($duplicate_name);
        
        return "
        class {$class_name} extends {$original_class}
        {
            public \$name = '{$duplicate_name}';
            
            public function __construct(\$module, \$post_type)
            {
                parent::__construct(\$module, \$post_type);
                \$this->name = '{$duplicate_name}';
            }
            
            public function init_language()
            {
                parent::init_language();
                
                // Add duplicate indicator to labels
                if (isset(\$this->lang['label_settings'])) {
                    \$this->lang['label_settings'] .= ' (Duplicate)';
                }
                if (isset(\$this->lang['label'])) {
                    \$this->lang['label'] .= ' (Duplicate)';
                }
            }
        }";
    }

    /**
     * Get original requirement name from duplicate name
     *
     * @param string $duplicate_name
     * @return string
     */
    private function getOriginalNameFromDuplicate($duplicate_name)
    {
        $parts = explode('_duplicate_', $duplicate_name);
        return isset($parts[0]) ? $parts[0] : $duplicate_name;
    }

    /**
     * Delete a duplicate requirement
     *
     * @param string $duplicate_name The duplicate requirement name
     * @return array Result array with success status and data
     */
    public function deleteDuplicateRequirement($duplicate_name)
    {
        try {
            // Get current options
            $options = get_option('publishpress_checklists_checklists_options', new \stdClass());
            if (!is_object($options)) {
                $options = new \stdClass();
            }

            // Remove all settings for this duplicate
            $this->removeDuplicateSettings($options, $duplicate_name);

            // Save the updated options
            update_option('publishpress_checklists_checklists_options', $options);

            // Remove from class mappings
            $mappings = get_option('ppc_duplicate_class_mappings', []);
            if (isset($mappings[$duplicate_name])) {
                unset($mappings[$duplicate_name]);
                update_option('ppc_duplicate_class_mappings', $mappings);
            }

            return [
                'success' => true,
                'message' => 'Duplicate requirement deleted successfully'
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Error deleting duplicate requirement: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Rename a duplicate requirement
     *
     * @param string $old_name The current duplicate name
     * @param string $new_display_name The new display name
     * @return array Result array with success status and data
     */
    public function renameDuplicateRequirement($old_name, $new_display_name)
    {
        try {
            // Get current options
            $options = get_option('publishpress_checklists_checklists_options', new \stdClass());
            if (!is_object($options)) {
                $options = new \stdClass();
            }

            // Update the title/display name for all post types
            $title_key = $old_name . '_title';
            if (isset($options->{$title_key})) {
                foreach ($options->{$title_key} as $post_type => $current_title) {
                    $options->{$title_key}[$post_type] = $new_display_name;
                }
            }

            // Update metadata
            $metadata_key = $old_name . '_duplicate_meta';
            if (isset($options->{$metadata_key})) {
                $options->{$metadata_key}['custom_name'] = $new_display_name;
                $options->{$metadata_key}['updated_at'] = current_time('mysql');
            }

            // Save the updated options
            update_option('publishpress_checklists_checklists_options', $options);

            return [
                'success' => true,
                'new_name' => $new_display_name,
                'message' => 'Duplicate requirement renamed successfully'
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Error renaming duplicate requirement: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Remove all settings for a duplicate requirement
     *
     * @param object $options
     * @param string $duplicate_name
     */
    private function removeDuplicateSettings($options, $duplicate_name)
    {
        // Define setting suffixes to remove
        $setting_suffixes = [
            '_rule',
            '_min',
            '_max',
            '_multiple',
            '_can_ignore',
            '_editable_by',
            '_title',
            '_users',
            '_roles',
            '_field_key',
            '_taxonomy',
            '_terms',
            '_duplicate_meta'
        ];

        foreach ($setting_suffixes as $suffix) {
            $key = $duplicate_name . $suffix;
            if (isset($options->{$key})) {
                unset($options->{$key});
            }
        }

        // Also remove any other settings that start with the duplicate name
        foreach ($options as $key => $value) {
            if (strpos($key, $duplicate_name . '_') === 0) {
                unset($options->{$key});
            }
        }
    }

    /**
     * Deep copy an object or array
     *
     * @param mixed $data
     * @return mixed
     */
    private function deepCopy($data)
    {
        if (is_object($data)) {
            return clone $data;
        } elseif (is_array($data)) {
            return array_map([$this, 'deepCopy'], $data);
        } else {
            return $data;
        }
    }
}
