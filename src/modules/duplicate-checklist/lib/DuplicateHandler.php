<?php

/**
 * @package     PublishPress\ChecklistsPro
 * <AUTHOR> <<EMAIL>>
 * @copyright   copyright (C) 2019 PublishPress. All rights reserved.
 * @license     GPLv2 or later
 * @since       1.0.0
 */

namespace PublishPress\ChecklistsPro\DuplicateChecklist;

/**
 * Class DuplicateHandler
 * 
 * Handles the core logic for duplicating checklist requirements
 */
class DuplicateHandler
{
    /**
     * Duplicate a requirement with all its settings
     *
     * @param string $original_name The original requirement name
     * @param string $post_type The post type
     * @param string $custom_name Optional custom name for the duplicate
     * @return array Result array with success status and data
     */
    public function duplicateRequirement($original_name, $post_type, $custom_name = '')
    {
        try {
            error_log("=== DUPLICATE START ===");
            error_log("Requirement: $original_name, Post Type: $post_type, Custom Name: $custom_name");

            // Prevent duplicating already duplicated requirements
            if (strpos($original_name, '_duplicate_') !== false) {
                error_log("ERROR: Trying to duplicate an already duplicated requirement");
                return [
                    'success' => false,
                    'message' => 'Cannot duplicate an already duplicated requirement'
                ];
            }

            // Generate unique name for the duplicate
            $duplicate_name = $this->generateDuplicateName($original_name, $custom_name);
            error_log("Generated duplicate name: $duplicate_name");

            // Get current options
            $options = get_option('publishpress_checklists_checklists_options', new \stdClass());
            error_log("Options loaded: " . (is_object($options) ? 'YES' : 'NO'));
            error_log("Options type: " . gettype($options));

            if (!is_object($options)) {
                $options = new \stdClass();
                error_log("Converted options to object");
            }

            // Copy all settings from original to duplicate
            error_log("Copying settings from $original_name to $duplicate_name");
            $this->copyRequirementSettings($options, $original_name, $duplicate_name, $post_type);

            // Set the custom title for the duplicate
            error_log("Setting duplicate title");
            $this->setDuplicateTitle($options, $duplicate_name, $custom_name, $post_type);

            // Store metadata about the duplicate
            error_log("Storing duplicate metadata");
            $this->storeDuplicateMetadata($options, $duplicate_name, $original_name, $custom_name);

            // Debug: Check what keys we have before saving
            $option_keys = array_keys((array)$options);
            $duplicate_keys = array_filter($option_keys, function($key) use ($duplicate_name) {
                return strpos($key, $duplicate_name) === 0;
            });
            error_log("Keys to save for duplicate: " . implode(', ', $duplicate_keys));

            // Save the updated options
            error_log("About to save options to database");
            $save_result = update_option('publishpress_checklists_checklists_options', $options);
            error_log("Save result: " . ($save_result ? 'SUCCESS' : 'FAILED'));

            // Create the dynamic requirement class
            error_log("Creating dynamic requirement class");
            $this->createDynamicRequirementClass($original_name, $duplicate_name);

            error_log("=== DUPLICATE SUCCESS ===");
            error_log("Duplicate created: $duplicate_name");

            return [
                'success' => true,
                'duplicate_name' => $duplicate_name,
                'original_name' => $original_name,
                'message' => 'Requirement duplicated successfully'
            ];

        } catch (\Exception $e) {
            error_log("=== DUPLICATE ERROR ===");
            error_log("Exception: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());

            return [
                'success' => false,
                'message' => 'Error duplicating requirement: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Generate a unique name for the duplicate requirement using numbered format
     *
     * @param string $original_name
     * @param string $custom_name
     * @return string
     */
    private function generateDuplicateName($original_name, $custom_name = '')
    {
        $options = get_option('publishpress_checklists_checklists_options', new \stdClass());

        if (!empty($custom_name)) {
            // Use custom name with numbered format
            $sanitized_custom = sanitize_title($custom_name);
            $base_name = $original_name . '_duplicate_' . $sanitized_custom;
        } else {
            // Find the next available number for this requirement
            $next_number = $this->getNextDuplicateNumber($original_name, $options);
            $base_name = $original_name . '_duplicate_' . $next_number;
        }

        // Ensure uniqueness (in case of conflicts)
        $duplicate_name = $base_name;
        $counter = 1;

        while (isset($options->{$duplicate_name . '_rule'})) {
            $duplicate_name = $base_name . '_' . $counter;
            $counter++;
        }

        return $duplicate_name;
    }

    /**
     * Get the next available number for duplicating a requirement
     *
     * @param string $original_name
     * @param object $options
     * @return int
     */
    private function getNextDuplicateNumber($original_name, $options)
    {
        $existing_numbers = [];

        // Find all existing duplicates of this requirement
        foreach ($options as $key => $value) {
            if (strpos($key, $original_name . '_duplicate_') === 0 && strpos($key, '_rule') !== false) {
                // Extract the duplicate identifier
                $duplicate_name = str_replace('_rule', '', $key);
                $duplicate_part = str_replace($original_name . '_duplicate_', '', $duplicate_name);

                // Check if it's a number
                if (is_numeric($duplicate_part)) {
                    $existing_numbers[] = (int) $duplicate_part;
                }
            }
        }

        // Find the next available number starting from 2
        $next_number = 2;
        while (in_array($next_number, $existing_numbers)) {
            $next_number++;
        }

        return $next_number;
    }

    /**
     * Copy all settings from original requirement to duplicate
     *
     * @param object $options
     * @param string $original_name
     * @param string $duplicate_name
     * @param string $post_type
     */
    private function copyRequirementSettings($options, $original_name, $duplicate_name, $post_type)
    {
        error_log("=== COPY SETTINGS START ===");
        error_log("Original: $original_name, Duplicate: $duplicate_name, Post Type: $post_type");

        // Define setting suffixes to copy
        $setting_suffixes = [
            '_rule',
            '_min',
            '_max',
            '_multiple',
            '_can_ignore',
            '_editable_by',
            '_title',
            '_users',
            '_roles',
            '_field_key',
            '_taxonomy',
            '_terms'
        ];

        $copied_settings = [];
        $missing_settings = [];

        foreach ($setting_suffixes as $suffix) {
            $original_key = $original_name . $suffix;
            $duplicate_key = $duplicate_name . $suffix;

            if (isset($options->{$original_key})) {
                // Copy the entire setting structure
                $options->{$duplicate_key} = $this->deepCopy($options->{$original_key});
                $copied_settings[] = $suffix;

                error_log("Copied setting: $original_key -> $duplicate_key");
                error_log("Value: " . print_r($options->{$duplicate_key}, true));

                // Ensure the duplicate is enabled for the current post type
                if ($suffix === '_rule' && is_object($options->{$duplicate_key})) {
                    // Always enable the duplicate as 'recommended' (copy the original status)
                    $original_status = $options->{$duplicate_key}->{$post_type} ?? 'undefined';
                    if (!isset($options->{$duplicate_key}->{$post_type}) ||
                        $options->{$duplicate_key}->{$post_type} === 'disabled' ||
                        $options->{$duplicate_key}->{$post_type} === 'off') {
                        // For disabled/off originals, set to recommended
                        $options->{$duplicate_key}->{$post_type} = 'recommended';
                        error_log("Set rule for post type $post_type to 'recommended' (was: $original_status)");
                    } else {
                        // For enabled originals (block/recommended), keep the same status
                        error_log("Keeping original status '$original_status' for post type $post_type");
                    }
                }
            } else {
                $missing_settings[] = $suffix;
                error_log("Missing original setting: $original_key");
            }
        }

        error_log("Copied settings: " . implode(', ', $copied_settings));
        error_log("Missing settings: " . implode(', ', $missing_settings));

        // Also copy any post-type specific settings (but avoid duplicating duplicates)
        // Create a snapshot of keys to avoid infinite loop when adding new keys
        $existing_keys = array_keys((array) $options);
        foreach ($existing_keys as $key) {
            // Only copy if it starts with original name but is NOT a duplicate
            if (strpos($key, $original_name . '_') === 0 && strpos($key, '_duplicate_') === false) {
                $suffix = str_replace($original_name . '_', '', $key);
                $duplicate_key = $duplicate_name . '_' . $suffix;

                if (!isset($options->{$duplicate_key})) {
                    $options->{$duplicate_key} = $this->deepCopy($options->{$key});
                    error_log("Copied additional setting: $key -> $duplicate_key");
                }
            }
        }
    }

    /**
     * Set the title for the duplicate requirement
     *
     * @param object $options
     * @param string $duplicate_name
     * @param string $custom_name
     * @param string $post_type
     */
    private function setDuplicateTitle($options, $duplicate_name, $custom_name, $post_type)
    {
        $title_key = $duplicate_name . '_title';

        // If no custom name provided, generate numbered title
        if (empty($custom_name)) {
            $original_name = $this->getOriginalNameFromDuplicate($duplicate_name);
            $duplicate_number = $this->extractDuplicateNumber($duplicate_name);

            // Get the original requirement's title if available
            $original_title_key = $original_name . '_title';
            $original_title = '';

            if (isset($options->{$original_title_key}) && isset($options->{$original_title_key}->{$post_type})) {
                $original_title = $options->{$original_title_key}->{$post_type};
            } else {
                // Fallback to formatted name
                $original_title = ucwords(str_replace('_', ' ', $original_name));
            }

            $custom_name = $original_title . ' (' . $duplicate_number . ')';
        }

        // Set title for the specific post type
        if (!isset($options->{$title_key})) {
            $options->{$title_key} = new \stdClass();
        }

        if (!is_object($options->{$title_key})) {
            $options->{$title_key} = new \stdClass();
        }

        $options->{$title_key}->{$post_type} = $custom_name;
    }

    /**
     * Extract the duplicate number from a duplicate name
     *
     * @param string $duplicate_name
     * @return string
     */
    private function extractDuplicateNumber($duplicate_name)
    {
        // Extract the number from the duplicate name
        // Format: original_name_duplicate_2
        $parts = explode('_duplicate_', $duplicate_name);
        if (isset($parts[1])) {
            return $parts[1];
        }

        return '2'; // Default fallback
    }

    /**
     * Store metadata about the duplicate relationship
     *
     * @param object $options
     * @param string $duplicate_name
     * @param string $original_name
     * @param string $custom_name
     */
    private function storeDuplicateMetadata($options, $duplicate_name, $original_name, $custom_name)
    {
        $metadata_key = $duplicate_name . '_duplicate_meta';
        $options->{$metadata_key} = [
            'original_requirement' => $original_name,
            'custom_name' => $custom_name,
            'created_at' => current_time('mysql'),
            'created_by' => get_current_user_id()
        ];
    }

    /**
     * Create a dynamic requirement class for the duplicate
     *
     * @param string $original_name
     * @param string $duplicate_name
     */
    private function createDynamicRequirementClass($original_name, $duplicate_name)
    {
        error_log("=== CREATE DYNAMIC CLASS START ===");
        error_log("Original: $original_name, Duplicate: $duplicate_name");

        // Get the original requirement class
        $original_class = $this->getOriginalRequirementClass($original_name);
        error_log("Original class found: " . ($original_class ? $original_class : 'NOT FOUND'));

        if (!$original_class) {
            error_log("ERROR: No original class found for $original_name");
            return;
        }

        // Create dynamic class name
        $class_name = 'DuplicateRequirement_' . str_replace(['_', '-'], '', ucwords($duplicate_name, '_-'));
        error_log("Generated class name: $class_name");

        // Store the class mapping for later use
        error_log("Storing class mapping...");
        $this->storeDynamicClassMapping($duplicate_name, $class_name, $original_class);
        error_log("=== CREATE DYNAMIC CLASS END ===");
    }

    /**
     * Get the original requirement class name
     *
     * @param string $requirement_name
     * @return string|false
     */
    private function getOriginalRequirementClass($requirement_name)
    {
        // Map of requirement names to their classes
        $class_mapping = [
            'approved_by' => '\\PublishPress\\Checklists\\Core\\Requirement\\Approved_by',
            'categories_count' => '\\PublishPress\\Checklists\\Core\\Requirement\\Categories_count',
            'external_links' => '\\PublishPress\\Checklists\\Core\\Requirement\\External_links',
            'featured_image_alt' => '\\PublishPress\\Checklists\\Core\\Requirement\\Featured_image_alt',
            'featured_image_caption' => '\\PublishPress\\Checklists\\Core\\Requirement\\Featured_image_caption',
            'featured_image' => '\\PublishPress\\Checklists\\Core\\Requirement\\Featured_image',
            'filled_excerpt' => '\\PublishPress\\Checklists\\Core\\Requirement\\Filled_excerpt',
            'image_alt_count' => '\\PublishPress\\Checklists\\Core\\Requirement\\Image_alt_count',
            'image_alt' => '\\PublishPress\\Checklists\\Core\\Requirement\\Image_alt',
            'prohibited_categories' => '\\PublishPress\\Checklists\\Core\\Requirement\\Prohibited_categories',
            'prohibited_tags' => '\\PublishPress\\Checklists\\Core\\Requirement\\Prohibited_tags',
            'required_categories' => '\\PublishPress\\Checklists\\Core\\Requirement\\Required_categories',
            'required_tags' => '\\PublishPress\\Checklists\\Core\\Requirement\\Required_tags',
            'tags_count' => '\\PublishPress\\Checklists\\Core\\Requirement\\Tags_count',
            'title_count' => '\\PublishPress\\Checklists\\Core\\Requirement\\Title_count',
            'validate_links' => '\\PublishPress\\Checklists\\Core\\Requirement\\Validate_links',
            'words_count' => '\\PublishPress\\Checklists\\Core\\Requirement\\Words_count',
            
            // Pro requirements
            'heading_in_hierarchy' => '\\PublishPress\\ChecklistsPro\\Accessibility\\Requirement\\HeadingInHierarchy',
            'single_h1_per_page' => '\\PublishPress\\ChecklistsPro\\Accessibility\\Requirement\\SingleH1PerPage',
            'table_header' => '\\PublishPress\\ChecklistsPro\\Accessibility\\Requirement\\TableHeader',
            'all_in_one_seo_headline_score' => '\\PublishPress\\ChecklistsPro\\AllInOneSEO\\Requirement\\AllInOneSEOHeadlineScore',
            'all_in_one_seo_score' => '\\PublishPress\\ChecklistsPro\\AllInOneSEO\\Requirement\\AllInOneSEOScore',
            'approved_by_user' => '\\PublishPress\\ChecklistsPro\\Approval\\Requirement\\ApprovedByUser',
            'featured_image_height' => '\\PublishPress\\ChecklistsPro\\FeaturedImage\\Requirement\\FeaturedImageHeight',
            'featured_image_width' => '\\PublishPress\\ChecklistsPro\\FeaturedImage\\Requirement\\FeaturedImageWidth',
            'image_count' => '\\PublishPress\\ChecklistsPro\\Images\\Requirement\\ImageCount',
            'no_heading_tags' => '\\PublishPress\\ChecklistsPro\\Content\\Requirement\\NoHeadingTags',
            'publish_time_exact' => '\\PublishPress\\ChecklistsPro\\PublishDateTime\\Requirement\\PublishTimeExact',
            'publish_time_future' => '\\PublishPress\\ChecklistsPro\\PublishDateTime\\Requirement\\PublishTimeFuture',
            'rank_math_score' => '\\PublishPress\\ChecklistsPro\\RankMath\\Requirement\\RankMathScore',
            
        ];

        return isset($class_mapping[$requirement_name]) ? $class_mapping[$requirement_name] : false;
    }

    /**
     * Store dynamic class mapping for requirement registration
     *
     * @param string $duplicate_name
     * @param string $class_name
     * @param string $original_class
     */
    private function storeDynamicClassMapping($duplicate_name, $class_name, $original_class)
    {
        error_log("=== STORE CLASS MAPPING START ===");
        error_log("Duplicate: $duplicate_name");
        error_log("Class: $class_name");
        error_log("Original: $original_class");

        $mappings = get_option('ppc_duplicate_class_mappings', []);
        error_log("Existing mappings count: " . count($mappings));

        $mappings[$duplicate_name] = [
            'class_name' => $class_name,
            'original_class' => $original_class,
            'created_at' => current_time('mysql')
        ];

        $save_result = update_option('ppc_duplicate_class_mappings', $mappings);
        error_log("Mapping save result: " . ($save_result ? 'SUCCESS' : 'FAILED'));
        error_log("New mappings count: " . count($mappings));
        error_log("=== STORE CLASS MAPPING END ===");
    }

    /**
     * Get duplicated requirements for a specific post type
     *
     * @param string $post_type
     * @return array Array of requirement class names
     */
    public function getDuplicatedRequirements($post_type)
    {
        error_log("=== GET DUPLICATED REQUIREMENTS START ===");
        error_log("Post type: $post_type");

        $duplicated_requirements = [];
        $options = get_option('publishpress_checklists_checklists_options', new \stdClass());
        $class_mappings = get_option('ppc_duplicate_class_mappings', []);

        error_log("Options loaded: " . (is_object($options) ? 'YES' : 'NO'));
        error_log("Class mappings count: " . count($class_mappings));

        if (!is_object($options)) {
            error_log("ERROR: Options not loaded properly");
            return $duplicated_requirements;
        }

        // Find all duplicate requirements for this post type
        foreach ($options as $key => $value) {
            if (strpos($key, '_duplicate_') !== false && strpos($key, '_rule') !== false) {
                $requirement_name = str_replace('_rule', '', $key);
                error_log("Found duplicate rule: $key -> requirement: $requirement_name");

                // Check if this requirement is enabled for the post type
                if (isset($value[$post_type])) {
                    $status = $value[$post_type];
                    error_log("Status for $post_type: $status");

                    if ($status !== 'disabled' && $status !== 'off') {
                        error_log("Requirement $requirement_name is enabled, creating dynamic class...");

                        // Create dynamic class for this duplicate
                        $class_name = $this->createDynamicClass($requirement_name, $class_mappings);
                        if ($class_name) {
                            $duplicated_requirements[] = $class_name;
                            error_log("Added class to requirements: $class_name");
                        } else {
                            error_log("ERROR: Failed to create dynamic class for $requirement_name");
                        }
                    } else {
                        error_log("Requirement $requirement_name is disabled/off");
                    }
                } else {
                    error_log("No status found for post type $post_type in requirement $requirement_name");
                }
            }
        }

        error_log("Total duplicated requirements found: " . count($duplicated_requirements));
        error_log("Requirements: " . implode(', ', $duplicated_requirements));
        error_log("=== GET DUPLICATED REQUIREMENTS END ===");

        return $duplicated_requirements;
    }

    /**
     * Create a dynamic class for a duplicate requirement
     *
     * @param string $duplicate_name
     * @param array $class_mappings
     * @return string|false
     */
    private function createDynamicClass($duplicate_name, $class_mappings)
    {
        if (!isset($class_mappings[$duplicate_name])) {
            return false;
        }

        $mapping = $class_mappings[$duplicate_name];
        $original_class = $mapping['original_class'];
        $dynamic_class_name = $mapping['class_name'];

        // Check if class already exists
        if (class_exists($dynamic_class_name)) {
            return $dynamic_class_name;
        }

        // Create the dynamic class
        $class_code = $this->generateDynamicClassCode($dynamic_class_name, $original_class, $duplicate_name);
        
        try {
            eval($class_code);
            return $dynamic_class_name;
        } catch (\Exception $e) {
            error_log('Error creating dynamic class: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Generate PHP code for a dynamic requirement class
     *
     * @param string $class_name
     * @param string $original_class
     * @param string $duplicate_name
     * @return string
     */
    private function generateDynamicClassCode($class_name, $original_class, $duplicate_name)
    {
        $original_name = $this->getOriginalNameFromDuplicate($duplicate_name);

        // Get the group from the original class
        $group = $this->getOriginalRequirementGroup($original_name);

        // Generate a unique position for the duplicate
        $unique_position = $this->generateUniquePosition($duplicate_name);

        // Debug log
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("Generated position {$unique_position} for duplicate {$duplicate_name}");
        }

        return "
        class {$class_name} extends {$original_class}
        {
            public \$name = '{$duplicate_name}';
            public \$group = '{$group}';
            public \$position = {$unique_position};

            public function __construct(\$module, \$post_type)
            {
                parent::__construct(\$module, \$post_type);
                \$this->name = '{$duplicate_name}';
                \$this->group = '{$group}';
                \$this->position = {$unique_position};
            }

            public function init_language()
            {
                parent::init_language();

                // Add numbered duplicate indicator to labels
                \$duplicate_number = \$this->extractDuplicateNumber('{$duplicate_name}');
                \$suffix = ' (' . \$duplicate_number . ')';

                if (isset(\$this->lang['label_settings'])) {
                    \$this->lang['label_settings'] .= \$suffix;
                }
                if (isset(\$this->lang['label'])) {
                    \$this->lang['label'] .= \$suffix;
                }
            }

            private function extractDuplicateNumber(\$duplicate_name)
            {
                \$parts = explode('_duplicate_', \$duplicate_name);
                return isset(\$parts[1]) ? \$parts[1] : '2';
            }
        }";
    }

    /**
     * Get original requirement name from duplicate name
     *
     * @param string $duplicate_name
     * @return string
     */
    private function getOriginalNameFromDuplicate($duplicate_name)
    {
        $parts = explode('_duplicate_', $duplicate_name);
        return isset($parts[0]) ? $parts[0] : $duplicate_name;
    }

    /**
     * Generate a unique position for a duplicate requirement
     *
     * @param string $duplicate_name
     * @return int
     */
    private function generateUniquePosition($duplicate_name)
    {
        // Get the original requirement's position
        $original_name = $this->getOriginalNameFromDuplicate($duplicate_name);
        $original_position = $this->getOriginalRequirementPosition($original_name);

        // Generate a unique position based on the duplicate name hash
        // This ensures duplicates have different positions but are still grouped near the original
        $hash = crc32($duplicate_name);
        $offset = abs($hash % 1000) + 1; // Generate offset between 1-1000

        return $original_position + $offset;
    }

    /**
     * Get the group for an original requirement
     *
     * @param string $requirement_name
     * @return string
     */
    private function getOriginalRequirementGroup($requirement_name)
    {
        // Map of requirement names to their groups
        $group_mapping = [
            'approved_by' => 'approval',
            'categories_count' => 'categories',
            'external_links' => 'links',
            'featured_image_alt' => 'featured_image',
            'featured_image_caption' => 'featured_image',
            'featured_image' => 'featured_image',
            'filled_excerpt' => 'content',
            'image_alt_count' => 'images',
            'image_alt' => 'images',
            'prohibited_categories' => 'categories',
            'prohibited_tags' => 'tags',
            'required_categories' => 'categories',
            'required_tags' => 'tags',
            'tags_count' => 'tags',
            'title_count' => 'title',
            'validate_links' => 'links',
            'words_count' => 'content',
            
            // Pro requirements
            'heading_in_hierarchy' => 'accessibility',
            'single_h1_per_page' => 'accessibility',
            'table_header' => 'accessibility',
            'all_in_one_seo_headline_score' => 'all_in_one_seo',
            'all_in_one_seo_score' => 'all_in_one_seo',
            'approved_by_user' => 'approval',
            'featured_image_height' => 'featured_image',
            'featured_image_width' => 'featured_image',
            'image_count' => 'images',
            'no_heading_tags' => 'content',
            'publish_time_exact' => 'publish_date_time',
            'publish_time_future' => 'publish_date_time',
            'rank_math_score' => 'rank_math',
        ];

        return isset($group_mapping[$requirement_name]) ? $group_mapping[$requirement_name] : 'general';
    }

    /**
     * Get the position for an original requirement
     *
     * @param string $requirement_name
     * @return int
     */
    private function getOriginalRequirementPosition($requirement_name)
    {
        // Map of requirement names to their positions
        $position_mapping = [
            'approved_by' => 170,
            'categories_count' => 30,
            'external_links' => 110,
            'featured_image_alt' => 105,
            'featured_image_caption' => 106,
            'featured_image' => 102,
            'filled_excerpt' => 90,
            'image_alt_count' => 135,
            'image_alt' => 130,
            'prohibited_categories' => 50,
            'prohibited_tags' => 80,
            'required_categories' => 40,
            'required_tags' => 70,
            'tags_count' => 60,
            'title_count' => 10,
            'validate_links' => 120,
            'words_count' => 20,
            
            // Pro requirements
            'heading_in_hierarchy' => 142,
            'single_h1_per_page' => 143,
            'table_header' => 140,
            'all_in_one_seo_headline_score' => 150,
            'all_in_one_seo_score' => 149,
            'approved_by_user' => 171,
            'featured_image_height' => 104,
            'featured_image_width' => 103,
            'image_count' => 101,
            'no_heading_tags' => 108,
            'publish_time_exact' => 161,
            'publish_time_future' => 107,
            'rank_math_score' => 141,
            // Add more as needed
        ];

        return isset($position_mapping[$requirement_name]) ? $position_mapping[$requirement_name] : 1000;
    }

    /**
     * Delete a duplicate requirement
     *
     * @param string $duplicate_name The duplicate requirement name
     * @return array Result array with success status and data
     */
    public function deleteDuplicateRequirement($duplicate_name)
    {
        try {
            // Get current options
            $options = get_option('publishpress_checklists_checklists_options', new \stdClass());
            if (!is_object($options)) {
                $options = new \stdClass();
            }

            // Remove all settings for this duplicate
            $this->removeDuplicateSettings($options, $duplicate_name);

            // Save the updated options
            update_option('publishpress_checklists_checklists_options', $options);

            // Remove from class mappings
            $mappings = get_option('ppc_duplicate_class_mappings', []);
            if (isset($mappings[$duplicate_name])) {
                unset($mappings[$duplicate_name]);
                update_option('ppc_duplicate_class_mappings', $mappings);
            }

            return [
                'success' => true,
                'message' => 'Duplicate requirement deleted successfully'
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Error deleting duplicate requirement: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Rename a duplicate requirement
     *
     * @param string $old_name The current duplicate name
     * @param string $new_display_name The new display name
     * @return array Result array with success status and data
     */
    public function renameDuplicateRequirement($old_name, $new_display_name)
    {
        try {
            // Get current options
            $options = get_option('publishpress_checklists_checklists_options', new \stdClass());
            if (!is_object($options)) {
                $options = new \stdClass();
            }

            // Update the title/display name for all post types
            $title_key = $old_name . '_title';
            if (isset($options->{$title_key})) {
                foreach ($options->{$title_key} as $post_type => $current_title) {
                    $options->{$title_key}[$post_type] = $new_display_name;
                }
            }

            // Update metadata
            $metadata_key = $old_name . '_duplicate_meta';
            if (isset($options->{$metadata_key})) {
                $options->{$metadata_key}['custom_name'] = $new_display_name;
                $options->{$metadata_key}['updated_at'] = current_time('mysql');
            }

            // Save the updated options
            update_option('publishpress_checklists_checklists_options', $options);

            return [
                'success' => true,
                'new_name' => $new_display_name,
                'message' => 'Duplicate requirement renamed successfully'
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Error renaming duplicate requirement: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Remove all settings for a duplicate requirement
     *
     * @param object $options
     * @param string $duplicate_name
     */
    private function removeDuplicateSettings($options, $duplicate_name)
    {
        // Define setting suffixes to remove
        $setting_suffixes = [
            '_rule',
            '_min',
            '_max',
            '_multiple',
            '_can_ignore',
            '_editable_by',
            '_title',
            '_users',
            '_roles',
            '_field_key',
            '_taxonomy',
            '_terms',
            '_duplicate_meta'
        ];

        foreach ($setting_suffixes as $suffix) {
            $key = $duplicate_name . $suffix;
            if (isset($options->{$key})) {
                unset($options->{$key});
            }
        }

        // Also remove any other settings that start with the duplicate name
        // Create a snapshot of keys to avoid issues when unsetting
        $existing_keys = array_keys((array) $options);
        foreach ($existing_keys as $key) {
            if (strpos($key, $duplicate_name . '_') === 0) {
                unset($options->{$key});
            }
        }
    }

    /**
     * Debug method to check what was created
     *
     * @param string $duplicate_name
     * @return array
     */
    public function debugDuplicate($duplicate_name)
    {
        $options = get_option('publishpress_checklists_checklists_options', new \stdClass());
        $mappings = get_option('ppc_duplicate_class_mappings', []);

        $debug_info = [
            'duplicate_name' => $duplicate_name,
            'options_found' => [],
            'class_mapping' => isset($mappings[$duplicate_name]) ? $mappings[$duplicate_name] : null,
        ];

        // Find all options related to this duplicate
        $existing_keys = array_keys((array) $options);
        foreach ($existing_keys as $key) {
            if (strpos($key, $duplicate_name) === 0) {
                $debug_info['options_found'][$key] = $options->{$key};
            }
        }

        return $debug_info;
    }

    /**
     * Clean up corrupted duplicate entries from the database
     *
     * @return array Result array with success status and data
     */
    public function cleanupCorruptedDuplicates()
    {
        try {
            $options = get_option('publishpress_checklists_checklists_options', new \stdClass());
            if (!is_object($options)) {
                return ['success' => false, 'message' => 'No options found'];
            }

            $cleaned_keys = [];
            $existing_keys = array_keys((array) $options);

            foreach ($existing_keys as $key) {
                // Find keys with multiple _duplicate_ patterns (corrupted)
                if (substr_count($key, '_duplicate_') > 1) {
                    unset($options->{$key});
                    $cleaned_keys[] = $key;
                    error_log("Cleaned corrupted key: $key");
                }
            }

            // Save cleaned options
            update_option('publishpress_checklists_checklists_options', $options);

            // Also clean up class mappings
            $mappings = get_option('ppc_duplicate_class_mappings', []);
            $cleaned_mappings = [];
            foreach ($mappings as $name => $mapping) {
                if (substr_count($name, '_duplicate_') <= 1) {
                    $cleaned_mappings[$name] = $mapping;
                }
            }
            update_option('ppc_duplicate_class_mappings', $cleaned_mappings);

            return [
                'success' => true,
                'message' => 'Cleaned up ' . count($cleaned_keys) . ' corrupted entries',
                'cleaned_keys' => $cleaned_keys
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Error cleaning up: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Deep copy an object or array with recursion protection
     *
     * @param mixed $data
     * @param int $depth Current recursion depth
     * @param int $max_depth Maximum allowed recursion depth
     * @return mixed
     */
    private function deepCopy($data, $depth = 0, $max_depth = 10)
    {
        // Prevent infinite recursion
        if ($depth > $max_depth) {
            return $data;
        }

        if (is_object($data)) {
            return clone $data;
        } elseif (is_array($data)) {
            $result = [];
            foreach ($data as $key => $value) {
                $result[$key] = $this->deepCopy($value, $depth + 1, $max_depth);
            }
            return $result;
        } else {
            return $data;
        }
    }
}
