<?php
/**
 * Simple test script for duplicate functionality
 * This file can be used to test the duplicate functionality manually
 * 
 * To use: Add this to your WordPress admin and call the test functions
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Test the duplicate functionality
 */
function test_duplicate_functionality() {
    echo "<h2>Testing Duplicate Checklist Functionality</h2>";
    
    // Test 1: Check if DuplicateHandler class exists
    echo "<h3>Test 1: Class Loading</h3>";
    if (class_exists('\\PublishPress\\ChecklistsPro\\DuplicateChecklist\\DuplicateHandler')) {
        echo "✅ DuplicateHandler class loaded successfully<br>";
    } else {
        echo "❌ DuplicateHandler class not found<br>";
        return;
    }
    
    // Test 2: Create handler instance
    echo "<h3>Test 2: Handler Instantiation</h3>";
    try {
        $handler = new \PublishPress\ChecklistsPro\DuplicateChecklist\DuplicateHandler();
        echo "✅ DuplicateHandler instantiated successfully<br>";
    } catch (Exception $e) {
        echo "❌ Error instantiating DuplicateHandler: " . $e->getMessage() . "<br>";
        return;
    }
    
    // Test 3: Check current options structure
    echo "<h3>Test 3: Options Structure</h3>";
    $options = get_option('publishpress_checklists_checklists_options', new stdClass());
    if (is_object($options)) {
        echo "✅ Options loaded as object<br>";
        
        // Count existing requirements
        $requirement_count = 0;
        foreach ($options as $key => $value) {
            if (strpos($key, '_rule') !== false) {
                $requirement_count++;
            }
        }
        echo "📊 Found {$requirement_count} existing requirements<br>";
    } else {
        echo "❌ Options not loaded properly<br>";
    }
    
    // Test 4: Test duplication (dry run)
    echo "<h3>Test 4: Duplication Test (Dry Run)</h3>";
    
    // Check if title_count exists
    if (isset($options->title_count_rule)) {
        echo "✅ title_count requirement found, ready for duplication test<br>";
        
        // Test the duplication logic without actually saving
        try {
            $result = $handler->duplicateRequirement('title_count', 'post', 'test_duplicate');
            if ($result['success']) {
                echo "✅ Duplication test successful<br>";
                echo "📝 Generated duplicate name: " . $result['duplicate_name'] . "<br>";
                
                // Clean up the test duplicate
                $handler->deleteDuplicateRequirement($result['duplicate_name']);
                echo "🧹 Test duplicate cleaned up<br>";
            } else {
                echo "❌ Duplication test failed: " . $result['message'] . "<br>";
            }
        } catch (Exception $e) {
            echo "❌ Error during duplication test: " . $e->getMessage() . "<br>";
        }
    } else {
        echo "⚠️ title_count requirement not found, skipping duplication test<br>";
    }
    
    // Test 5: Check JavaScript files
    echo "<h3>Test 5: Asset Files</h3>";
    $js_file = __DIR__ . '/assets/js/duplicate-checklist.js';
    $css_file = __DIR__ . '/assets/css/duplicate-checklist.css';
    $validation_file = __DIR__ . '/assets/js/validation-manager.js';
    
    if (file_exists($js_file)) {
        echo "✅ JavaScript file exists<br>";
    } else {
        echo "❌ JavaScript file missing<br>";
    }
    
    if (file_exists($css_file)) {
        echo "✅ CSS file exists<br>";
    } else {
        echo "❌ CSS file missing<br>";
    }
    
    if (file_exists($validation_file)) {
        echo "✅ Validation manager file exists<br>";
    } else {
        echo "❌ Validation manager file missing<br>";
    }
    
    // Test 6: Check hooks registration
    echo "<h3>Test 6: Hooks Registration</h3>";
    if (has_action('publishpress_checklists_tasks_list_th')) {
        echo "✅ Header hook registered<br>";
    } else {
        echo "❌ Header hook not registered<br>";
    }
    
    if (has_action('publishpress_checklists_tasks_list_td')) {
        echo "✅ Cell hook registered<br>";
    } else {
        echo "❌ Cell hook not registered<br>";
    }
    
    if (has_action('wp_ajax_ppc_duplicate_requirement')) {
        echo "✅ AJAX duplicate hook registered<br>";
    } else {
        echo "❌ AJAX duplicate hook not registered<br>";
    }
    
    echo "<h3>Test Summary</h3>";
    echo "🎉 Duplicate functionality testing completed!<br>";
    echo "📋 Check the results above for any issues that need to be addressed.<br>";
}

/**
 * Display current duplicate requirements
 */
function display_current_duplicates() {
    echo "<h2>Current Duplicate Requirements</h2>";
    
    $options = get_option('publishpress_checklists_checklists_options', new stdClass());
    $duplicates = [];
    
    foreach ($options as $key => $value) {
        if (strpos($key, '_duplicate_') !== false && strpos($key, '_rule') !== false) {
            $requirement_name = str_replace('_rule', '', $key);
            $duplicates[] = $requirement_name;
        }
    }
    
    if (empty($duplicates)) {
        echo "📝 No duplicate requirements found.<br>";
    } else {
        echo "<ul>";
        foreach ($duplicates as $duplicate) {
            echo "<li>🔄 {$duplicate}</li>";
        }
        echo "</ul>";
    }
}

// Add admin page for testing (only for administrators)
if (current_user_can('manage_options')) {
    add_action('admin_menu', function() {
        add_submenu_page(
            'tools.php',
            'Test Duplicate Functionality',
            'Test Duplicates',
            'manage_options',
            'test-duplicate-checklist',
            function() {
                echo '<div class="wrap">';
                echo '<h1>Duplicate Checklist Testing</h1>';
                
                if (isset($_GET['run_test'])) {
                    test_duplicate_functionality();
                    echo '<hr>';
                }
                
                display_current_duplicates();
                
                echo '<p><a href="' . admin_url('tools.php?page=test-duplicate-checklist&run_test=1') . '" class="button button-primary">Run Tests</a></p>';
                echo '</div>';
            }
        );
    });
}
