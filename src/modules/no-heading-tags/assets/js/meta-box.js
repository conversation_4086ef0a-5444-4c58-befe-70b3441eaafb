(function ($, document, PP_Checklists) {
  'use strict';

  $(function () {
    if ($('#pp-checklists-req-no_heading_tags').length > 0) {
      if (PP_Checklists.is_gutenberg_active()) {
        /**
         * For Gutenberg
         */
        wp.data.subscribe(function () {
          var content = PP_Checklists.getEditor().getEditedPostAttribute('content');

          if (typeof content == 'undefined') {
            return;
          }

          var disallowedHeadingTags = objectL10n_checklist_requirements.requirements.no_heading_tags.value;
          var status = true;

          if (disallowedHeadingTags && disallowedHeadingTags.length > 0) {
            // Check for disallowed heading tags in the content
            for (var i = 0; i < disallowedHeadingTags.length; i++) {
              var headingTag = disallowedHeadingTags[i];
              // More comprehensive regex that handles various heading formats
              var pattern = new RegExp('<' + headingTag + '\\b[^>]*>.*?<\\/' + headingTag + '>', 'is');

              if (pattern.test(content)) {
                status = false;
                // Don't break here, we need to check all tags to mark blocks
              }
            }

            // Check blocks for disallowed heading tags and add warnings
            if (wp.data.select('core/block-editor')) {
              const blocks = wp.data.select('core/block-editor').getBlocks();

              blocks.forEach(block => {
                let hasDisallowedHeading = false;

                // Check if this is a heading block with a disallowed level
                if (block.name === 'core/heading') {
                  const headingLevel = block.attributes.level;
                  const headingTag = 'h' + headingLevel;

                  if (disallowedHeadingTags.includes(headingTag)) {
                    hasDisallowedHeading = true;
                  }
                }
                // Also check block content for HTML headings (for blocks that might contain HTML)
                else {
                  const blockContent = block.attributes.content || '';

                  // Check if this block contains any disallowed heading tags
                  for (var i = 0; i < disallowedHeadingTags.length; i++) {
                    var headingTag = disallowedHeadingTags[i];
                    // More comprehensive regex that handles various heading formats
                    var pattern = new RegExp('<' + headingTag + '\\b[^>]*>.*?<\\/' + headingTag + '>', 'is');

                    if (pattern.test(blockContent)) {
                      hasDisallowedHeading = true;
                      break;
                    }
                  }
                }

                // Set warning attribute on the list view item
                const listViewElement = document.querySelector(
                  `.block-editor-list-view-leaf[data-block="${block.clientId}"]`
                );
                if (listViewElement) {
                  listViewElement.setAttribute('data-warning', hasDisallowedHeading);
                }
              });
            }
          }

          $('#pp-checklists-req-no_heading_tags').trigger(
            PP_Checklists.EVENT_UPDATE_REQUIREMENT_STATE,
            status
          );
        });
      } else {
        /**
         * For the Classic Editor
         */
        var $content = $('#content');
        var editor;

        /**
         * Check for prohibited heading tags and update the status of the requirement
         */
        function update() {
          var text;

          if (typeof editor == 'undefined' || !editor || editor.isHidden()) {
            // For the text tab.
            text = $content.val();
          } else {
            // For the editor tab.
            text = editor.getContent({ format: 'raw' });
          }

          var disallowedHeadingTags = objectL10n_checklist_requirements.requirements.no_heading_tags.value;
          var status = true;
          console.log("disallowedHeadingTags", disallowedHeadingTags);


          if (disallowedHeadingTags && disallowedHeadingTags.length > 0) {
            for (var i = 0; i < disallowedHeadingTags.length; i++) {
              var headingTag = disallowedHeadingTags[i];
              // More comprehensive regex that handles various heading formats
              var pattern = new RegExp('<' + headingTag + '\\b[^>]*>.*?<\\/' + headingTag + '>', 'is');

              if (pattern.test(text)) {
                status = false;
                break;
              }

            }
          }

          $('#pp-checklists-req-no_heading_tags').trigger(
            PP_Checklists.EVENT_UPDATE_REQUIREMENT_STATE,
            status
          );
        }

        // For the editor.
        $(document).on(PP_Checklists.EVENT_TINYMCE_LOADED, function (event, tinymce) {
          editor = tinymce.editors['content'];

          if (typeof editor !== 'undefined') {
            editor.onInit.add(function () {
              /**
               * Bind the update triggers.
               *
               * When a node change in the main TinyMCE editor has been triggered.
               * When a key has been released in the plain text content editor.
               */

              if (editor.id !== 'content') {
                return;
              }

              editor.on('nodechange keyup', _.debounce(update, 200));
              
            });
          }
        });

        $content.on('input keyup', _.debounce(update, 200));
        update();
      }
    }
  });
})(jQuery, document, PP_Checklists);
