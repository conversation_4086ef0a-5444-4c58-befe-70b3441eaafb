(function ($, document, PP_Checklists) {
  'use strict';

  $(function () {
    var lastCount = 0;

    if ($('#pp-checklists-req-image_count').length > 0) {
      if (PP_Checklists.is_gutenberg_active()) {
        /**
         * For Gutenberg
         */
        wp.data.subscribe(function () {
          var content = PP_Checklists.getEditor().getEditedPostAttribute('content');

          if (typeof content == 'undefined') {
            return;
          }
          const imgRegex = /<img[^>]+src\s*=\s*['"]([^'"]+)['"][^>]*>/gi;
          const matches = content.match(imgRegex);

          var count = matches ? matches.length : 0;

          if (lastCount == count) {
            return;
          }

          var min = parseInt(objectL10n_checklist_requirements.requirements.image_count.value[0]),
            max = parseInt(objectL10n_checklist_requirements.requirements.image_count.value[1]);

          $('#pp-checklists-req-image_count').trigger(
            PP_Checklists.EVENT_UPDATE_REQUIREMENT_STATE,
            PP_Checklists.check_valid_quantity(count, min, max),
          );

          lastCount = count;
        });
      } else {
        /**
         * For the Classic Editor
         */
        var $content = $('#content');
        var lastCount = 0;
        var editor;

        /**
         * Get the words count from TinyMCE and update the status of the requirement
         */
        function update() {
          var text, count;

          if (typeof editor == 'undefined' || !editor || editor.isHidden()) {
            // For the text tab.
            text = $content.val();
          } else {
            // For the editor tab.
            text = editor.getContent({ format: 'raw' });
          }

          const imgRegex = /<img[^>]+src\s*=\s*['"]([^'"]+)['"][^>]*>/gi;
          const matches = text.match(imgRegex);

          count = matches ? matches.length : 0;

          if (lastCount === count) {
            return;
          }

          var min = parseInt(objectL10n_checklist_requirements.requirements.image_count.value[0]),
            max = parseInt(objectL10n_checklist_requirements.requirements.image_count.value[1]);

          $('#pp-checklists-req-image_count').trigger(
            PP_Checklists.EVENT_UPDATE_REQUIREMENT_STATE,
            PP_Checklists.check_valid_quantity(count, min, max),
          );

          lastCount = count;
        }

        // For the editor.
        $(document).on(PP_Checklists.EVENT_TINYMCE_LOADED, function (event, tinymce) {
          editor = tinymce.editors['content'];

          if (typeof editor !== 'undefined') {
            editor.onInit.add(function () {
              /**
               * Bind the words count update triggers.
               *
               * When a node change in the main TinyMCE editor has been triggered.
               * When a key has been released in the plain text content editor.
               */

              if (editor.id !== 'content') {
                return;
              }

              editor.on('nodechange keyup', _.debounce(update, 200));
            });
          }
        });

        $content.on('input keyup', _.debounce(update, 200));
        update();
      }
    }
  });
})(jQuery, document, PP_Checklists);
